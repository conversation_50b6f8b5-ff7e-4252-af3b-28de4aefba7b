import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { IncentiveValidityService, CreateValidityConfigDto, UpdateValidityConfigDto } from './incentive-validity.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

export class CalculateValidityDto {
  startDate: string; // ISO date string
  industryId: string;
  categoryId?: string;
  campaignType?: string;
}

export class ValidateValidityDto {
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  industryId: string;
  categoryId?: string;
  campaignType?: string;
}

@ApiTags('Incentive Validity')
@Controller('incentive-validity')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class IncentiveValidityController {
  constructor(private readonly incentiveValidityService: IncentiveValidityService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Criar configuração de validade de incentivos' })
  @ApiResponse({ status: 201, description: 'Configuração criada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 409, description: 'Configuração já existe' })
  async create(@Body() createDto: CreateValidityConfigDto, @Request() req) {
    return this.incentiveValidityService.createConfig(createDto, req.user.sub);
  }

  @Get()
  @ApiOperation({ summary: 'Listar configurações de validade' })
  @ApiQuery({ name: 'industryId', required: false, description: 'Filtrar por indústria' })
  @ApiResponse({ status: 200, description: 'Lista de configurações' })
  async findAll(@Query('industryId') industryId?: string) {
    return this.incentiveValidityService.findAll(industryId);
  }

  @Get('timezone')
  @ApiOperation({ summary: 'Obter informações do timezone padrão (America/Sao_Paulo)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Informações do timezone',
    schema: {
      type: 'object',
      properties: {
        timezone: { type: 'string' },
        currentTime: { type: 'string' },
        offset: { type: 'string' }
      }
    }
  })
  async getTimezoneInfo() {
    return this.incentiveValidityService.getTimezoneInfo();
  }

  @Post('calculate')
  @ApiOperation({ summary: 'Calcular período de validade para uma campanha' })
  @ApiResponse({ 
    status: 200, 
    description: 'Período de validade calculado',
    schema: {
      type: 'object',
      properties: {
        startDate: { type: 'string', format: 'date-time' },
        endDate: { type: 'string', format: 'date-time' },
        validityDays: { type: 'number' },
        timezone: { type: 'string' },
        businessHours: { 
          type: 'object',
          properties: {
            start: { type: 'string' },
            end: { type: 'string' }
          }
        },
        excludedDates: { type: 'array', items: { type: 'string' } },
        warnings: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async calculateValidity(@Body() calculateDto: CalculateValidityDto) {
    const startDate = new Date(calculateDto.startDate);
    
    return this.incentiveValidityService.calculateValidity(
      startDate,
      calculateDto.industryId,
      calculateDto.categoryId,
      calculateDto.campaignType,
    );
  }

  @Post('validate')
  @ApiOperation({ summary: 'Validar período de validade personalizado' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultado da validação',
    schema: {
      type: 'object',
      properties: {
        isValid: { type: 'boolean' },
        errors: { type: 'array', items: { type: 'string' } },
        warnings: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async validateValidity(@Body() validateDto: ValidateValidityDto) {
    const startDate = new Date(validateDto.startDate);
    const endDate = new Date(validateDto.endDate);
    
    return this.incentiveValidityService.validateValidityPeriod(
      startDate,
      endDate,
      validateDto.industryId,
      validateDto.categoryId,
      validateDto.campaignType,
    );
  }

  @Get('campaign/:industryId')
  @ApiOperation({ summary: 'Obter configuração de validade para uma campanha específica' })
  @ApiQuery({ name: 'categoryId', required: false, description: 'ID da categoria do produto' })
  @ApiQuery({ name: 'campaignType', required: false, description: 'Tipo da campanha' })
  @ApiResponse({ status: 200, description: 'Configuração de validade aplicável' })
  async getValidityForCampaign(
    @Param('industryId') industryId: string,
    @Query('categoryId') categoryId?: string,
    @Query('campaignType') campaignType?: string,
  ) {
    return this.incentiveValidityService.getValidityForCampaign(industryId, categoryId, campaignType);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obter configuração de validade por ID' })
  @ApiResponse({ status: 200, description: 'Configuração encontrada' })
  @ApiResponse({ status: 404, description: 'Configuração não encontrada' })
  async findOne(@Param('id') id: string) {
    return this.incentiveValidityService.findById(id);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Atualizar configuração de validade' })
  @ApiResponse({ status: 200, description: 'Configuração atualizada com sucesso' })
  @ApiResponse({ status: 404, description: 'Configuração não encontrada' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateValidityConfigDto,
    @Request() req,
  ) {
    return this.incentiveValidityService.updateConfig(id, updateDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Excluir configuração de validade (apenas admins)' })
  @ApiResponse({ status: 200, description: 'Configuração excluída com sucesso' })
  @ApiResponse({ status: 404, description: 'Configuração não encontrada' })
  async remove(@Param('id') id: string) {
    await this.incentiveValidityService.deleteConfig(id);
    return { message: 'Configuração de validade excluída com sucesso' };
  }

  @Post('bulk-calculate')
  @ApiOperation({ summary: 'Calcular validade para múltiplas campanhas' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultados dos cálculos em lote',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          campaignId: { type: 'string' },
          startDate: { type: 'string', format: 'date-time' },
          endDate: { type: 'string', format: 'date-time' },
          validityDays: { type: 'number' },
          timezone: { type: 'string' },
          excludedDates: { type: 'array', items: { type: 'string' } },
          warnings: { type: 'array', items: { type: 'string' } }
        }
      }
    }
  })
  async bulkCalculateValidity(@Body() campaigns: (CalculateValidityDto & { campaignId: string })[]) {
    const results = [];

    for (const campaign of campaigns) {
      try {
        const startDate = new Date(campaign.startDate);
        const result = await this.incentiveValidityService.calculateValidity(
          startDate,
          campaign.industryId,
          campaign.categoryId,
          campaign.campaignType,
        );

        results.push({
          campaignId: campaign.campaignId,
          ...result,
        });
      } catch (error) {
        results.push({
          campaignId: campaign.campaignId,
          error: error.message,
        });
      }
    }

    return results;
  }

  @Get('industry/:industryId/defaults')
  @ApiOperation({ summary: 'Obter configurações padrão para uma indústria' })
  @ApiResponse({ 
    status: 200, 
    description: 'Configurações padrão da indústria',
    schema: {
      type: 'object',
      properties: {
        defaultValidityDays: { type: 'number' },
        timezone: { type: 'string' },
        allowWeekends: { type: 'boolean' },
        allowHolidays: { type: 'boolean' },
        businessHours: { type: 'object' }
      }
    }
  })
  async getIndustryDefaults(@Param('industryId') industryId: string) {
    const config = await this.incentiveValidityService.getValidityForCampaign(industryId);
    
    return {
      defaultValidityDays: config.defaultValidityDays,
      minValidityDays: config.minValidityDays,
      maxValidityDays: config.maxValidityDays,
      timezone: config.timezone,
      allowWeekends: config.allowWeekends,
      allowHolidays: config.allowHolidays,
      businessHours: config.businessHours,
      defaultStartTime: config.defaultStartTime,
      defaultEndTime: config.defaultEndTime,
    };
  }
}
