import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { SecurityEvent, SecurityEventType, SecuritySeverity } from '../database/entities/security-event.entity';

@Injectable()
export class SecurityAuditService {
  private readonly logger = new Logger(SecurityAuditService.name);

  constructor(
    @InjectRepository(SecurityEvent)
    private readonly securityEventRepository: Repository<SecurityEvent>,
  ) {}

  async logSecurityEvent(eventData: {
    type: SecurityEventType;
    severity: SecuritySeverity;
    description: string;
    userId?: string;
    ipAddress?: string;
    userAgent?: string;
    resource?: string;
    action?: string;
    metadata?: Record<string, any>;
  }): Promise<SecurityEvent> {
    const event = this.securityEventRepository.create({
      ...eventData,
      timestamp: new Date(),
    });

    const savedEvent = await this.securityEventRepository.save(event);

    // Log to console based on severity
    const logMessage = `Security Event [${eventData.type}]: ${eventData.description}`;
    
    switch (eventData.severity) {
      case SecuritySeverity.CRITICAL:
        this.logger.error(logMessage);
        break;
      case SecuritySeverity.HIGH:
        this.logger.warn(logMessage);
        break;
      case SecuritySeverity.MEDIUM:
        this.logger.log(logMessage);
        break;
      case SecuritySeverity.LOW:
        this.logger.debug(logMessage);
        break;
    }

    return savedEvent;
  }

  async getSecurityEvents(
    limit: number = 100,
    offset: number = 0,
    severity?: SecuritySeverity,
    type?: SecurityEventType
  ): Promise<{ events: SecurityEvent[]; total: number }> {
    const queryBuilder = this.securityEventRepository.createQueryBuilder('event')
      .orderBy('event.timestamp', 'DESC')
      .limit(limit)
      .offset(offset);

    if (severity) {
      queryBuilder.andWhere('event.severity = :severity', { severity });
    }

    if (type) {
      queryBuilder.andWhere('event.type = :type', { type });
    }

    const [events, total] = await queryBuilder.getManyAndCount();

    return { events, total };
  }

  async getSecurityEventsByUser(
    userId: string,
    limit: number = 50
  ): Promise<SecurityEvent[]> {
    return this.securityEventRepository.find({
      where: { userId },
      order: { timestamp: 'DESC' },
      take: limit,
    });
  }

  async getSecurityEventsByIp(
    ipAddress: string,
    limit: number = 50
  ): Promise<SecurityEvent[]> {
    return this.securityEventRepository.find({
      where: { ipAddress },
      order: { timestamp: 'DESC' },
      take: limit,
    });
  }

  async getSecurityStatistics(): Promise<{
    totalEvents: number;
    eventsBySeverity: Record<SecuritySeverity, number>;
    eventsByType: Record<SecurityEventType, number>;
    recentHighSeverityEvents: number;
  }> {
    const totalEvents = await this.securityEventRepository.count();

    // Get events by severity
    const severityResults = await this.securityEventRepository
      .createQueryBuilder('event')
      .select('event.severity', 'severity')
      .addSelect('COUNT(*)', 'count')
      .groupBy('event.severity')
      .getRawMany();

    const eventsBySeverity = severityResults.reduce((acc, result) => {
      acc[result.severity as SecuritySeverity] = parseInt(result.count);
      return acc;
    }, {} as Record<SecuritySeverity, number>);

    // Get events by type
    const typeResults = await this.securityEventRepository
      .createQueryBuilder('event')
      .select('event.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('event.type')
      .getRawMany();

    const eventsByType = typeResults.reduce((acc, result) => {
      acc[result.type as SecurityEventType] = parseInt(result.count);
      return acc;
    }, {} as Record<SecurityEventType, number>);

    // Get recent high severity events (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const recentHighSeverityEvents = await this.securityEventRepository.count({
      where: [
        { severity: SecuritySeverity.HIGH, timestamp: yesterday },
        { severity: SecuritySeverity.CRITICAL, timestamp: yesterday },
      ],
    });

    return {
      totalEvents,
      eventsBySeverity,
      eventsByType,
      recentHighSeverityEvents,
    };
  }

  async logFailedLogin(email: string, ipAddress: string, userAgent?: string): Promise<void> {
    await this.logSecurityEvent({
      type: SecurityEventType.FAILED_LOGIN,
      severity: SecuritySeverity.MEDIUM,
      description: `Failed login attempt for ${email}`,
      ipAddress,
      userAgent,
      action: 'login',
      metadata: { email },
    });
  }

  async logSuspiciousActivity(
    description: string,
    userId?: string,
    ipAddress?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logSecurityEvent({
      type: SecurityEventType.SUSPICIOUS_ACTIVITY,
      severity: SecuritySeverity.HIGH,
      description,
      userId,
      ipAddress,
      metadata,
    });
  }

  async logUnauthorizedAccess(
    resource: string,
    userId?: string,
    ipAddress?: string,
    action?: string
  ): Promise<void> {
    await this.logSecurityEvent({
      type: SecurityEventType.UNAUTHORIZED_ACCESS,
      severity: SecuritySeverity.HIGH,
      description: `Unauthorized access attempt to ${resource}`,
      userId,
      ipAddress,
      resource,
      action,
    });
  }

  async logRateLimitExceeded(
    endpoint: string,
    ipAddress: string,
    userId?: string
  ): Promise<void> {
    await this.logSecurityEvent({
      type: SecurityEventType.RATE_LIMIT_EXCEEDED,
      severity: SecuritySeverity.MEDIUM,
      description: `Rate limit exceeded for ${endpoint}`,
      userId,
      ipAddress,
      resource: endpoint,
      metadata: { endpoint },
    });
  }
}
