import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { IndustryRulesService } from './industry-rules.service';
import { CreateIndustryRuleDto, UpdateIndustryRuleDto, ValidateCampaignDto } from './dto/industry-rules.dto';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

@ApiTags('Industry Rules')
@Controller('industry-rules')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class IndustryRulesController {
  constructor(private readonly industryRulesService: IndustryRulesService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Criar nova regra de indústria' })
  @ApiResponse({ status: 201, description: 'Regra criada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 409, description: 'Regra já existe para esta indústria e tipo' })
  async create(@Body() createDto: CreateIndustryRuleDto, @Request() req) {
    return this.industryRulesService.create(createDto, req.user.sub);
  }

  @Get()
  @ApiOperation({ summary: 'Listar regras de indústria' })
  @ApiQuery({ name: 'industryId', required: false, description: 'Filtrar por ID da indústria' })
  @ApiResponse({ status: 200, description: 'Lista de regras' })
  async findAll(@Query('industryId') industryId?: string) {
    return this.industryRulesService.findAll(industryId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obter regra por ID' })
  @ApiResponse({ status: 200, description: 'Dados da regra' })
  @ApiResponse({ status: 404, description: 'Regra não encontrada' })
  async findOne(@Param('id') id: string) {
    return this.industryRulesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Atualizar regra de indústria' })
  @ApiResponse({ status: 200, description: 'Regra atualizada com sucesso' })
  @ApiResponse({ status: 404, description: 'Regra não encontrada' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateIndustryRuleDto,
    @Request() req,
  ) {
    return this.industryRulesService.update(id, updateDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Excluir regra de indústria (apenas admins)' })
  @ApiResponse({ status: 204, description: 'Regra excluída com sucesso' })
  @ApiResponse({ status: 404, description: 'Regra não encontrada' })
  async remove(@Param('id') id: string) {
    await this.industryRulesService.delete(id);
  }

  @Post('validate-campaign')
  @ApiOperation({ summary: 'Validar campanha contra regras de indústria' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultado da validação',
    schema: {
      type: 'object',
      properties: {
        isValid: { type: 'boolean' },
        errors: { type: 'array', items: { type: 'string' } },
        warnings: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async validateCampaign(@Body() validateDto: ValidateCampaignDto) {
    const { industryId, incentivePercentage, incentiveValue, productCount, validityDays = 30 } = validateDto;
    
    return this.industryRulesService.validateCampaignIncentive(
      industryId,
      incentivePercentage,
      incentiveValue,
      productCount,
      validityDays
    );
  }

  @Get('industry/:industryId/limits')
  @ApiOperation({ summary: 'Obter limites configurados para uma indústria' })
  @ApiResponse({ 
    status: 200, 
    description: 'Limites da indústria',
    schema: {
      type: 'object',
      properties: {
        minPercentage: { type: 'number', nullable: true },
        maxPercentage: { type: 'number', nullable: true },
        minValue: { type: 'number', nullable: true },
        maxValue: { type: 'number', nullable: true },
        productLimitPerCycle: { type: 'number', nullable: true },
        incentiveValidityDays: { type: 'number', default: 30 }
      }
    }
  })
  async getIndustryLimits(@Param('industryId') industryId: string) {
    return this.industryRulesService.getIndustryLimits(industryId);
  }

  @Get('industry/:industryId/default-validity')
  @ApiOperation({ summary: 'Obter validade padrão de incentivos para uma indústria' })
  @ApiResponse({ 
    status: 200, 
    description: 'Validade padrão em dias',
    schema: {
      type: 'object',
      properties: {
        validityDays: { type: 'number' }
      }
    }
  })
  async getDefaultIncentiveValidity(@Param('industryId') industryId: string) {
    const validityDays = await this.industryRulesService.getDefaultIncentiveValidity(industryId);
    return { validityDays };
  }

  @Post('bulk-validate')
  @ApiOperation({ summary: 'Validar múltiplas campanhas de uma vez' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultados das validações',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          campaignId: { type: 'string' },
          isValid: { type: 'boolean' },
          errors: { type: 'array', items: { type: 'string' } },
          warnings: { type: 'array', items: { type: 'string' } }
        }
      }
    }
  })
  async bulkValidate(@Body() campaigns: Array<ValidateCampaignDto & { campaignId: string }>) {
    const results = [];
    
    for (const campaign of campaigns) {
      const { campaignId, industryId, incentivePercentage, incentiveValue, productCount, validityDays = 30 } = campaign;
      
      const validation = await this.industryRulesService.validateCampaignIncentive(
        industryId,
        incentivePercentage,
        incentiveValue,
        productCount,
        validityDays
      );
      
      results.push({
        campaignId,
        ...validation
      });
    }
    
    return results;
  }
}
