import { Injectable, Logger } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as fs from 'fs';
import * as path from 'path';

export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf' | 'json';
  includeCharts?: boolean;
  includeRawData?: boolean;
  compression?: boolean;
  password?: string;
}

@Injectable()
export class ExportService {
  private readonly logger = new Logger(ExportService.name);
  private readonly exportDir = path.join(process.cwd(), 'exports');

  constructor() {
    // Ensure export directory exists
    if (!fs.existsSync(this.exportDir)) {
      fs.mkdirSync(this.exportDir, { recursive: true });
    }
  }

  async exportToExcel(data: any, filename: string, options: ExportOptions): Promise<string> {
    this.logger.log(`Generating Excel file: ${filename}`);

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Relatório');

    // Add title
    worksheet.addRow([`Relatório - ${new Date().toLocaleDateString('pt-BR')}`]);
    worksheet.addRow([]);

    if (data.summary) {
      // Add summary section
      worksheet.addRow(['RESUMO']);
      Object.entries(data.summary).forEach(([key, value]) => {
        worksheet.addRow([key, value]);
      });
      worksheet.addRow([]);
    }

    // Add main data
    if (data.campaigns) {
      worksheet.addRow(['CAMPANHAS']);
      const headers = ['ID', 'Nome', 'Status', 'Impressões', 'Cliques', 'CTR (%)', 'Conversões', 'ROI (%)'];
      worksheet.addRow(headers);
      
      data.campaigns.forEach(campaign => {
        worksheet.addRow([
          campaign.id,
          campaign.name,
          campaign.status,
          campaign.impressions,
          campaign.clicks,
          campaign.ctr,
          campaign.conversions,
          campaign.roi
        ]);
      });
    } else if (data.products) {
      worksheet.addRow(['PRODUTOS']);
      const headers = ['ID', 'Nome', 'Categoria', 'Preço', 'Vendas', 'Receita', 'Avaliação', 'Estoque'];
      worksheet.addRow(headers);
      
      data.products.forEach(product => {
        worksheet.addRow([
          product.id,
          product.name,
          product.category,
          product.price,
          product.salesCount,
          product.revenue,
          product.averageRating,
          product.stockQuantity
        ]);
      });
    }

    // Style the worksheet
    worksheet.getRow(1).font = { bold: true, size: 16 };
    worksheet.getRow(3).font = { bold: true, size: 14 };
    
    // Auto-fit columns
    worksheet.columns.forEach(column => {
      column.width = 15;
    });

    const filePath = path.join(this.exportDir, filename);
    await workbook.xlsx.writeFile(filePath);

    this.logger.log(`Excel file generated: ${filePath}`);
    return filePath;
  }

  async exportToPDF(data: any, filename: string, options: ExportOptions): Promise<string> {
    this.logger.log(`Generating PDF file: ${filename}`);

    const doc = new jsPDF();
    
    // Add title
    doc.setFontSize(20);
    doc.text('Relatório de Performance', 20, 30);
    
    doc.setFontSize(12);
    doc.text(`Gerado em: ${new Date().toLocaleDateString('pt-BR')}`, 20, 45);

    let yPosition = 60;

    // Add summary
    if (data.summary) {
      doc.setFontSize(16);
      doc.text('Resumo', 20, yPosition);
      yPosition += 15;

      doc.setFontSize(12);
      Object.entries(data.summary).forEach(([key, value]) => {
        doc.text(`${key}: ${value}`, 20, yPosition);
        yPosition += 10;
      });
      yPosition += 10;
    }

    // Add table data
    if (data.campaigns) {
      doc.setFontSize(16);
      doc.text('Campanhas', 20, yPosition);
      yPosition += 15;

      const tableData = data.campaigns.map(campaign => [
        campaign.id,
        campaign.name,
        campaign.status,
        campaign.impressions?.toString() || '0',
        campaign.clicks?.toString() || '0',
        campaign.ctr?.toString() || '0',
        campaign.conversions?.toString() || '0',
        campaign.roi?.toString() || '0'
      ]);

      autoTable(doc, {
        head: [['ID', 'Nome', 'Status', 'Impressões', 'Cliques', 'CTR (%)', 'Conversões', 'ROI (%)']],
        body: tableData,
        startY: yPosition,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [66, 139, 202] }
      });
    } else if (data.products) {
      doc.setFontSize(16);
      doc.text('Produtos', 20, yPosition);
      yPosition += 15;

      const tableData = data.products.map(product => [
        product.id,
        product.name,
        product.category,
        product.price?.toString() || '0',
        product.salesCount?.toString() || '0',
        product.revenue?.toString() || '0',
        product.averageRating?.toString() || '0',
        product.stockQuantity?.toString() || '0'
      ]);

      autoTable(doc, {
        head: [['ID', 'Nome', 'Categoria', 'Preço', 'Vendas', 'Receita', 'Avaliação', 'Estoque']],
        body: tableData,
        startY: yPosition,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [66, 139, 202] }
      });
    }

    const filePath = path.join(this.exportDir, filename);
    doc.save(filePath);

    this.logger.log(`PDF file generated: ${filePath}`);
    return filePath;
  }

  async exportToJSON(data: any, filename: string): Promise<string> {
    this.logger.log(`Generating JSON file: ${filename}`);
    
    const filePath = path.join(this.exportDir, filename);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    
    this.logger.log(`JSON file generated: ${filePath}`);
    return filePath;
  }

  async exportToCSV(data: any, filename: string): Promise<string> {
    this.logger.log(`Generating CSV file: ${filename}`);
    
    let csvContent = '';
    
    if (data.campaigns) {
      csvContent += 'ID,Nome,Status,Impressões,Cliques,CTR (%),Conversões,ROI (%)\n';
      data.campaigns.forEach(campaign => {
        csvContent += `${campaign.id},"${campaign.name}",${campaign.status},${campaign.impressions || 0},${campaign.clicks || 0},${campaign.ctr || 0},${campaign.conversions || 0},${campaign.roi || 0}\n`;
      });
    } else if (data.products) {
      csvContent += 'ID,Nome,Categoria,Preço,Vendas,Receita,Avaliação,Estoque\n';
      data.products.forEach(product => {
        csvContent += `${product.id},"${product.name}","${product.category}",${product.price || 0},${product.salesCount || 0},${product.revenue || 0},${product.averageRating || 0},${product.stockQuantity || 0}\n`;
      });
    }
    
    const filePath = path.join(this.exportDir, filename);
    fs.writeFileSync(filePath, csvContent);
    
    this.logger.log(`CSV file generated: ${filePath}`);
    return filePath;
  }

  getFileStats(filePath: string): { size: number; exists: boolean } {
    try {
      const stats = fs.statSync(filePath);
      return { size: stats.size, exists: true };
    } catch (error) {
      return { size: 0, exists: false };
    }
  }

  deleteFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        this.logger.log(`File deleted: ${filePath}`);
      }
    } catch (error) {
      this.logger.error(`Error deleting file: ${filePath}`, error);
    }
  }
}
