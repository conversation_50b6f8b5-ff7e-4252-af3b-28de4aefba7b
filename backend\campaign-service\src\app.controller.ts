import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('health')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Health check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  getHealth(): { status: string; service: string; timestamp: string } {
    console.log('🔥🔥🔥 APP CONTROLLER GET HEALTH CALLED 🔥🔥🔥');
    return this.appService.getHealth();
  }

  @Get('health')
  @ApiOperation({ summary: 'Detailed health check' })
  @ApiResponse({ status: 200, description: 'Detailed health information' })
  getDetailedHealth() {
    return this.appService.getDetailedHealth();
  }

  @Get('debug-test')
  @ApiOperation({ summary: 'Debug test endpoint' })
  @ApiResponse({ status: 200, description: 'Debug test successful' })
  debugTest() {
    console.log('🔥🔥🔥 APP CONTROLLER DEBUG TEST CALLED 🔥🔥🔥');
    return { debug: 'app-controller-working', timestamp: new Date().toISOString() };
  }
}
