import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';

import { ProductPrice } from '../database/entities/product-price.entity';
import { PriceStatus, PriceType } from '../database/entities/price-types.entity';
import { PriceHistory } from '../database/entities/price-history.entity';
import { PriceProvider, ProviderStatus, ProviderType } from '../database/entities/price-provider.entity';
import { PriceProviderService } from './price-provider.service';
import { PriceCacheService } from './price-cache.service';
import { PriceValidationService } from './price-validation.service';

@Injectable()
export class PriceIntegrationService {
  private readonly logger = new Logger(PriceIntegrationService.name);

  constructor(
    @InjectRepository(ProductPrice)
    private readonly productPriceRepository: Repository<ProductPrice>,
    @InjectRepository(PriceHistory)
    private readonly priceHistoryRepository: Repository<PriceHistory>,
    @InjectRepository(PriceProvider)
    private readonly priceProviderRepository: Repository<PriceProvider>,
    private readonly priceProviderService: PriceProviderService,
    private readonly priceCacheService: PriceCacheService,
    private readonly priceValidationService: PriceValidationService,
  ) {}

  async getProductPrice(
    productId: string,
    priceType: PriceType = PriceType.REGULAR,
    forceRefresh = false
  ): Promise<{ price: number; currency: string; lastUpdated: Date; provider: string } | null> {
    // Try cache first if not forcing refresh
    if (!forceRefresh) {
      const cachedPrice = await this.priceCacheService.getPrice(productId, priceType);
      if (cachedPrice) {
        return cachedPrice;
      }
    }

    // Get current price from database
    const currentPrice = await this.productPriceRepository.findOne({
      where: {
        productId,
        priceType,
        status: PriceStatus.ACTIVE,
      },
      relations: ['provider'],
      order: { lastUpdated: 'DESC' },
    });

    if (currentPrice && currentPrice.isValid() && !forceRefresh) {
      const result = {
        price: currentPrice.price,
        currency: currentPrice.currency,
        lastUpdated: currentPrice.lastUpdated,
        provider: currentPrice.provider.displayName,
      };

      // Cache the result
      await this.priceCacheService.setPrice(productId, priceType, result);
      return result;
    }

    // Fetch fresh price from providers
    return this.fetchPriceFromProviders(productId, priceType);
  }

  async fetchPriceFromProviders(
    productId: string,
    priceType: PriceType = PriceType.REGULAR
  ): Promise<{ price: number; currency: string; lastUpdated: Date; provider: string } | null> {
    // Get active providers ordered by priority
    const providers = await this.priceProviderRepository.find({
      where: { status: ProviderStatus.ACTIVE },
      order: { priority: 'DESC', lastSuccessAt: 'DESC' },
    });

    if (providers.length === 0) {
      this.logger.warn('No active price providers available');
      return null;
    }

    // Try each provider until we get a valid price
    for (const provider of providers) {
      if (!provider.shouldUse()) {
        this.logger.debug(`Skipping unhealthy provider: ${provider.name}`);
        continue;
      }

      try {
        this.logger.debug(`Fetching price for product ${productId} from provider ${provider.name}`);
        
        const priceData = await this.priceProviderService.fetchPrice(provider, productId, priceType);
        
        if (priceData && this.priceValidationService.isValidPrice(priceData.price)) {
          // Save the price
          const savedPrice = await this.saveProductPrice(
            productId,
            provider.id,
            priceType,
            priceData.price,
            priceData.currency || 'BRL',
            provider.cacheMinutes
          );

          provider.markSuccess();
          await this.priceProviderRepository.save(provider);

          const result = {
            price: savedPrice.price,
            currency: savedPrice.currency,
            lastUpdated: savedPrice.lastUpdated,
            provider: provider.displayName,
          };

          // Cache the result
          await this.priceCacheService.setPrice(productId, priceType, result);
          
          this.logger.log(`Successfully fetched price for product ${productId} from ${provider.name}: ${savedPrice.getFormattedPrice()}`);
          return result;
        }
      } catch (error) {
        this.logger.error(`Error fetching price from provider ${provider.name}:`, error);
        
        provider.markError(error.message);
        await this.priceProviderRepository.save(provider);
        
        // Continue to next provider
        continue;
      }
    }

    this.logger.warn(`Failed to fetch price for product ${productId} from all providers`);
    return null;
  }

  async saveProductPrice(
    productId: string,
    providerId: string,
    priceType: PriceType,
    price: number,
    currency: string = 'BRL',
    cacheMinutes: number = 60
  ): Promise<ProductPrice> {
    // Get current price for comparison
    const currentPrice = await this.productPriceRepository.findOne({
      where: { productId, providerId, priceType },
      order: { lastUpdated: 'DESC' },
    });

    // Create or update price record
    let productPrice: ProductPrice;
    
    if (currentPrice) {
      productPrice = currentPrice;
      productPrice.markAsActive(price, cacheMinutes);
    } else {
      productPrice = this.productPriceRepository.create({
        productId,
        providerId,
        priceType,
        price,
        currency,
        status: PriceStatus.ACTIVE,
        lastUpdated: new Date(),
        expiresAt: new Date(Date.now() + cacheMinutes * 60 * 1000),
      });
    }

    const savedPrice = await this.productPriceRepository.save(productPrice);

    // Record price history if price changed
    if (!currentPrice || currentPrice.price !== price) {
      const historyRecord = PriceHistory.createFromPriceChange(
        productId,
        providerId,
        priceType,
        currentPrice?.price || null,
        price,
        currency,
        'Atualização automática',
        'API'
      );

      await this.priceHistoryRepository.save(historyRecord);
      
      this.logger.log(`Price change recorded for product ${productId}: ${currentPrice?.getFormattedPrice() || 'N/A'} → ${savedPrice.getFormattedPrice()}`);
    }

    return savedPrice;
  }

  async updateProductPrice(
    productId: string,
    priceType: PriceType,
    price: number,
    currency: string = 'BRL',
    reason?: string
  ): Promise<ProductPrice> {
    // Find manual/fallback provider
    const fallbackProvider = await this.priceProviderRepository.findOne({
      where: { type: ProviderType.FALLBACK, status: ProviderStatus.ACTIVE },
    });

    if (!fallbackProvider) {
      throw new BadRequestException('Provedor de fallback não encontrado');
    }

    if (!this.priceValidationService.isValidPrice(price)) {
      throw new BadRequestException('Preço inválido');
    }

    return this.saveProductPrice(
      productId,
      fallbackProvider.id,
      priceType,
      price,
      currency,
      fallbackProvider.cacheMinutes
    );
  }

  async getPriceHistory(
    productId: string,
    priceType?: PriceType,
    limit: number = 50
  ): Promise<PriceHistory[]> {
    const queryBuilder = this.priceHistoryRepository.createQueryBuilder('history')
      .leftJoinAndSelect('history.provider', 'provider')
      .where('history.productId = :productId', { productId })
      .orderBy('history.recordedAt', 'DESC')
      .limit(limit);

    if (priceType) {
      queryBuilder.andWhere('history.priceType = :priceType', { priceType });
    }

    return queryBuilder.getMany();
  }

  async getProductPrices(
    productIds: string[],
    priceType: PriceType = PriceType.REGULAR
  ): Promise<Record<string, { price: number; currency: string; lastUpdated: Date; provider: string } | null>> {
    const results: Record<string, any> = {};

    // Process in batches to avoid overwhelming the system
    const batchSize = 10;
    for (let i = 0; i < productIds.length; i += batchSize) {
      const batch = productIds.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (productId) => {
        const price = await this.getProductPrice(productId, priceType);
        return { productId, price };
      });

      const batchResults = await Promise.all(batchPromises);
      
      batchResults.forEach(({ productId, price }) => {
        results[productId] = price;
      });
    }

    return results;
  }

  async refreshExpiredPrices(): Promise<{ updated: number; failed: number }> {
    const expiredPrices = await this.productPriceRepository.find({
      where: [
        { status: PriceStatus.ACTIVE },
        { status: PriceStatus.ERROR },
      ],
      relations: ['provider'],
    });

    const toUpdate = expiredPrices.filter(price => 
      price.needsUpdate() && price.provider.shouldUse()
    );

    this.logger.log(`Found ${toUpdate.length} prices that need updating`);

    let updated = 0;
    let failed = 0;

    for (const price of toUpdate) {
      try {
        await this.fetchPriceFromProviders(price.productId, price.priceType);
        updated++;
      } catch (error) {
        this.logger.error(`Failed to update price for product ${price.productId}:`, error);
        failed++;
      }
    }

    this.logger.log(`Price refresh completed: ${updated} updated, ${failed} failed`);
    return { updated, failed };
  }

  // Cron job to refresh expired prices every 30 minutes
  @Cron('0 */30 * * * *')
  async refreshExpiredPricesCron(): Promise<void> {
    try {
      await this.refreshExpiredPrices();
    } catch (error) {
      this.logger.error('Error in refresh expired prices cron job:', error);
    }
  }

  // Cron job to cleanup old price history (keep last 1000 records per product)
  @Cron('0 2 * * *') // Daily at 2 AM
  async cleanupOldPriceHistory(): Promise<void> {
    try {
      this.logger.log('Starting price history cleanup...');
      
      // This is a simplified cleanup - in production you might want more sophisticated logic
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 90); // Keep last 90 days
      
      const result = await this.priceHistoryRepository
        .createQueryBuilder()
        .delete()
        .where('recordedAt < :cutoffDate', { cutoffDate })
        .execute();

      this.logger.log(`Cleaned up ${result.affected} old price history records`);
    } catch (error) {
      this.logger.error('Error in cleanup price history cron job:', error);
    }
  }

  async getProviderStats(): Promise<{
    providers: Array<{
      id: string;
      name: string;
      displayName: string;
      type: string;
      status: string;
      healthScore: number;
      lastSuccess: Date | null;
      lastError: Date | null;
      consecutiveErrors: number;
    }>;
    totalPrices: number;
    activePrices: number;
    expiredPrices: number;
  }> {
    const providers = await this.priceProviderRepository.find({
      order: { priority: 'DESC' },
    });

    const [totalPrices, activePrices, expiredPrices] = await Promise.all([
      this.productPriceRepository.count(),
      this.productPriceRepository.count({ where: { status: PriceStatus.ACTIVE } }),
      this.productPriceRepository.count({ where: { status: PriceStatus.ERROR } }),
    ]);

    return {
      providers: providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        displayName: provider.displayName,
        type: provider.type,
        status: provider.getDisplayStatus(),
        healthScore: provider.getHealthScore(),
        lastSuccess: provider.lastSuccessAt,
        lastError: provider.lastErrorAt,
        consecutiveErrors: provider.consecutiveErrors,
      })),
      totalPrices,
      activePrices,
      expiredPrices,
    };
  }
}
