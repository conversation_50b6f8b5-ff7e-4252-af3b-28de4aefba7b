import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';

import { AttributionController } from './attribution.controller';
import { AttributionService } from './attribution.service';
import { AttributionEngineService } from './attribution-engine.service';
import { AttributionAnalyticsService } from './attribution-analytics.service';
import { TouchpointService } from './touchpoint.service';
import { ConversionService } from './conversion.service';

import { Attribution } from '../database/entities/attribution.entity';
import { Touchpoint } from '../database/entities/touchpoint.entity';
import { Conversion } from '../database/entities/conversion.entity';
import { AttributionModelConfig } from '../database/entities/attribution-model.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Attribution,
      Touchpoint,
      Conversion,
      AttributionModelConfig,
    ]),
    ConfigModule,
  ],
  controllers: [AttributionController],
  providers: [
    AttributionService,
    AttributionEngineService,
    AttributionAnalyticsService,
    TouchpointService,
    ConversionService,
  ],
  exports: [
    AttributionService,
    AttributionEngineService,
    TouchpointService,
    ConversionService,
  ],
})
export class AttributionModule {}
