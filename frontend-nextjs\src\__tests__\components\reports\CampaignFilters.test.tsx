import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import CampaignFilters from '@/components/reports/CampaignFilters'

// Mock the API
jest.mock('@/lib/api', () => ({
  campaignAPI: {
    getCampaigns: jest.fn(),
  },
}))

const mockProps = {
  filters: {
    campaignIds: [],
    status: [],
    search: ''
  },
  onFiltersChange: jest.fn(),
  isOpen: true,
  onClose: jest.fn()
}

describe('CampaignFilters', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders when open', () => {
    render(<CampaignFilters {...mockProps} />)

    expect(screen.getByText('Filtros de Campanhas')).toBeInTheDocument()
    expect(screen.getByText('Status das Campanhas')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Digite o nome da campanha...')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<CampaignFilters {...mockProps} isOpen={false} />)
    
    expect(screen.queryByText('Filtros de Campanhas')).not.toBeInTheDocument()
  })

  it('displays status options in Portuguese', () => {
    render(<CampaignFilters {...mockProps} />)
    
    expect(screen.getByText('Ativas')).toBeInTheDocument()
    expect(screen.getByText('Concluídas')).toBeInTheDocument()
    expect(screen.getByText('Pausadas')).toBeInTheDocument()
    expect(screen.getByText('Rascunho')).toBeInTheDocument()
  })

  it('allows status selection', async () => {
    const user = userEvent.setup()
    render(<CampaignFilters {...mockProps} />)
    
    const activeCheckbox = screen.getByLabelText('Ativas')
    await user.click(activeCheckbox)
    
    expect(activeCheckbox).toBeChecked()
  })

  it('allows search input', async () => {
    const user = userEvent.setup()
    render(<CampaignFilters {...mockProps} />)

    const searchInput = screen.getByPlaceholderText('Digite o nome da campanha...')
    await user.type(searchInput, 'Black Friday')

    expect(searchInput).toHaveValue('Black Friday')
  })

  it('calls onFiltersChange when applying filters', async () => {
    const user = userEvent.setup()
    render(<CampaignFilters {...mockProps} />)
    
    // Select a status
    const activeCheckbox = screen.getByLabelText('Ativas')
    await user.click(activeCheckbox)
    
    // Add search term
    const searchInput = screen.getByPlaceholderText('Digite o nome da campanha...')
    await user.type(searchInput, 'test')
    
    // Apply filters
    const applyButton = screen.getByText('Aplicar Filtros')
    await user.click(applyButton)
    
    expect(mockProps.onFiltersChange).toHaveBeenCalledWith({
      campaignIds: [],
      status: ['active'],
      search: 'test'
    })
  })

  it('calls onClose when cancel button is clicked', async () => {
    const user = userEvent.setup()
    render(<CampaignFilters {...mockProps} />)
    
    const cancelButton = screen.getByText('Cancelar')
    await user.click(cancelButton)
    
    expect(mockProps.onClose).toHaveBeenCalledTimes(1)
  })

  it('loads mock campaigns when API fails', async () => {
    const { campaignAPI } = require('@/lib/api')
    campaignAPI.getCampaigns.mockRejectedValue(new Error('API Error'))
    
    render(<CampaignFilters {...mockProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Campanha Black Friday')).toBeInTheDocument()
      expect(screen.getByText('Promoção Eletrônicos')).toBeInTheDocument()
    })
  })

  it('allows campaign selection', async () => {
    const user = userEvent.setup()
    const { campaignAPI } = require('@/lib/api')
    campaignAPI.getCampaigns.mockRejectedValue(new Error('API Error'))
    
    render(<CampaignFilters {...mockProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Campanha Black Friday')).toBeInTheDocument()
    })

    const campaignDiv = screen.getByText('Campanha Black Friday').closest('div[class*="cursor-pointer"]')
    await user.click(campaignDiv!)

    // Verify the campaign was selected by checking if the CheckCircle icon appears
    await waitFor(() => {
      expect(screen.getByText('Campanha Black Friday').closest('div[class*="bg-blue-50"]')).toBeInTheDocument()
    })
  })

  it('clears all filters when clear button is clicked', async () => {
    const user = userEvent.setup()
    render(<CampaignFilters {...mockProps} />)
    
    // Select some filters first
    const activeCheckbox = screen.getByLabelText('Ativas')
    await user.click(activeCheckbox)
    
    const searchInput = screen.getByPlaceholderText('Digite o nome da campanha...')
    await user.type(searchInput, 'test')
    
    // Clear filters
    const clearButton = screen.getByText('Limpar Filtros')
    await user.click(clearButton)
    
    expect(activeCheckbox).not.toBeChecked()
    expect(searchInput).toHaveValue('')
  })

  it('shows loading state while fetching campaigns', () => {
    const { campaignAPI } = require('@/lib/api')
    campaignAPI.getCampaigns.mockImplementation(() => new Promise(() => {})) // Never resolves
    
    render(<CampaignFilters {...mockProps} />)
    
    expect(screen.getByText('Carregando campanhas...')).toBeInTheDocument()
  })

  it('preserves initial filter values', () => {
    const propsWithFilters = {
      ...mockProps,
      filters: {
        campaignIds: ['1'],
        status: ['active', 'paused'],
        search: 'initial search'
      }
    }
    
    render(<CampaignFilters {...propsWithFilters} />)
    
    expect(screen.getByLabelText('Ativas')).toBeChecked()
    expect(screen.getByLabelText('Pausadas')).toBeChecked()
    expect(screen.getByDisplayValue('initial search')).toBeInTheDocument()
  })
})
