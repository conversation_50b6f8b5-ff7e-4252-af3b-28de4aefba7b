import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  BeforeInsert,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import * as bcrypt from 'bcryptjs';

export enum OtpType {
  LOGIN = 'login',
  EMAIL_VERIFICATION = 'email_verification',
  PASSWORD_RESET = 'password_reset',
  TWO_FACTOR = 'two_factor',
}

export enum OtpStatus {
  PENDING = 'pending',
  USED = 'used',
  EXPIRED = 'expired',
}

@Entity('otps')
@Index(['email', 'type'])
@Index(['token'])
@Index(['expiresAt'])
export class Otp {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  email: string;

  @Column({ type: 'varchar', length: 255 })
  @Exclude()
  code: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  token: string;

  @Column({
    type: 'enum',
    enum: OtpType,
    default: OtpType.LOGIN,
  })
  type: OtpType;

  @Column({
    type: 'enum',
    enum: OtpStatus,
    default: OtpStatus.PENDING,
  })
  status: OtpStatus;

  @Column({ type: 'timestamp' })
  expiresAt: Date;

  @Column({ type: 'inet', nullable: true })
  requestIp: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  userAgent: string;

  @Column({ type: 'timestamp', nullable: true })
  usedAt: Date;

  @Column({ type: 'uuid', nullable: true })
  userId: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @BeforeInsert()
  async hashCode() {
    if (this.code && !this.code.startsWith('$2a$')) {
      this.code = await bcrypt.hash(this.code, 12);
    }
  }

  async validateCode(code: string): Promise<boolean> {
    if (!this.code) return false;
    return bcrypt.compare(code, this.code);
  }

  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  isUsed(): boolean {
    return this.status === OtpStatus.USED;
  }

  isValid(): boolean {
    return this.status === OtpStatus.PENDING && !this.isExpired();
  }

  toJSON() {
    const { code, ...result } = this;
    return result;
  }
}
