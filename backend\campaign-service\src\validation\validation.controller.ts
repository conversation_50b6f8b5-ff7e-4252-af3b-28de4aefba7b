import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Param,
  Delete,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ValidationService, ValidationContext } from './validation.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

export class ValidateRealTimeDto {
  industryId: string;
  incentivePercentage?: number;
  incentiveValue?: number;
}

export class ValidateCampaignDto {
  industryId: string;
  campaignId?: string;
  productIds?: string[];
  incentivePercentage?: number;
  incentiveValue?: number;
  productCount?: number;
  validityDays?: number;
  cycleId?: string;
}

@ApiTags('Validation')
@Controller('validation')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ValidationController {
  constructor(private readonly validationService: ValidationService) {}

  @Post('real-time')
  @ApiOperation({ summary: 'Validação em tempo real para feedback imediato' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultado da validação em tempo real',
    schema: {
      type: 'object',
      properties: {
        isValid: { type: 'boolean' },
        errors: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              code: { type: 'string' },
              message: { type: 'string' },
              field: { type: 'string' },
              value: { type: 'any' },
              ruleId: { type: 'string' },
              severity: { type: 'string', enum: ['error', 'warning'] }
            }
          }
        },
        warnings: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              code: { type: 'string' },
              message: { type: 'string' },
              field: { type: 'string' },
              value: { type: 'any' },
              ruleId: { type: 'string' },
              severity: { type: 'string', enum: ['error', 'warning'] }
            }
          }
        },
        performance: {
          type: 'object',
          properties: {
            executionTime: { type: 'number' },
            rulesEvaluated: { type: 'number' },
            cacheHits: { type: 'number' }
          }
        }
      }
    }
  })
  async validateRealTime(@Body() validateDto: ValidateRealTimeDto) {
    const context: ValidationContext = {
      industryId: validateDto.industryId,
      incentivePercentage: validateDto.incentivePercentage,
      incentiveValue: validateDto.incentiveValue,
    };

    return this.validationService.validateInRealTime(context);
  }

  @Post('campaign')
  @ApiOperation({ summary: 'Validação completa de campanha' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultado da validação completa',
    schema: {
      type: 'object',
      properties: {
        isValid: { type: 'boolean' },
        errors: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              code: { type: 'string' },
              message: { type: 'string' },
              field: { type: 'string' },
              value: { type: 'any' },
              ruleId: { type: 'string' },
              severity: { type: 'string', enum: ['error', 'warning'] }
            }
          }
        },
        warnings: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              code: { type: 'string' },
              message: { type: 'string' },
              field: { type: 'string' },
              value: { type: 'any' },
              ruleId: { type: 'string' },
              severity: { type: 'string', enum: ['error', 'warning'] }
            }
          }
        },
        performance: {
          type: 'object',
          properties: {
            executionTime: { type: 'number' },
            rulesEvaluated: { type: 'number' },
            cacheHits: { type: 'number' }
          }
        }
      }
    }
  })
  async validateCampaign(@Body() validateDto: ValidateCampaignDto) {
    const context: ValidationContext = {
      industryId: validateDto.industryId,
      campaignId: validateDto.campaignId,
      productIds: validateDto.productIds,
      incentivePercentage: validateDto.incentivePercentage,
      incentiveValue: validateDto.incentiveValue,
      productCount: validateDto.productCount,
      validityDays: validateDto.validityDays,
      cycleId: validateDto.cycleId,
    };

    return this.validationService.validateCampaign(context);
  }

  @Post('batch')
  @ApiOperation({ summary: 'Validação em lote de múltiplas campanhas' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultados das validações em lote',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          campaignId: { type: 'string' },
          isValid: { type: 'boolean' },
          errors: { type: 'array' },
          warnings: { type: 'array' },
          performance: { type: 'object' }
        }
      }
    }
  })
  async validateBatch(@Body() campaigns: ValidateCampaignDto[]) {
    const results = [];

    for (const campaign of campaigns) {
      const context: ValidationContext = {
        industryId: campaign.industryId,
        campaignId: campaign.campaignId,
        productIds: campaign.productIds,
        incentivePercentage: campaign.incentivePercentage,
        incentiveValue: campaign.incentiveValue,
        productCount: campaign.productCount,
        validityDays: campaign.validityDays,
        cycleId: campaign.cycleId,
      };

      const result = await this.validationService.validateCampaign(context);
      
      results.push({
        campaignId: campaign.campaignId,
        ...result,
      });
    }

    return results;
  }

  @Get('cache/stats')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Estatísticas do cache de validação (apenas admins)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Estatísticas do cache',
    schema: {
      type: 'object',
      properties: {
        size: { type: 'number' },
        hitRate: { type: 'number' }
      }
    }
  })
  getCacheStats() {
    return this.validationService.getCacheStats();
  }

  @Delete('cache/:industryId?')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Limpar cache de validação (apenas admins)' })
  @ApiResponse({ status: 200, description: 'Cache limpo com sucesso' })
  clearCache(@Param('industryId') industryId?: string) {
    this.validationService.clearCache(industryId);
    return { 
      message: industryId 
        ? `Cache limpo para indústria ${industryId}` 
        : 'Cache global limpo com sucesso' 
    };
  }

  @Post('performance-test')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Teste de performance da validação (apenas admins)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultados do teste de performance',
    schema: {
      type: 'object',
      properties: {
        iterations: { type: 'number' },
        averageTime: { type: 'number' },
        minTime: { type: 'number' },
        maxTime: { type: 'number' },
        totalTime: { type: 'number' }
      }
    }
  })
  async performanceTest(@Body() testConfig: { industryId: string; iterations?: number }) {
    const iterations = testConfig.iterations || 100;
    const times: number[] = [];

    const context: ValidationContext = {
      industryId: testConfig.industryId,
      incentivePercentage: 15.5,
      incentiveValue: 2.50,
      productCount: 50,
      validityDays: 30,
    };

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      await this.validationService.validateCampaign(context);
      times.push(Date.now() - startTime);
    }

    const totalTime = times.reduce((sum, time) => sum + time, 0);
    const averageTime = totalTime / iterations;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    return {
      iterations,
      averageTime,
      minTime,
      maxTime,
      totalTime,
    };
  }
}
