import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private readonly configService: ConfigService) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    // Support both EMAIL_* and SMTP_* environment variables
    const emailUser = this.configService.get('EMAIL_USER') || this.configService.get('SMTP_USER');
    const emailPass = this.configService.get('EMAIL_PASS') || this.configService.get('SMTP_PASS');
    const emailHost = this.configService.get('EMAIL_HOST') || this.configService.get('SMTP_HOST', 'smtp.gmail.com');
    const emailPort = this.configService.get('EMAIL_PORT') || this.configService.get('SMTP_PORT', 587);

    if (!emailUser || !emailPass) {
      this.logger.warn('Email credentials not configured. Email sending will be mocked.');
      return;
    }

    this.transporter = nodemailer.createTransport({
      host: emailHost,
      port: parseInt(emailPort.toString(), 10),
      secure: false, // true for 465, false for other ports
      auth: {
        user: emailUser,
        pass: emailPass,
      },
    });

    this.logger.log(`Email transporter initialized successfully (${emailHost}:${emailPort})`);
  }

  async sendOtpEmail(email: string, code: string, expiresInMinutes: number = 5): Promise<boolean> {
    try {
      if (!this.transporter) {
        this.logger.warn(`Mock email: OTP ${code} would be sent to ${email}`);
        return true; // Mock success
      }

      const fromName = this.configService.get('EMAIL_FROM_NAME', 'Retail Media Platform');
      const fromAddress = this.configService.get('EMAIL_FROM_ADDRESS', this.configService.get('EMAIL_USER'));

      const mailOptions = {
        from: `"${fromName}" <${fromAddress}>`,
        to: email,
        subject: 'Código de Acesso - Retail Media Platform',
        html: this.generateOtpEmailTemplate(code, expiresInMinutes),
      };

      const result = await this.transporter.sendMail(mailOptions);
      this.logger.log(`OTP email sent successfully to ${email}. MessageId: ${result.messageId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send OTP email to ${email}:`, error);
      return false;
    }
  }

  private generateOtpEmailTemplate(code: string, expiresInMinutes: number): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Código de Acesso</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">🔐 Código de Acesso</h1>
          <p style="color: #f0f0f0; margin: 10px 0 0 0;">Retail Media Platform</p>
        </div>
        
        <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h2 style="color: #333; margin-top: 0;">Seu código de acesso:</h2>
          
          <div style="background: #f8f9fa; border: 2px dashed #667eea; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0;">
            <span style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px;">${code}</span>
          </div>
          
          <p style="margin: 20px 0;">Este código é válido por <strong>${expiresInMinutes} minutos</strong>.</p>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            Se você não solicitou este código, ignore este e-mail.
          </p>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          
          <p style="color: #999; font-size: 12px; text-align: center; margin: 0;">
            © 2025 Retail Media Platform - Sistema de Gestão de Campanhas
          </p>
        </div>
      </body>
      </html>
    `;
  }

  async testConnection(): Promise<boolean> {
    try {
      if (!this.transporter) {
        this.logger.warn('Email transporter not configured');
        return false;
      }

      await this.transporter.verify();
      this.logger.log('Email connection test successful');
      return true;
    } catch (error) {
      this.logger.error('Email connection test failed:', error);
      return false;
    }
  }
}
