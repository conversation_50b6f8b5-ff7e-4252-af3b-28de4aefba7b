import {
  Injectable,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Touchpoint, TouchpointType, TouchpointChannel, DeviceType } from '../database/entities/touchpoint.entity';

@Injectable()
export class TouchpointService {
  private readonly logger = new Logger(TouchpointService.name);

  constructor(
    @InjectRepository(Touchpoint)
    private readonly touchpointRepository: Repository<Touchpoint>,
  ) {}

  async createTouchpoint(touchpointData: {
    userId: string;
    campaignId?: string;
    sessionId?: string;
    type: TouchpointType;
    channel: TouchpointChannel;
    url?: string;
    referrer?: string;
    source?: string;
    medium?: string;
    campaign?: string;
    content?: string;
    term?: string;
    deviceType?: DeviceType;
    userAgent?: string;
    ipAddress?: string;
    customAttributes?: Record<string, any>;
    value?: number;
    currency?: string;
  }): Promise<Touchpoint> {
    const touchpoint = this.touchpointRepository.create(
      Touchpoint.createFromEvent(touchpointData)
    );

    // Add geolocation if IP address is provided
    if (touchpointData.ipAddress) {
      const geoData = await this.getGeolocationFromIP(touchpointData.ipAddress);
      if (geoData) {
        touchpoint.countryCode = geoData.countryCode;
        touchpoint.city = geoData.city;
        touchpoint.latitude = geoData.latitude;
        touchpoint.longitude = geoData.longitude;
      }
    }

    const savedTouchpoint = await this.touchpointRepository.save(touchpoint);
    
    this.logger.log(`Created touchpoint ${savedTouchpoint.id} for user ${touchpointData.userId}`);
    return savedTouchpoint;
  }

  async getTouchpoints(
    userId: string,
    startDate?: Date,
    endDate?: Date,
    campaignId?: string,
    limit: number = 100
  ): Promise<Touchpoint[]> {
    const queryBuilder = this.touchpointRepository.createQueryBuilder('touchpoint')
      .where('touchpoint.userId = :userId', { userId })
      .orderBy('touchpoint.timestamp', 'DESC')
      .limit(limit);

    if (startDate) {
      queryBuilder.andWhere('touchpoint.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('touchpoint.timestamp <= :endDate', { endDate });
    }

    if (campaignId) {
      queryBuilder.andWhere('touchpoint.campaignId = :campaignId', { campaignId });
    }

    return queryBuilder.getMany();
  }

  async getUserJourney(
    userId: string,
    sessionId?: string,
    windowDays: number = 30
  ): Promise<{
    touchpoints: Touchpoint[];
    journeyStats: {
      totalTouchpoints: number;
      uniqueChannels: number;
      uniqueCampaigns: number;
      journeyDuration: number; // in milliseconds
      firstTouch: Date | null;
      lastTouch: Date | null;
    };
  }> {
    const windowStart = new Date(Date.now() - windowDays * 24 * 60 * 60 * 1000);
    
    const queryBuilder = this.touchpointRepository.createQueryBuilder('touchpoint')
      .where('touchpoint.userId = :userId', { userId })
      .andWhere('touchpoint.timestamp >= :windowStart', { windowStart })
      .orderBy('touchpoint.timestamp', 'ASC');

    if (sessionId) {
      queryBuilder.andWhere('touchpoint.sessionId = :sessionId', { sessionId });
    }

    const touchpoints = await queryBuilder.getMany();

    const uniqueChannels = new Set(touchpoints.map(t => t.channel)).size;
    const uniqueCampaigns = new Set(touchpoints.map(t => t.campaignId).filter(Boolean)).size;
    const firstTouch = touchpoints.length > 0 ? touchpoints[0].timestamp : null;
    const lastTouch = touchpoints.length > 0 ? touchpoints[touchpoints.length - 1].timestamp : null;
    const journeyDuration = firstTouch && lastTouch 
      ? lastTouch.getTime() - firstTouch.getTime() 
      : 0;

    return {
      touchpoints,
      journeyStats: {
        totalTouchpoints: touchpoints.length,
        uniqueChannels,
        uniqueCampaigns,
        journeyDuration,
        firstTouch,
        lastTouch,
      },
    };
  }

  async getTouchpointAnalytics(
    campaignId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalTouchpoints: number;
    byType: Record<TouchpointType, number>;
    byChannel: Record<TouchpointChannel, number>;
    byDevice: Record<DeviceType, number>;
    topSources: Array<{ source: string; count: number }>;
    topCampaigns: Array<{ campaignId: string; count: number }>;
    hourlyDistribution: Array<{ hour: number; count: number }>;
  }> {
    const queryBuilder = this.touchpointRepository.createQueryBuilder('touchpoint');

    if (campaignId) {
      queryBuilder.where('touchpoint.campaignId = :campaignId', { campaignId });
    }

    if (startDate) {
      queryBuilder.andWhere('touchpoint.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('touchpoint.timestamp <= :endDate', { endDate });
    }

    const touchpoints = await queryBuilder.getMany();

    const totalTouchpoints = touchpoints.length;

    // Group by type
    const byType = touchpoints.reduce((acc, t) => {
      acc[t.type] = (acc[t.type] || 0) + 1;
      return acc;
    }, {} as Record<TouchpointType, number>);

    // Group by channel
    const byChannel = touchpoints.reduce((acc, t) => {
      acc[t.channel] = (acc[t.channel] || 0) + 1;
      return acc;
    }, {} as Record<TouchpointChannel, number>);

    // Group by device
    const byDevice = touchpoints.reduce((acc, t) => {
      acc[t.deviceType] = (acc[t.deviceType] || 0) + 1;
      return acc;
    }, {} as Record<DeviceType, number>);

    // Top sources
    const sourceCount = touchpoints.reduce((acc, t) => {
      if (t.source) {
        acc[t.source] = (acc[t.source] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const topSources = Object.entries(sourceCount)
      .map(([source, count]) => ({ source, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Top campaigns
    const campaignCount = touchpoints.reduce((acc, t) => {
      if (t.campaignId) {
        acc[t.campaignId] = (acc[t.campaignId] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const topCampaigns = Object.entries(campaignCount)
      .map(([campaignId, count]) => ({ campaignId, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Hourly distribution
    const hourlyCount = touchpoints.reduce((acc, t) => {
      const hour = t.timestamp.getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const hourlyDistribution = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      count: hourlyCount[hour] || 0,
    }));

    return {
      totalTouchpoints,
      byType,
      byChannel,
      byDevice,
      topSources,
      topCampaigns,
      hourlyDistribution,
    };
  }

  async createBulkTouchpoints(
    touchpointsData: Array<{
      userId: string;
      campaignId?: string;
      sessionId?: string;
      type: TouchpointType;
      channel: TouchpointChannel;
      timestamp?: Date;
      url?: string;
      referrer?: string;
      source?: string;
      medium?: string;
      campaign?: string;
      content?: string;
      term?: string;
      deviceType?: DeviceType;
      userAgent?: string;
      ipAddress?: string;
      customAttributes?: Record<string, any>;
      value?: number;
      currency?: string;
    }>
  ): Promise<Touchpoint[]> {
    if (touchpointsData.length === 0) {
      throw new BadRequestException('No touchpoints data provided');
    }

    if (touchpointsData.length > 1000) {
      throw new BadRequestException('Maximum 1000 touchpoints per batch');
    }

    const touchpoints = touchpointsData.map(data => {
      const touchpoint = Touchpoint.createFromEvent(data);
      if (data.timestamp) {
        touchpoint.timestamp = data.timestamp;
      }
      return this.touchpointRepository.create(touchpoint);
    });

    const savedTouchpoints = await this.touchpointRepository.save(touchpoints);
    
    this.logger.log(`Created ${savedTouchpoints.length} touchpoints in bulk`);
    return savedTouchpoints;
  }

  private async getGeolocationFromIP(ipAddress: string): Promise<{
    countryCode: string;
    city: string;
    latitude: number;
    longitude: number;
  } | null> {
    // Mock geolocation service
    // In a real implementation, you would use a service like MaxMind, IPStack, etc.
    
    // Simple mock based on IP patterns
    if (ipAddress.startsWith('192.168.') || ipAddress.startsWith('10.') || ipAddress.startsWith('172.')) {
      // Private IP - assume local/Brazil
      return {
        countryCode: 'BR',
        city: 'São Paulo',
        latitude: -23.5505,
        longitude: -46.6333,
      };
    }

    // Mock some international IPs
    const mockGeoData = [
      { countryCode: 'BR', city: 'São Paulo', latitude: -23.5505, longitude: -46.6333 },
      { countryCode: 'BR', city: 'Rio de Janeiro', latitude: -22.9068, longitude: -43.1729 },
      { countryCode: 'BR', city: 'Brasília', latitude: -15.7942, longitude: -47.8822 },
      { countryCode: 'US', city: 'New York', latitude: 40.7128, longitude: -74.0060 },
      { countryCode: 'US', city: 'Los Angeles', latitude: 34.0522, longitude: -118.2437 },
    ];

    // Simple hash to get consistent results for the same IP
    const hash = ipAddress.split('.').reduce((acc, part) => acc + parseInt(part, 10), 0);
    return mockGeoData[hash % mockGeoData.length];
  }

  async deleteTouchpoint(touchpointId: string): Promise<void> {
    const result = await this.touchpointRepository.delete(touchpointId);
    
    if (result.affected === 0) {
      throw new BadRequestException('Touchpoint not found');
    }

    this.logger.log(`Deleted touchpoint ${touchpointId}`);
  }

  async cleanupOldTouchpoints(retentionDays: number = 365): Promise<{ deleted: number }> {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
    
    const result = await this.touchpointRepository
      .createQueryBuilder()
      .delete()
      .where('timestamp < :cutoffDate', { cutoffDate })
      .execute();

    this.logger.log(`Cleaned up ${result.affected} old touchpoints older than ${retentionDays} days`);
    
    return { deleted: result.affected || 0 };
  }
}
