import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixProductUuidColumns1703000000006 implements MigrationInterface {
  name = 'FixProductUuidColumns1703000000006';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if columns are already UUID type
    const categoryIdColumn = await queryRunner.query(`
      SELECT data_type 
      FROM information_schema.columns 
      WHERE table_name = 'products' 
      AND column_name = 'categoryId'
    `);

    const subcategoryIdColumn = await queryRunner.query(`
      SELECT data_type 
      FROM information_schema.columns 
      WHERE table_name = 'products' 
      AND column_name = 'subcategoryId'
    `);

    // Convert categoryId from VARCHAR to UUID if needed
    if (categoryIdColumn[0]?.data_type !== 'uuid') {
      await queryRunner.query(`
        ALTER TABLE products 
        ALTER COLUMN "categoryId" TYPE uuid USING "categoryId"::uuid
      `);
    }

    // Convert subcategoryId from VARCHAR to UUID if needed
    if (subcategoryIdColumn[0]?.data_type !== 'uuid') {
      await queryRunner.query(`
        ALTER TABLE products 
        ALTER COLUMN "subcategoryId" TYPE uuid USING "subcategoryId"::uuid
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert categoryId to VARCHAR
    await queryRunner.query(`
      ALTER TABLE products 
      ALTER COLUMN "categoryId" TYPE varchar USING "categoryId"::varchar
    `);

    // Revert subcategoryId to VARCHAR
    await queryRunner.query(`
      ALTER TABLE products 
      ALTER COLUMN "subcategoryId" TYPE varchar USING "subcategoryId"::varchar
    `);
  }
}

