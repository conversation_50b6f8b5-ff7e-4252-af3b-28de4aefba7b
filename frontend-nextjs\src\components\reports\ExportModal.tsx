'use client';

import { useState } from 'react';

import { Download, X, FileText, FileSpreadsheet, Loader2, CheckCircle } from 'lucide-react';
import toast from 'react-hot-toast';

export interface ExportOptions {
  format: 'excel' | 'pdf';
  reportType: 'campaign' | 'product' | 'incentive' | 'user';
  includeCharts: boolean;
  includeRawData: boolean;
}

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (options: ExportOptions) => Promise<void>;
  filters: any;
  period: any;
}

export default function ExportModal({ 
  isOpen, 
  onClose, 
  onExport, 
  filters, 
  period 
}: ExportModalProps) {
  const [selectedFormat, setSelectedFormat] = useState<'excel' | 'pdf'>('excel');
  const [selectedReportType, setSelectedReportType] = useState<'campaign' | 'product' | 'incentive' | 'user'>('campaign');
  const [includeCharts, setIncludeCharts] = useState(true);
  const [includeRawData, setIncludeRawData] = useState(true);
  const [isExporting, setIsExporting] = useState(false);

  const formatOptions = [
    {
      value: 'excel' as const,
      label: 'Excel (XLSX)',
      description: 'Planilha com dados estruturados e gráficos',
      icon: FileSpreadsheet,
      color: 'text-green-600'
    },
    {
      value: 'pdf' as const,
      label: 'PDF',
      description: 'Relatório formatado para impressão',
      icon: FileText,
      color: 'text-red-600'
    }
  ];

  const reportTypeOptions = [
    {
      value: 'campaign' as const,
      label: 'Performance de Campanhas',
      description: 'Métricas e análise de campanhas'
    },
    {
      value: 'product' as const,
      label: 'Performance de Produtos',
      description: 'Análise de vendas e produtos'
    },
    {
      value: 'incentive' as const,
      label: 'Análise de Incentivos',
      description: 'ROI e efetividade dos incentivos'
    },
    {
      value: 'user' as const,
      label: 'Atividade de Usuários',
      description: 'Comportamento e engajamento'
    }
  ];

  const handleExport = async () => {
    setIsExporting(true);
    try {
      await onExport({
        format: selectedFormat,
        reportType: selectedReportType,
        includeCharts,
        includeRawData
      });
      
      toast.success('Relatório exportado com sucesso!');
      onClose();
    } catch (error) {
      console.error('Erro ao exportar relatório:', error);
      toast.error('Erro ao exportar relatório. Tente novamente.');
    } finally {
      setIsExporting(false);
    }
  };

  const getFilterSummary = () => {
    const parts = [];

    if (filters.campaignIds?.length > 0) {
      parts.push(`${filters.campaignIds.length} campanhas selecionadas`);
    }

    if (filters.status?.length > 0) {
      const statusLabels = {
        'active': 'Ativas',
        'ended': 'Concluídas',
        'paused': 'Pausadas',
        'draft': 'Rascunho',
        'scheduled': 'Agendadas',
        'reactivated': 'Reativadas'
      };
      const translatedStatus = filters.status.map(status =>
        statusLabels[status as keyof typeof statusLabels] || status
      );
      parts.push(`Status: ${translatedStatus.join(', ')}`);
    }

    if (period.type === 'preset' && period.preset !== 'all') {
      const presetLabels = {
        '1d': 'Último dia',
        '7d': 'Últimos 7 dias',
        '30d': 'Últimos 30 dias',
        '60d': 'Últimos 60 dias',
        '90d': 'Últimos 90 dias'
      };
      parts.push(`Período: ${presetLabels[period.preset as keyof typeof presetLabels]}`);
    } else if (period.type === 'custom') {
      parts.push(`Período: ${new Date(period.startDate).toLocaleDateString()} - ${new Date(period.endDate).toLocaleDateString()}`);
    }

    return parts.length > 0 ? parts.join(' | ') : 'Todos os dados';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex flex-row items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <Download className="h-5 w-5 mr-2" />
            Exportar Relatório
          </h2>
          <button
            onClick={onClose}
            disabled={isExporting}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
            aria-label="Close"
          >
            <X className="h-4 w-4 text-gray-500" />
          </button>
        </div>

        <div className="p-4 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Format Selection */}
          <div className="space-y-3">
            <label className="text-base font-medium text-gray-900">Formato de Exportação</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {formatOptions.map(option => {
                const Icon = option.icon;
                return (
                  <div
                    key={option.value}
                    className={`
                      flex items-center p-4 rounded-lg border cursor-pointer transition-colors
                      ${selectedFormat === option.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }
                    `}
                    onClick={() => setSelectedFormat(option.value)}
                  >
                    <Icon className={`h-6 w-6 mr-3 ${option.color}`} />
                    <div className="flex-1">
                      <div className="font-medium text-sm">{option.label}</div>
                      <div className="text-xs text-gray-500">{option.description}</div>
                    </div>
                    {selectedFormat === option.value && (
                      <CheckCircle className="h-4 w-4 text-blue-600" />
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Report Type Selection */}
          <div className="space-y-3">
            <label className="text-base font-medium text-gray-900">Tipo de Relatório</label>
            <div className="space-y-2">
              {reportTypeOptions.map(option => (
                <div
                  key={option.value}
                  className={`
                    flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-colors
                    ${selectedReportType === option.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }
                  `}
                  onClick={() => setSelectedReportType(option.value)}
                >
                  <div className="flex-1">
                    <div className="font-medium text-sm">{option.label}</div>
                    <div className="text-xs text-gray-500">{option.description}</div>
                  </div>
                  {selectedReportType === option.value && (
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Export Options */}
          <div className="space-y-3">
            <label className="text-base font-medium text-gray-900">Opções de Exportação</label>
            <div className="space-y-3">
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={includeCharts}
                  onChange={(e) => setIncludeCharts(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <div>
                  <div className="text-sm font-medium">Incluir Gráficos</div>
                  <div className="text-xs text-gray-500">
                    Adiciona visualizações e gráficos ao relatório
                  </div>
                </div>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={includeRawData}
                  onChange={(e) => setIncludeRawData(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <div>
                  <div className="text-sm font-medium">Incluir Dados Brutos</div>
                  <div className="text-xs text-gray-500">
                    Adiciona tabelas detalhadas com todos os dados
                  </div>
                </div>
              </label>
            </div>
          </div>

          {/* Filter Summary */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="text-sm font-medium text-gray-700 mb-2">Filtros Aplicados:</div>
            <div className="text-sm text-gray-600">{getFilterSummary()}</div>
          </div>

        </div>

        {/* Actions - Fixed at bottom */}
        <div className="flex justify-end space-x-2 p-4 border-t bg-gray-50">
          <button
            onClick={onClose}
            disabled={isExporting}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            Cancelar
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
          >
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exportando...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
