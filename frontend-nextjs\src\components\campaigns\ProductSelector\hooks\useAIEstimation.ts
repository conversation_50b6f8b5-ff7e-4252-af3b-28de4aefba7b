import { useState, useEffect, useCallback } from 'react';
import { Product, AIEstimation } from '../types';
import toast from 'react-hot-toast';

const ESTIMATION_DEBOUNCE_MS = 500;

export function useAIEstimation(selectedProducts: Product[], incentiveValue: number) {
  const [estimation, setEstimation] = useState<AIEstimation | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const generateEstimation = useCallback(async () => {
    if (selectedProducts.length === 0 || incentiveValue <= 0) {
      setEstimation(null);
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const baseAudience = selectedProducts.length * 1000;
      const incentiveMultiplier = Math.min(incentiveValue / 10, 5);
      const estimatedAudience = Math.floor(
        baseAudience * incentiveMultiplier * (0.8 + Math.random() * 0.4)
      );
      const estimatedCost = estimatedAudience * incentiveValue;
      const confidence = Math.min(95, 60 + (selectedProducts.length * 5) + (incentiveValue > 5 ? 20 : 0));
      
      // Check for anomalies
      const normalCost = estimatedAudience * 2;
      const isAnomalous = estimatedCost > normalCost * 10;
      
      if (isAnomalous) {
        toast.error('Padrão anômalo detectado - custo muito alto para o incentivo');
        return;
      }

      setEstimation({
        estimatedAudience,
        estimatedCost,
        confidence,
        isAnomalous,
        factors: [
          `${selectedProducts.length} produtos selecionados`,
          `Incentivo de R$ ${incentiveValue.toFixed(2)}`,
          'Dados históricos da categoria',
          'Sazonalidade atual'
        ]
      });
    } catch (error) {
      toast.error('Serviço de IA temporariamente indisponível');
    } finally {
      setIsLoading(false);
    }
  }, [selectedProducts, incentiveValue]);

  useEffect(() => {
    const timer = setTimeout(generateEstimation, ESTIMATION_DEBOUNCE_MS);
    return () => clearTimeout(timer);
  }, [generateEstimation]);

  return {
    estimation,
    isLoading,
    regenerateEstimation: generateEstimation
  };
}
