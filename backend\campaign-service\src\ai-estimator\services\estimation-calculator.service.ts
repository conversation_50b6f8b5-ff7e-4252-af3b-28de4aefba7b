import { Injectable } from '@nestjs/common';
import { EstimationResult, EstimationFactors, EstimationInput } from '../domain/estimation-result';
import { ProductForEstimation } from '../domain/product';

/**
 * Service responsible for calculating campaign performance estimations
 * Follows Single Responsibility Principle and Open/Closed Principle
 */
@Injectable()
export class EstimationCalculatorService {
  private readonly BASE_ENGAGEMENT_RATE = 0.15;
  private readonly DEFAULT_COST_PERCENTAGE = 0.3;

  /**
   * Calculate campaign performance estimation
   * @param input - Estimation input parameters
   * @param products - Products to analyze
   * @returns Estimation result
   */
  calculateEstimation(
    input: EstimationInput,
    products: ProductForEstimation[]
  ): EstimationResult {
    const factors = this.calculateEstimationFactors(products, input);
    const baseMetrics = this.calculateBaseMetrics(products, input);
    
    const reach = this.calculateReach(baseMetrics, factors);
    const engagement = this.calculateEngagement(reach, factors);
    const conversions = this.calculateConversions(engagement, factors, products);
    const revenue = this.calculateRevenue(conversions, products);
    const roi = this.calculateROI(revenue, input.budget);
    const confidence = this.calculateConfidence(factors, products);

    return {
      reach: Math.round(reach),
      engagement: Math.round(engagement),
      conversions: Math.round(conversions),
      revenue: Math.round(revenue * 100) / 100,
      roi: Math.round(roi * 100) / 100,
      confidence: Math.round(confidence),
      factors,
      recommendations: this.generateRecommendations(factors, { reach, engagement, conversions, revenue, roi }),
      warnings: this.generateWarnings(factors, products)
    };
  }

  private calculateEstimationFactors(
    products: ProductForEstimation[],
    input: EstimationInput
  ): EstimationFactors {
    const productPopularity = this.calculateProductPopularity(products);
    const incentiveAttractiveness = this.calculateIncentiveAttractiveness(input.incentivePercentage);
    const seasonality = this.calculateSeasonalityFactor();
    const marketTrends = this.calculateMarketTrends(products);
    const historicalPerformance = this.calculateHistoricalPerformance(products);

    return {
      productPopularity,
      incentiveAttractiveness,
      seasonality,
      marketTrends,
      historicalPerformance
    };
  }

  private calculateBaseMetrics(products: ProductForEstimation[], input: EstimationInput) {
    const averagePrice = products.reduce((sum, p) => sum + p.price, 0) / products.length;
    const averageRating = products.reduce((sum, p) => sum + p.averageRating, 0) / products.length;
    const totalSales = products.reduce((sum, p) => sum + p.salesCount, 0);

    return { averagePrice, averageRating, totalSales };
  }

  private calculateReach(baseMetrics: any, factors: EstimationFactors): number {
    const baseReach = Math.min(baseMetrics.totalSales * 2, 50000); // Cap at 50k
    return baseReach * factors.productPopularity * factors.incentiveAttractiveness;
  }

  private calculateEngagement(reach: number, factors: EstimationFactors): number {
    return reach * this.BASE_ENGAGEMENT_RATE * factors.incentiveAttractiveness * factors.marketTrends;
  }

  private calculateConversions(
    engagement: number, 
    factors: EstimationFactors, 
    products: ProductForEstimation[]
  ): number {
    const baseConversionRate = this.calculateBaseConversionRate(products);
    return engagement * baseConversionRate * factors.historicalPerformance;
  }

  private calculateRevenue(conversions: number, products: ProductForEstimation[]): number {
    const averagePrice = products.reduce((sum, p) => sum + p.price, 0) / products.length;
    return conversions * averagePrice;
  }

  private calculateROI(revenue: number, budget?: number): number {
    const estimatedCost = budget || (revenue * this.DEFAULT_COST_PERCENTAGE);
    if (estimatedCost === 0) return 0;
    return ((revenue - estimatedCost) / estimatedCost) * 100;
  }

  private calculateProductPopularity(products: ProductForEstimation[]): number {
    if (products.length === 0) return 1;
    
    const averagePopularity = products.reduce((sum, p) => sum + p.popularityScore, 0) / products.length;
    return Math.max(0.5, Math.min(2.0, averagePopularity * 2)); // Scale between 0.5 and 2.0
  }

  private calculateIncentiveAttractiveness(incentivePercentage: number): number {
    // Logarithmic scale for diminishing returns
    return Math.min(2.0, Math.log(incentivePercentage + 1) / Math.log(21)); // Cap at 20% incentive
  }

  private calculateSeasonalityFactor(): number {
    const month = new Date().getMonth();
    const seasonalFactors = [0.8, 0.8, 0.9, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.1, 1.2, 1.2];
    return seasonalFactors[month];
  }

  private calculateMarketTrends(products: ProductForEstimation[]): number {
    // Simplified market trend calculation based on product categories
    const trendingCategories = ['Higiene e Beleza', 'Alimentação', 'Tecnologia'];
    const trendingCount = products.filter(p => trendingCategories.includes(p.category)).length;
    const trendFactor = trendingCount / products.length;
    return 0.8 + (trendFactor * 0.4); // Range: 0.8 to 1.2
  }

  private calculateHistoricalPerformance(products: ProductForEstimation[]): number {
    const highPerformingCount = products.filter(p => p.isHighPerforming()).length;
    if (products.length === 0) return 1;
    
    const performanceRatio = highPerformingCount / products.length;
    return 0.7 + (performanceRatio * 0.6); // Range: 0.7 to 1.3
  }

  private calculateBaseConversionRate(products: ProductForEstimation[]): number {
    const averageRating = products.reduce((sum, p) => sum + p.averageRating, 0) / products.length;
    const baseRate = 0.02; // 2% base conversion
    const ratingMultiplier = averageRating / 5; // 0 to 1
    return baseRate * (0.5 + ratingMultiplier); // Range: 1% to 3%
  }

  private calculateConfidence(factors: EstimationFactors, products: ProductForEstimation[]): number {
    let confidence = 60; // Base confidence

    // Product count factor
    confidence += Math.min(products.length * 2, 20);

    // Historical data quality
    const highPerformingRatio = products.filter(p => p.isHighPerforming()).length / products.length;
    confidence += highPerformingRatio * 15;

    // Seasonality confidence
    if (factors.seasonality > 0.8 && factors.seasonality < 1.2) {
      confidence += 5;
    }

    return Math.min(95, Math.max(30, confidence));
  }

  private generateRecommendations(factors: EstimationFactors, metrics: any): string[] {
    const recommendations: string[] = [];

    if (factors.incentiveAttractiveness < 1.0) {
      recommendations.push('Considere aumentar o incentivo para melhorar a atratividade');
    }

    if (factors.productPopularity > 1.2) {
      recommendations.push('Produtos populares selecionados - excelente para alcance');
    }

    if (metrics.roi > 100) {
      recommendations.push('ROI estimado excelente - campanha muito promissora');
    }

    if (factors.seasonality > 1.1) {
      recommendations.push('Período favorável devido à sazonalidade');
    }

    return recommendations;
  }

  private generateWarnings(factors: EstimationFactors, products: ProductForEstimation[]): string[] {
    const warnings: string[] = [];

    if (factors.seasonality < 0.9) {
      warnings.push('Período pode não ser ideal devido à sazonalidade');
    }

    if (factors.productPopularity < 0.8) {
      warnings.push('Produtos com baixa popularidade podem afetar o desempenho');
    }

    if (products.length < 3) {
      warnings.push('Campanhas com mais produtos tendem a ter melhor performance');
    }

    const lowRatedProducts = products.filter(p => p.averageRating < 3.5).length;
    if (lowRatedProducts > 0) {
      warnings.push(`${lowRatedProducts} produto(s) com avaliação baixa podem impactar resultados`);
    }

    return warnings;
  }
}
