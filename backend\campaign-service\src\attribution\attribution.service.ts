import {
  Injectable,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';

import { Attribution } from '../database/entities/attribution.entity';
import { AttributionModelConfig, AttributionModel } from '../database/entities/attribution-model.entity';
import { AttributionEngineService } from './attribution-engine.service';
import { TouchpointService } from './touchpoint.service';
import { ConversionService } from './conversion.service';

@Injectable()
export class AttributionService {
  private readonly logger = new Logger(AttributionService.name);

  constructor(
    @InjectRepository(Attribution)
    private readonly attributionRepository: Repository<Attribution>,
    @InjectRepository(AttributionModelConfig)
    private readonly modelConfigRepository: Repository<AttributionModelConfig>,
    private readonly attributionEngineService: AttributionEngineService,
    private readonly touchpointService: TouchpointService,
    private readonly conversionService: ConversionService,
  ) {}

  async onModuleInit() {
    await this.initializeDefaultModels();
  }

  private async initializeDefaultModels(): Promise<void> {
    const existingModels = await this.modelConfigRepository.count();
    
    if (existingModels === 0) {
      const defaultModels = AttributionModelConfig.getDefaultModels();
      
      for (const modelData of defaultModels) {
        const model = this.modelConfigRepository.create(modelData);
        await this.modelConfigRepository.save(model);
      }
      
      this.logger.log('Initialized default attribution models');
    }
  }

  async getAttributionModels(): Promise<AttributionModelConfig[]> {
    return this.modelConfigRepository.find({
      where: { isActive: true },
      order: { priority: 'DESC' },
    });
  }

  async createAttributionModel(modelData: {
    name: string;
    displayName: string;
    description?: string;
    type: AttributionModel;
    lookbackWindowDays?: number;
    configuration?: Record<string, any>;
    weights?: Record<string, number>;
    decayRate?: number;
    priority?: number;
  }): Promise<AttributionModelConfig> {
    // Check if model name already exists
    const existing = await this.modelConfigRepository.findOne({
      where: { name: modelData.name },
    });

    if (existing) {
      throw new BadRequestException(`Attribution model with name '${modelData.name}' already exists`);
    }

    const model = this.modelConfigRepository.create({
      ...modelData,
      lookbackWindowDays: modelData.lookbackWindowDays || 7,
      priority: modelData.priority || 50,
      decayRate: modelData.decayRate || 0.5,
    });

    const savedModel = await this.modelConfigRepository.save(model);
    
    this.logger.log(`Created attribution model: ${savedModel.name}`);
    return savedModel;
  }

  async updateAttributionModel(
    modelId: string,
    updateData: Partial<{
      displayName: string;
      description: string;
      isActive: boolean;
      isDefault: boolean;
      lookbackWindowDays: number;
      configuration: Record<string, any>;
      weights: Record<string, number>;
      decayRate: number;
      priority: number;
    }>
  ): Promise<AttributionModelConfig> {
    const model = await this.modelConfigRepository.findOne({
      where: { id: modelId },
    });

    if (!model) {
      throw new BadRequestException('Attribution model not found');
    }

    // If setting as default, unset other defaults
    if (updateData.isDefault) {
      await this.modelConfigRepository.update(
        { isDefault: true },
        { isDefault: false }
      );
    }

    Object.assign(model, updateData);
    const updatedModel = await this.modelConfigRepository.save(model);
    
    this.logger.log(`Updated attribution model: ${model.name}`);
    return updatedModel;
  }

  async getAttributions(
    userId?: string,
    campaignId?: string,
    conversionId?: string,
    model?: AttributionModel,
    startDate?: Date,
    endDate?: Date,
    limit: number = 100,
    offset: number = 0
  ): Promise<{ attributions: Attribution[]; total: number }> {
    const queryBuilder = this.attributionRepository.createQueryBuilder('attribution')
      .leftJoinAndSelect('attribution.conversion', 'conversion')
      .leftJoinAndSelect('attribution.touchpoint', 'touchpoint')
      .leftJoinAndSelect('attribution.campaign', 'campaign')
      .orderBy('attribution.createdAt', 'DESC')
      .limit(limit)
      .offset(offset);

    if (userId) {
      queryBuilder.andWhere('attribution.userId = :userId', { userId });
    }

    if (campaignId) {
      queryBuilder.andWhere('attribution.campaignId = :campaignId', { campaignId });
    }

    if (conversionId) {
      queryBuilder.andWhere('attribution.conversionId = :conversionId', { conversionId });
    }

    if (model) {
      queryBuilder.andWhere('attribution.model = :model', { model });
    }

    if (startDate) {
      queryBuilder.andWhere('conversion.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('conversion.timestamp <= :endDate', { endDate });
    }

    const [attributions, total] = await queryBuilder.getManyAndCount();

    return { attributions, total };
  }

  async getAttributionReport(
    campaignId?: string,
    startDate?: Date,
    endDate?: Date,
    model?: AttributionModel
  ): Promise<{
    summary: {
      totalAttributedValue: number;
      totalConversions: number;
      totalTouchpoints: number;
      averageJourneyLength: number;
    };
    byCampaign: Array<{
      campaignId: string;
      campaignName?: string;
      attributedValue: number;
      conversions: number;
      touchpoints: number;
      averageWeight: number;
    }>;
    byChannel: Array<{
      channel: string;
      attributedValue: number;
      conversions: number;
      touchpoints: number;
      averageWeight: number;
    }>;
    byModel: Array<{
      model: AttributionModel;
      attributedValue: number;
      conversions: number;
      touchpoints: number;
    }>;
    journeyAnalysis: {
      averageJourneyLength: number;
      journeyLengthDistribution: Array<{ length: number; count: number }>;
      averageTimeBetweenTouchpoints: number;
      topJourneyPaths: Array<{ path: string; count: number; value: number }>;
    };
  }> {
    const summary = await this.attributionEngineService.getAttributionSummary(
      campaignId,
      startDate,
      endDate,
      model
    );

    // Get detailed attributions for analysis
    const { attributions } = await this.getAttributions(
      undefined,
      campaignId,
      undefined,
      model,
      startDate,
      endDate,
      10000 // Large limit for analysis
    );

    // Group by campaign
    const campaignStats = new Map();
    attributions.forEach(attr => {
      if (!attr.campaignId) return;
      
      const existing = campaignStats.get(attr.campaignId) || {
        campaignId: attr.campaignId,
        campaignName: attr.campaign?.name,
        attributedValue: 0,
        conversions: new Set(),
        touchpoints: new Set(),
        totalWeight: 0,
        weightCount: 0,
      };
      
      existing.attributedValue += attr.attributedValue;
      existing.conversions.add(attr.conversionId);
      existing.touchpoints.add(attr.touchpointId);
      existing.totalWeight += attr.weight;
      existing.weightCount += 1;
      
      campaignStats.set(attr.campaignId, existing);
    });

    const byCampaign = Array.from(campaignStats.values())
      .map(stats => ({
        campaignId: stats.campaignId,
        campaignName: stats.campaignName,
        attributedValue: stats.attributedValue,
        conversions: stats.conversions.size,
        touchpoints: stats.touchpoints.size,
        averageWeight: stats.weightCount > 0 ? stats.totalWeight / stats.weightCount : 0,
      }))
      .sort((a, b) => b.attributedValue - a.attributedValue);

    // Group by channel
    const channelStats = new Map();
    attributions.forEach(attr => {
      if (!attr.touchpoint?.channel) return;
      
      const channel = attr.touchpoint.channel;
      const existing = channelStats.get(channel) || {
        channel,
        attributedValue: 0,
        conversions: new Set(),
        touchpoints: new Set(),
        totalWeight: 0,
        weightCount: 0,
      };
      
      existing.attributedValue += attr.attributedValue;
      existing.conversions.add(attr.conversionId);
      existing.touchpoints.add(attr.touchpointId);
      existing.totalWeight += attr.weight;
      existing.weightCount += 1;
      
      channelStats.set(channel, existing);
    });

    const byChannel = Array.from(channelStats.values())
      .map(stats => ({
        channel: stats.channel,
        attributedValue: stats.attributedValue,
        conversions: stats.conversions.size,
        touchpoints: stats.touchpoints.size,
        averageWeight: stats.weightCount > 0 ? stats.totalWeight / stats.weightCount : 0,
      }))
      .sort((a, b) => b.attributedValue - a.attributedValue);

    // Group by model
    const modelStats = new Map();
    attributions.forEach(attr => {
      const existing = modelStats.get(attr.model) || {
        model: attr.model,
        attributedValue: 0,
        conversions: new Set(),
        touchpoints: new Set(),
      };
      
      existing.attributedValue += attr.attributedValue;
      existing.conversions.add(attr.conversionId);
      existing.touchpoints.add(attr.touchpointId);
      
      modelStats.set(attr.model, existing);
    });

    const byModel = Array.from(modelStats.values())
      .map(stats => ({
        model: stats.model,
        attributedValue: stats.attributedValue,
        conversions: stats.conversions.size,
        touchpoints: stats.touchpoints.size,
      }))
      .sort((a, b) => b.attributedValue - a.attributedValue);

    // Journey analysis
    const journeyLengths = attributions.map(attr => attr.touchpointCount);
    const averageJourneyLength = journeyLengths.length > 0 
      ? journeyLengths.reduce((sum, len) => sum + len, 0) / journeyLengths.length 
      : 0;

    const journeyLengthDistribution = journeyLengths.reduce((acc, length) => {
      acc[length] = (acc[length] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const journeyLengthDistributionArray = Object.entries(journeyLengthDistribution)
      .map(([length, count]) => ({ length: parseInt(length), count }))
      .sort((a, b) => a.length - b.length);

    const timeBetweenTouchpoints = attributions
      .filter(attr => attr.timeSinceTouch > 0)
      .map(attr => attr.timeSinceTouch);
    
    const averageTimeBetweenTouchpoints = timeBetweenTouchpoints.length > 0
      ? timeBetweenTouchpoints.reduce((sum, time) => sum + time, 0) / timeBetweenTouchpoints.length
      : 0;

    // Mock journey paths (would need more complex analysis in real implementation)
    const topJourneyPaths = [
      { path: 'Display → Search → Direct', count: 45, value: 12500 },
      { path: 'Social → Email → Direct', count: 32, value: 8900 },
      { path: 'Search → Display → Search', count: 28, value: 7600 },
    ];

    return {
      summary: {
        totalAttributedValue: summary.totalAttributedValue,
        totalConversions: summary.totalConversions,
        totalTouchpoints: summary.totalTouchpoints,
        averageJourneyLength: summary.averageJourneyLength,
      },
      byCampaign,
      byChannel,
      byModel,
      journeyAnalysis: {
        averageJourneyLength,
        journeyLengthDistribution: journeyLengthDistributionArray,
        averageTimeBetweenTouchpoints,
        topJourneyPaths,
      },
    };
  }

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async processYesterdayConversions(): Promise<void> {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
      const result = await this.attributionEngineService.reprocessConversions(
        yesterday,
        today
      );
      
      this.logger.log(`Daily attribution processing completed: ${result.processed} processed, ${result.errors} errors`);
    } catch (error) {
      this.logger.error('Error in daily attribution processing:', error);
    }
  }

  async reprocessAttributions(
    startDate: Date,
    endDate: Date,
    modelName?: string
  ): Promise<{ processed: number; errors: number }> {
    return this.attributionEngineService.reprocessConversions(startDate, endDate, modelName);
  }

  async deleteAttribution(attributionId: string): Promise<void> {
    const result = await this.attributionRepository.delete(attributionId);
    
    if (result.affected === 0) {
      throw new BadRequestException('Attribution not found');
    }

    this.logger.log(`Deleted attribution ${attributionId}`);
  }
}
