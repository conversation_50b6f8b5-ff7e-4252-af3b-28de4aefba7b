import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { RateLimitingService } from './rate-limiting.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

export class BlockIPDto {
  ip: string;
  reason: string;
  duration?: number; // Duration in milliseconds
}

export class SecurityConfigDto {
  enableRateLimit: boolean;
  enableIPBlocking: boolean;
  enableSuspiciousActivityDetection: boolean;
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  blockDuration: number;
  alertThreshold: number;
}

@ApiTags('Security')
@Controller('security')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
@ApiBearerAuth()
export class SecurityController {
  constructor(private readonly rateLimitingService: RateLimitingService) {}

  @Get('status')
  @ApiOperation({ summary: 'Obter status de segurança do sistema' })
  @ApiResponse({ 
    status: 200, 
    description: 'Status de segurança',
    schema: {
      type: 'object',
      properties: {
        blockedIPs: { type: 'array', items: { type: 'string' } },
        suspiciousIPs: { type: 'array', items: { type: 'string' } },
        activeRules: { type: 'number' },
        totalAttempts: { type: 'number' },
        recentEvents: { type: 'number' },
        systemHealth: { type: 'string' },
        lastUpdate: { type: 'string', format: 'date-time' }
      }
    }
  })
  getSecurityStatus() {
    const status = this.rateLimitingService.getSecurityStatus();
    
    let systemHealth = 'healthy';
    if (status.blockedIPs.length > 10) systemHealth = 'warning';
    if (status.blockedIPs.length > 50) systemHealth = 'critical';

    return {
      ...status,
      systemHealth,
      lastUpdate: new Date().toISOString(),
    };
  }

  @Get('blocked-ips')
  @ApiOperation({ summary: 'Listar IPs bloqueados' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de IPs bloqueados',
    schema: {
      type: 'object',
      properties: {
        blockedIPs: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              ip: { type: 'string' },
              reason: { type: 'string' },
              blockedAt: { type: 'string', format: 'date-time' },
              expiresAt: { type: 'string', format: 'date-time' },
              attempts: { type: 'number' }
            }
          }
        },
        total: { type: 'number' }
      }
    }
  })
  getBlockedIPs() {
    const status = this.rateLimitingService.getSecurityStatus();
    
    // In a real implementation, this would fetch detailed data from database
    const blockedIPs = status.blockedIPs.map(ip => ({
      ip,
      reason: 'Rate limit exceeded',
      blockedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 3600000).toISOString(), // 1 hour from now
      attempts: Math.floor(Math.random() * 100) + 50, // Simulated
    }));

    return {
      blockedIPs,
      total: blockedIPs.length,
    };
  }

  @Get('suspicious-ips')
  @ApiOperation({ summary: 'Listar IPs suspeitos' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de IPs suspeitos',
    schema: {
      type: 'object',
      properties: {
        suspiciousIPs: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              ip: { type: 'string' },
              riskLevel: { type: 'string', enum: ['low', 'medium', 'high'] },
              lastActivity: { type: 'string', format: 'date-time' },
              attempts: { type: 'number' },
              patterns: { type: 'array', items: { type: 'string' } }
            }
          }
        },
        total: { type: 'number' }
      }
    }
  })
  getSuspiciousIPs() {
    const status = this.rateLimitingService.getSecurityStatus();
    
    // In a real implementation, this would fetch detailed data from database
    const suspiciousIPs = status.suspiciousIPs.map(ip => ({
      ip,
      riskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
      lastActivity: new Date().toISOString(),
      attempts: Math.floor(Math.random() * 50) + 20,
      patterns: ['rapid_requests', 'unusual_endpoints', 'failed_auth'],
    }));

    return {
      suspiciousIPs,
      total: suspiciousIPs.length,
    };
  }

  @Post('block-ip')
  @ApiOperation({ summary: 'Bloquear IP manualmente' })
  @ApiResponse({ status: 200, description: 'IP bloqueado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async blockIP(@Body() blockData: BlockIPDto) {
    await this.rateLimitingService.blockIP(
      blockData.ip,
      blockData.reason,
      blockData.duration,
    );

    return {
      message: 'IP bloqueado com sucesso',
      ip: blockData.ip,
      reason: blockData.reason,
      duration: blockData.duration,
      blockedAt: new Date().toISOString(),
    };
  }

  @Delete('unblock-ip/:ip')
  @ApiOperation({ summary: 'Desbloquear IP' })
  @ApiResponse({ status: 200, description: 'IP desbloqueado com sucesso' })
  async unblockIP(@Param('ip') ip: string) {
    await this.rateLimitingService.unblockIP(ip);

    return {
      message: 'IP desbloqueado com sucesso',
      ip,
      unblockedAt: new Date().toISOString(),
    };
  }

  @Get('events')
  @ApiOperation({ summary: 'Listar eventos de segurança recentes' })
  @ApiResponse({ 
    status: 200, 
    description: 'Eventos de segurança',
    schema: {
      type: 'object',
      properties: {
        events: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              type: { type: 'string' },
              severity: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
              source: { type: 'string' },
              endpoint: { type: 'string' },
              description: { type: 'string' },
              timestamp: { type: 'string', format: 'date-time' },
              resolved: { type: 'boolean' },
              actions: { type: 'array', items: { type: 'string' } }
            }
          }
        },
        pagination: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            totalPages: { type: 'number' }
          }
        }
      }
    }
  })
  getSecurityEvents(
    @Query('page') page = 1,
    @Query('limit') limit = 20,
    @Query('severity') severity?: string,
    @Query('type') type?: string,
  ) {
    // In a real implementation, this would fetch from database
    const mockEvents = [
      {
        id: '1',
        type: 'rate_limit_exceeded',
        severity: 'medium',
        source: '*************',
        endpoint: '/api/v1/campaigns',
        description: 'Rate limit exceeded for campaign creation',
        timestamp: new Date().toISOString(),
        resolved: false,
        actions: ['warning_sent'],
      },
      {
        id: '2',
        type: 'suspicious_activity',
        severity: 'high',
        source: '*********',
        endpoint: '/api/v1/auth/login',
        description: 'Multiple failed login attempts',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        resolved: true,
        actions: ['ip_blocked', 'alert_sent'],
      },
      {
        id: '3',
        type: 'brute_force',
        severity: 'critical',
        source: '***********',
        endpoint: '/api/v1/auth/*',
        description: 'Brute force attack detected',
        timestamp: new Date(Date.now() - 600000).toISOString(),
        resolved: true,
        actions: ['ip_blocked', 'admin_notified', 'incident_created'],
      },
    ];

    // Apply filters
    let filteredEvents = mockEvents;
    if (severity) {
      filteredEvents = filteredEvents.filter(e => e.severity === severity);
    }
    if (type) {
      filteredEvents = filteredEvents.filter(e => e.type === type);
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedEvents = filteredEvents.slice(startIndex, endIndex);

    return {
      events: paginatedEvents,
      pagination: {
        total: filteredEvents.length,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(filteredEvents.length / limit),
      },
    };
  }

  @Get('analytics')
  @ApiOperation({ summary: 'Análise de segurança e estatísticas' })
  @ApiResponse({ 
    status: 200, 
    description: 'Análise de segurança',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalRequests: { type: 'number' },
            blockedRequests: { type: 'number' },
            blockRate: { type: 'number' },
            topBlockedIPs: { type: 'array', items: { type: 'string' } },
            topTargetedEndpoints: { type: 'array', items: { type: 'string' } }
          }
        },
        trends: {
          type: 'object',
          properties: {
            hourly: { type: 'array', items: { type: 'number' } },
            daily: { type: 'array', items: { type: 'number' } },
            weekly: { type: 'array', items: { type: 'number' } }
          }
        },
        threats: {
          type: 'object',
          properties: {
            rateLimitViolations: { type: 'number' },
            suspiciousActivity: { type: 'number' },
            bruteForceAttempts: { type: 'number' },
            ddosAttempts: { type: 'number' }
          }
        }
      }
    }
  })
  getSecurityAnalytics() {
    // In a real implementation, this would calculate from database
    return {
      summary: {
        totalRequests: 15420,
        blockedRequests: 234,
        blockRate: 1.52,
        topBlockedIPs: ['*************', '*********', '***********'],
        topTargetedEndpoints: ['/api/v1/auth/login', '/api/v1/campaigns', '/api/v1/ai-estimator/estimate-performance'],
      },
      trends: {
        hourly: Array.from({ length: 24 }, () => Math.floor(Math.random() * 100)),
        daily: Array.from({ length: 7 }, () => Math.floor(Math.random() * 1000)),
        weekly: Array.from({ length: 4 }, () => Math.floor(Math.random() * 5000)),
      },
      threats: {
        rateLimitViolations: 156,
        suspiciousActivity: 23,
        bruteForceAttempts: 8,
        ddosAttempts: 2,
      },
    };
  }

  @Post('test-rate-limit')
  @ApiOperation({ summary: 'Testar rate limiting (apenas para desenvolvimento)' })
  @ApiResponse({ status: 200, description: 'Teste de rate limiting executado' })
  async testRateLimit(@Body() testData: { endpoint: string; method: string; requests: number }) {
    const results = [];
    
    for (let i = 0; i < testData.requests; i++) {
      const result = await this.rateLimitingService.checkRateLimit(
        testData.endpoint,
        testData.method,
        `test-key-${Date.now()}`,
        'admin',
      );
      
      results.push({
        attempt: i + 1,
        allowed: result.allowed,
        remaining: result.remaining,
        rule: result.rule?.name,
      });
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    return {
      message: 'Teste de rate limiting concluído',
      endpoint: testData.endpoint,
      method: testData.method,
      totalRequests: testData.requests,
      results,
    };
  }
}
