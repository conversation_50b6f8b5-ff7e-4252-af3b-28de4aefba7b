import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PassportModule } from '@nestjs/passport';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { User } from '../database/entities/user.entity';
import { Otp } from '../database/entities/otp.entity';
import { AuditLog } from '../database/entities/audit-log.entity';
import { CacheModule } from '../cache/cache.module';
import { EmailService } from '../email/email.service';
// import { QueueModule } from '../queue/queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Otp, AuditLog]),
    PassportModule,
    CacheModule,
    // QueueModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, EmailService],
  exports: [AuthService],
})
export class AuthModule {}
