import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { ProductPrice } from './product-price.entity';
import { PriceHistory } from './price-history.entity';

export enum ProviderType {
  PDV = 'pdv',
  ERP = 'erp',
  API = 'api',
  MANUAL = 'manual',
  FALLBACK = 'fallback',
}

export enum ProviderStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  MAINTENANCE = 'maintenance',
}

@Entity('price_providers')
@Index(['type'])
@Index(['status'])
@Index(['priority'])
export class PriceProvider {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  @Index()
  name: string;

  @Column({ type: 'varchar', length: 255 })
  displayName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: ProviderType })
  type: ProviderType;

  @Column({ type: 'enum', enum: ProviderStatus, default: ProviderStatus.ACTIVE })
  status: ProviderStatus;

  @Column({ type: 'int', default: 100 })
  priority: number; // Higher priority = preferred provider

  @Column({ type: 'varchar', length: 500, nullable: true })
  apiUrl: string;

  @Column({ type: 'json', nullable: true })
  apiConfig: Record<string, any>; // API configuration (headers, auth, etc.)

  @Column({ type: 'json', nullable: true })
  mapping: Record<string, any>; // Field mapping configuration

  @Column({ type: 'int', default: 60 })
  cacheMinutes: number; // How long to cache prices from this provider

  @Column({ type: 'int', default: 30000 })
  timeoutMs: number; // Request timeout in milliseconds

  @Column({ type: 'int', default: 3 })
  maxRetries: number;

  @Column({ type: 'boolean', default: true })
  isDefault: boolean; // Use as fallback when other providers fail

  @Column({ type: 'timestamp', nullable: true })
  lastSuccessAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastErrorAt: Date;

  @Column({ type: 'text', nullable: true })
  lastErrorMessage: string;

  @Column({ type: 'int', default: 0 })
  consecutiveErrors: number;

  @Column({ type: 'json', nullable: true })
  healthCheck: Record<string, any>; // Health check configuration

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => ProductPrice, productPrice => productPrice.provider)
  productPrices: ProductPrice[];

  @OneToMany(() => PriceHistory, priceHistory => priceHistory.provider)
  priceHistory: PriceHistory[];

  // Helper methods
  isHealthy(): boolean {
    if (this.status !== ProviderStatus.ACTIVE) {
      return false;
    }

    // Consider unhealthy if more than 5 consecutive errors
    if (this.consecutiveErrors > 5) {
      return false;
    }

    // Consider unhealthy if last error was recent and no success since
    if (this.lastErrorAt && this.lastSuccessAt) {
      return this.lastSuccessAt > this.lastErrorAt;
    }

    return this.consecutiveErrors === 0;
  }

  markSuccess(): void {
    this.lastSuccessAt = new Date();
    this.consecutiveErrors = 0;
    this.lastErrorMessage = null;
    
    if (this.status === ProviderStatus.ERROR) {
      this.status = ProviderStatus.ACTIVE;
    }
  }

  markError(errorMessage: string): void {
    this.lastErrorAt = new Date();
    this.lastErrorMessage = errorMessage;
    this.consecutiveErrors += 1;

    // Mark as error status if too many consecutive errors
    if (this.consecutiveErrors > 5) {
      this.status = ProviderStatus.ERROR;
    }
  }

  getHealthScore(): number {
    if (this.status !== ProviderStatus.ACTIVE) {
      return 0;
    }

    let score = 100;

    // Reduce score based on consecutive errors
    score -= this.consecutiveErrors * 10;

    // Reduce score if recent errors
    if (this.lastErrorAt) {
      const hoursSinceError = (Date.now() - this.lastErrorAt.getTime()) / (1000 * 60 * 60);
      if (hoursSinceError < 1) {
        score -= 30;
      } else if (hoursSinceError < 24) {
        score -= 10;
      }
    }

    return Math.max(0, score);
  }

  shouldUse(): boolean {
    return this.isHealthy() && this.getHealthScore() > 50;
  }

  getDisplayStatus(): string {
    if (this.status === ProviderStatus.MAINTENANCE) {
      return 'Em manutenção';
    }
    
    if (!this.isHealthy()) {
      return 'Com problemas';
    }

    const score = this.getHealthScore();
    if (score >= 90) return 'Excelente';
    if (score >= 70) return 'Bom';
    if (score >= 50) return 'Regular';
    return 'Ruim';
  }
}
