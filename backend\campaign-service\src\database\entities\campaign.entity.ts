import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { CampaignProduct } from './campaign-product.entity';
import { CampaignTransition } from './campaign-transition.entity';

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ENDED = 'ended',
  REACTIVATED = 'reactivated',
}

export enum IncentiveType {
  PERCENTAGE = 'percentage',
  AMOUNT_BRL = 'amount_brl',
}

@Entity('campaigns')
@Index(['industryId'])
@Index(['status'])
@Index(['startDate', 'endDate'])
@Index(['createdBy'])
export class Campaign {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'uuid' })
  industryId: string;

  @Column({ type: 'uuid' })
  createdBy: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: CampaignStatus.DRAFT,
  })
  status: CampaignStatus;

  @Column({
    type: 'varchar',
    length: 50,
    default: IncentiveType.PERCENTAGE,
  })
  incentiveType: IncentiveType;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  incentiveValue: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  incentivePercentage: number;

  @Column({ type: 'timestamp', nullable: true })
  startDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  endDate: Date;

  @Column({ type: 'integer', nullable: true })
  quantityLimit: number;

  @Column({ type: 'integer', default: 0 })
  quantityUsed: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalInvestment: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalDiscounts: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalRevenue: number;

  @Column({ type: 'integer', default: 0 })
  impactedConsumers: number;

  @Column({ type: 'integer', default: 0 })
  convertedConsumers: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  conversionRate: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  roi: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  cycleId: string;

  @Column({ type: 'timestamp', nullable: true })
  nextTransitionAt: Date;

  @Column({ type: 'varchar', length: 50, nullable: true })
  nextTransitionAction: string;

  @Column({ type: 'jsonb', nullable: true })
  aiEstimation: {
    estimatedConsumers: number;
    estimatedCost: number;
    confidence: number;
    factors: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  businessRules: {
    minIncentivePercent: number;
    minIncentiveAmount: number;
    maxProductsPerCycle: number;
    minQuantityPerSale: number;
    validityDays: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'timestamp', nullable: true })
  publishedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  pausedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  endedAt: Date;

  @Column({ type: 'uuid', nullable: true })
  pausedBy: string;

  @Column({ type: 'uuid', nullable: true })
  endedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => CampaignProduct, (campaignProduct) => campaignProduct.campaign, {
    cascade: true,
  })
  products: CampaignProduct[];

  @OneToMany(() => CampaignTransition, (transition) => transition.campaign, {
    cascade: true,
  })
  transitions: CampaignTransition[];

  // Computed properties
  get isActive(): boolean {
    return this.status === CampaignStatus.ACTIVE;
  }

  get isPaused(): boolean {
    return this.status === CampaignStatus.PAUSED;
  }

  get isEnded(): boolean {
    return this.status === CampaignStatus.ENDED;
  }

  get isDraft(): boolean {
    return this.status === CampaignStatus.DRAFT;
  }

  get isScheduled(): boolean {
    return this.status === CampaignStatus.SCHEDULED;
  }

  get hasQuantityLimit(): boolean {
    return this.quantityLimit !== null && this.quantityLimit > 0;
  }

  get isQuantityLimitReached(): boolean {
    return this.hasQuantityLimit && this.quantityUsed >= this.quantityLimit;
  }

  get remainingQuantity(): number {
    if (!this.hasQuantityLimit) return Infinity;
    return Math.max(0, this.quantityLimit - this.quantityUsed);
  }

  get isExpired(): boolean {
    if (!this.endDate) return false;
    return new Date() > this.endDate;
  }

  get daysRemaining(): number {
    if (!this.endDate) return Infinity;
    const now = new Date();
    const diff = this.endDate.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
  }

  // Business methods
  canBeActivated(): boolean {
    return this.status === CampaignStatus.DRAFT || this.status === CampaignStatus.SCHEDULED;
  }

  canBePaused(): boolean {
    return this.status === CampaignStatus.ACTIVE;
  }

  canBeResumed(): boolean {
    return this.status === CampaignStatus.PAUSED;
  }

  canBeEnded(): boolean {
    return this.status === CampaignStatus.ACTIVE || this.status === CampaignStatus.PAUSED;
  }

  canBeReactivated(): boolean {
    return this.status === CampaignStatus.ENDED;
  }

  calculateIncentiveAmount(productPrice: number): number {
    if (this.incentiveType === IncentiveType.PERCENTAGE) {
      return (productPrice * this.incentivePercentage) / 100;
    }
    return this.incentiveValue || 0;
  }

  updateMetrics(data: {
    quantityUsed?: number;
    totalInvestment?: number;
    totalDiscounts?: number;
    totalRevenue?: number;
    impactedConsumers?: number;
    convertedConsumers?: number;
  }): void {
    if (data.quantityUsed !== undefined) {
      this.quantityUsed = data.quantityUsed;
    }
    if (data.totalInvestment !== undefined) {
      this.totalInvestment = data.totalInvestment;
    }
    if (data.totalDiscounts !== undefined) {
      this.totalDiscounts = data.totalDiscounts;
    }
    if (data.totalRevenue !== undefined) {
      this.totalRevenue = data.totalRevenue;
    }
    if (data.impactedConsumers !== undefined) {
      this.impactedConsumers = data.impactedConsumers;
    }
    if (data.convertedConsumers !== undefined) {
      this.convertedConsumers = data.convertedConsumers;
    }

    // Calculate derived metrics
    if (this.impactedConsumers > 0) {
      this.conversionRate = (this.convertedConsumers / this.impactedConsumers) * 100;
    }

    if (this.totalInvestment > 0) {
      this.roi = ((this.totalRevenue - this.totalInvestment) / this.totalInvestment) * 100;
    }
  }

  scheduleTransition(action: string, executeAt: Date): void {
    this.nextTransitionAt = executeAt;
    this.nextTransitionAction = action;
  }

  clearScheduledTransition(): void {
    this.nextTransitionAt = null;
    this.nextTransitionAction = null;
  }
}
