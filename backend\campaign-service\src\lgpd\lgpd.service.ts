import {
  Injectable,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';

import { LgpdConsent, ConsentPurpose, ConsentStatus, ConsentMethod } from '../database/entities/lgpd-consent.entity';
import { DataSubjectRequest, RequestType, RequestStatus } from '../database/entities/data-subject-request.entity';
import { PersonalDataInventory } from '../database/entities/personal-data-inventory.entity';
import { DataProcessingActivity } from '../database/entities/data-processing-activity.entity';
import { LgpdIncident } from '../database/entities/lgpd-incident.entity';

import { DataAnonymizationService } from './data-anonymization.service';
import { AuditLoggerService } from '../audit/audit-logger.service';
import { ComplianceType, ComplianceEventType } from '../database/entities/compliance-event.entity';

@Injectable()
export class LgpdService {
  private readonly logger = new Logger(LgpdService.name);

  constructor(
    @InjectRepository(LgpdConsent)
    private readonly consentRepository: Repository<LgpdConsent>,
    @InjectRepository(DataSubjectRequest)
    private readonly requestRepository: Repository<DataSubjectRequest>,
    @InjectRepository(PersonalDataInventory)
    private readonly dataInventoryRepository: Repository<PersonalDataInventory>,
    @InjectRepository(DataProcessingActivity)
    private readonly processingActivityRepository: Repository<DataProcessingActivity>,
    @InjectRepository(LgpdIncident)
    private readonly incidentRepository: Repository<LgpdIncident>,
    private readonly anonymizationService: DataAnonymizationService,
    private readonly auditLogger: AuditLoggerService,
  ) {}

  // Consent Management
  async createConsent(consentData: {
    dataSubjectId: string;
    purpose: ConsentPurpose;
    method: ConsentMethod;
    description: string;
    legalBasis?: string;
    dataCategories?: string[];
    processingPurposes?: string[];
    recipients?: string[];
    retentionPeriod?: string;
    expiresAt?: Date;
    source?: string;
    ipAddress?: string;
    userAgent?: string;
    evidence?: Record<string, any>;
    isMinor?: boolean;
    guardianId?: string;
    preferences?: Record<string, any>;
    version?: string;
  }): Promise<LgpdConsent> {
    const consent = this.consentRepository.create(
      LgpdConsent.createConsent(consentData)
    );

    const savedConsent = await this.consentRepository.save(consent);

    // Log consent event
    await this.auditLogger.logComplianceEvent({
      userId: consentData.dataSubjectId,
      dataSubjectId: consentData.dataSubjectId,
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.CONSENT_GIVEN,
      description: `Consent given for ${consentData.purpose}: ${consentData.description}`,
      legalBasis: consentData.legalBasis,
      processingPurpose: consentData.purpose,
      dataCategories: consentData.dataCategories,
      consentDetails: {
        consentId: savedConsent.id,
        method: consentData.method,
        source: consentData.source,
      },
      consentTimestamp: savedConsent.createdAt,
      consentMethod: consentData.method,
      metadata: {
        ipAddress: consentData.ipAddress,
        userAgent: consentData.userAgent,
        evidence: consentData.evidence,
      },
    });

    this.logger.log(`Consent created for ${consentData.dataSubjectId} - ${consentData.purpose}`);
    return savedConsent;
  }

  async withdrawConsent(
    consentId: string,
    withdrawnBy?: string,
    reason?: string
  ): Promise<LgpdConsent> {
    const consent = await this.consentRepository.findOne({
      where: { id: consentId },
    });

    if (!consent) {
      throw new BadRequestException('Consent not found');
    }

    if (consent.status === ConsentStatus.WITHDRAWN) {
      throw new BadRequestException('Consent already withdrawn');
    }

    consent.withdraw(withdrawnBy, reason);
    const updatedConsent = await this.consentRepository.save(consent);

    // Log withdrawal event
    await this.auditLogger.logComplianceEvent({
      userId: withdrawnBy,
      dataSubjectId: consent.dataSubjectId,
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.CONSENT_WITHDRAWN,
      description: `Consent withdrawn for ${consent.purpose}${reason ? `: ${reason}` : ''}`,
      consentDetails: {
        consentId: consent.id,
        originalPurpose: consent.purpose,
        withdrawalReason: reason,
      },
      metadata: {
        withdrawnBy,
        originalConsentDate: consent.createdAt,
      },
    });

    this.logger.log(`Consent withdrawn: ${consentId} by ${withdrawnBy || 'data subject'}`);
    return updatedConsent;
  }

  async getConsents(
    dataSubjectId?: string,
    purpose?: ConsentPurpose,
    status?: ConsentStatus,
    limit: number = 100,
    offset: number = 0
  ): Promise<{ consents: LgpdConsent[]; total: number }> {
    const queryBuilder = this.consentRepository.createQueryBuilder('consent')
      .orderBy('consent.createdAt', 'DESC')
      .limit(limit)
      .offset(offset);

    if (dataSubjectId) {
      queryBuilder.andWhere('consent.dataSubjectId = :dataSubjectId', { dataSubjectId });
    }

    if (purpose) {
      queryBuilder.andWhere('consent.purpose = :purpose', { purpose });
    }

    if (status) {
      queryBuilder.andWhere('consent.status = :status', { status });
    }

    const [consents, total] = await queryBuilder.getManyAndCount();

    return { consents, total };
  }

  // Data Subject Rights
  async createDataSubjectRequest(requestData: {
    dataSubjectId: string;
    type: RequestType;
    description: string;
    subject?: string;
    requestedData?: string[];
    dataCategories?: string[];
    ipAddress?: string;
    userAgent?: string;
    source?: string;
  }): Promise<DataSubjectRequest> {
    const request = this.requestRepository.create(
      DataSubjectRequest.createRequest(requestData)
    );

    const savedRequest = await this.requestRepository.save(request);

    // Log request event
    await this.auditLogger.logComplianceEvent({
      dataSubjectId: requestData.dataSubjectId,
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.DATA_SUBJECT_REQUEST,
      description: `Data subject request: ${requestData.type} - ${requestData.description}`,
      requestType: requestData.type,
      requestId: savedRequest.id,
      responseDeadline: savedRequest.deadline,
      dataCategories: requestData.dataCategories,
      metadata: {
        ipAddress: requestData.ipAddress,
        userAgent: requestData.userAgent,
        source: requestData.source,
      },
    });

    this.logger.log(`Data subject request created: ${savedRequest.id} - ${requestData.type}`);
    return savedRequest;
  }

  async processDataSubjectRequest(
    requestId: string,
    response: string,
    responseMethod: string,
    processedBy: string,
    responseData?: Record<string, any>
  ): Promise<DataSubjectRequest> {
    const request = await this.requestRepository.findOne({
      where: { id: requestId },
    });

    if (!request) {
      throw new BadRequestException('Request not found');
    }

    if (request.isCompleted()) {
      throw new BadRequestException('Request already completed');
    }

    request.complete(response, responseMethod, responseData);
    const updatedRequest = await this.requestRepository.save(request);

    // Log completion event
    await this.auditLogger.logComplianceEvent({
      userId: processedBy,
      dataSubjectId: request.dataSubjectId,
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.DATA_SUBJECT_REQUEST,
      description: `Data subject request completed: ${request.type}`,
      requestType: request.type,
      requestId: request.id,
      responseDate: request.respondedAt,
      responseMethod: responseMethod,
      metadata: {
        processedBy,
        processingTime: request.getFormattedProcessingTime(),
        wasOverdue: request.isOverdue(),
      },
    });

    this.logger.log(`Data subject request completed: ${requestId} by ${processedBy}`);
    return updatedRequest;
  }

  // Compliance Monitoring
  async getComplianceStatus(): Promise<{
    activeConsents: number;
    withdrawnConsents: number;
    expiredConsents: number;
    pendingRequests: number;
    overdueRequests: number;
    completedRequests: number;
    complianceScore: number;
    riskLevel: 'low' | 'medium' | 'high';
    recommendations: string[];
  }> {
    const [
      activeConsents,
      withdrawnConsents,
      expiredConsents,
      pendingRequests,
      overdueRequests,
      completedRequests,
    ] = await Promise.all([
      this.consentRepository.count({ where: { status: ConsentStatus.GIVEN } }),
      this.consentRepository.count({ where: { status: ConsentStatus.WITHDRAWN } }),
      this.consentRepository.count({ where: { status: ConsentStatus.EXPIRED } }),
      this.requestRepository.count({ 
        where: [
          { status: RequestStatus.RECEIVED },
          { status: RequestStatus.UNDER_REVIEW },
          { status: RequestStatus.IN_PROGRESS },
        ]
      }),
      this.requestRepository.count({ where: { status: RequestStatus.OVERDUE } }),
      this.requestRepository.count({ where: { status: RequestStatus.COMPLETED } }),
    ]);

    // Calculate compliance score
    const totalRequests = pendingRequests + overdueRequests + completedRequests;
    const requestComplianceRate = totalRequests > 0 ? (completedRequests / totalRequests) * 100 : 100;
    const overdueRate = totalRequests > 0 ? (overdueRequests / totalRequests) * 100 : 0;
    
    let complianceScore = requestComplianceRate;
    if (overdueRate > 10) complianceScore -= 20;
    if (overdueRate > 20) complianceScore -= 30;
    
    complianceScore = Math.max(0, Math.min(100, complianceScore));

    // Determine risk level
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    if (complianceScore < 70 || overdueRequests > 5) riskLevel = 'high';
    else if (complianceScore < 85 || overdueRequests > 2) riskLevel = 'medium';

    // Generate recommendations
    const recommendations: string[] = [];
    if (overdueRequests > 0) {
      recommendations.push(`${overdueRequests} solicitações em atraso precisam de atenção imediata`);
    }
    if (expiredConsents > 0) {
      recommendations.push(`${expiredConsents} consentimentos expirados devem ser renovados ou dados anonimizados`);
    }
    if (complianceScore < 85) {
      recommendations.push('Melhorar processos de resposta a solicitações de titulares');
    }

    return {
      activeConsents,
      withdrawnConsents,
      expiredConsents,
      pendingRequests,
      overdueRequests,
      completedRequests,
      complianceScore,
      riskLevel,
      recommendations,
    };
  }

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async checkExpiredConsents(): Promise<void> {
    this.logger.log('Checking for expired consents');

    try {
      const expiredConsents = await this.consentRepository
        .createQueryBuilder('consent')
        .where('consent.expiresAt <= :now', { now: new Date() })
        .andWhere('consent.status = :status', { status: ConsentStatus.GIVEN })
        .getMany();

      for (const consent of expiredConsents) {
        consent.status = ConsentStatus.EXPIRED;
        await this.consentRepository.save(consent);

        // Log expiration
        await this.auditLogger.logComplianceEvent({
          dataSubjectId: consent.dataSubjectId,
          type: ComplianceType.LGPD,
          eventType: ComplianceEventType.CONSENT_WITHDRAWN,
          description: `Consent expired for ${consent.purpose}`,
          consentDetails: {
            consentId: consent.id,
            purpose: consent.purpose,
            expiredAt: consent.expiresAt,
          },
          metadata: {
            automaticExpiration: true,
          },
        });
      }

      this.logger.log(`Marked ${expiredConsents.length} consents as expired`);
    } catch (error) {
      this.logger.error('Error checking expired consents:', error);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async checkOverdueRequests(): Promise<void> {
    this.logger.log('Checking for overdue data subject requests');

    try {
      const overdueRequests = await this.requestRepository
        .createQueryBuilder('request')
        .where('request.deadline <= :now', { now: new Date() })
        .andWhere('request.status NOT IN (:...statuses)', { 
          statuses: [RequestStatus.COMPLETED, RequestStatus.REJECTED, RequestStatus.CANCELLED] 
        })
        .getMany();

      for (const request of overdueRequests) {
        request.status = RequestStatus.OVERDUE;
        await this.requestRepository.save(request);

        // Log overdue status
        await this.auditLogger.logComplianceEvent({
          dataSubjectId: request.dataSubjectId,
          type: ComplianceType.LGPD,
          eventType: ComplianceEventType.DATA_SUBJECT_REQUEST,
          description: `Data subject request is overdue: ${request.type}`,
          requestType: request.type,
          requestId: request.id,
          responseDeadline: request.deadline,
          metadata: {
            daysOverdue: request.getDaysOverdue(),
            automaticOverdueDetection: true,
          },
        });
      }

      this.logger.log(`Marked ${overdueRequests.length} requests as overdue`);
    } catch (error) {
      this.logger.error('Error checking overdue requests:', error);
    }
  }

  async generateLgpdReport(
    startDate: Date,
    endDate: Date
  ): Promise<{
    period: { start: Date; end: Date };
    consents: {
      total: number;
      given: number;
      withdrawn: number;
      expired: number;
      byPurpose: Record<ConsentPurpose, number>;
    };
    requests: {
      total: number;
      completed: number;
      pending: number;
      overdue: number;
      byType: Record<RequestType, number>;
      averageResponseTime: number;
    };
    compliance: {
      score: number;
      riskLevel: string;
      issues: string[];
    };
    anonymization: {
      recordsAnonymized: number;
      complianceRate: number;
    };
  }> {
    // Get consents in period
    const consents = await this.consentRepository
      .createQueryBuilder('consent')
      .where('consent.createdAt >= :startDate', { startDate })
      .andWhere('consent.createdAt <= :endDate', { endDate })
      .getMany();

    // Get requests in period
    const requests = await this.requestRepository
      .createQueryBuilder('request')
      .where('request.createdAt >= :startDate', { startDate })
      .andWhere('request.createdAt <= :endDate', { endDate })
      .getMany();

    // Calculate metrics
    const consentsByPurpose = consents.reduce((acc, consent) => {
      acc[consent.purpose] = (acc[consent.purpose] || 0) + 1;
      return acc;
    }, {} as Record<ConsentPurpose, number>);

    const requestsByType = requests.reduce((acc, request) => {
      acc[request.type] = (acc[request.type] || 0) + 1;
      return acc;
    }, {} as Record<RequestType, number>);

    const completedRequests = requests.filter(r => r.status === RequestStatus.COMPLETED);
    const averageResponseTime = completedRequests.length > 0
      ? completedRequests.reduce((sum, r) => sum + (r.getProcessingTime() || 0), 0) / completedRequests.length
      : 0;

    const complianceStatus = await this.getComplianceStatus();
    const anonymizationReport = await this.anonymizationService.generateAnonymizationReport(startDate, endDate);

    return {
      period: { start: startDate, end: endDate },
      consents: {
        total: consents.length,
        given: consents.filter(c => c.status === ConsentStatus.GIVEN).length,
        withdrawn: consents.filter(c => c.status === ConsentStatus.WITHDRAWN).length,
        expired: consents.filter(c => c.status === ConsentStatus.EXPIRED).length,
        byPurpose: consentsByPurpose,
      },
      requests: {
        total: requests.length,
        completed: requests.filter(r => r.status === RequestStatus.COMPLETED).length,
        pending: requests.filter(r => [RequestStatus.RECEIVED, RequestStatus.UNDER_REVIEW, RequestStatus.IN_PROGRESS].includes(r.status)).length,
        overdue: requests.filter(r => r.status === RequestStatus.OVERDUE).length,
        byType: requestsByType,
        averageResponseTime: Math.round(averageResponseTime / (1000 * 60 * 60)), // Convert to hours
      },
      compliance: {
        score: complianceStatus.complianceScore,
        riskLevel: complianceStatus.riskLevel,
        issues: complianceStatus.recommendations,
      },
      anonymization: {
        recordsAnonymized: anonymizationReport.totalRecordsAnonymized,
        complianceRate: anonymizationReport.complianceRate,
      },
    };
  }
}
