'use client';

import { useState, useEffect } from 'react';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Settings, 
  Save, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  Percent,
  DollarSign,
  Package,
  ToggleLeft,
  ToggleRight
} from 'lucide-react';
import toast from 'react-hot-toast';

interface BusinessRule {
  id: string;
  name: string;
  enabled: boolean;
  minPercentage?: number;
  minValue?: number;
  maxProducts?: number;
  description: string;
}

function AdminRulesPage() {
  const { user } = useAuth();
  const [rules, setRules] = useState<BusinessRule[]>([
    {
      id: 'incentive-percentage',
      name: 'Incentivo Mínimo (%)',
      enabled: true,
      minPercentage: 10,
      description: 'Percentual mínimo de incentivo permitido para campanhas'
    },
    {
      id: 'incentive-value',
      name: 'Incentivo Mínimo (R$)',
      enabled: true,
      minValue: 0.50,
      description: 'Valor mínimo em reais de incentivo permitido'
    },
    {
      id: 'products-per-cycle',
      name: 'Limite de Produtos por Ciclo',
      enabled: true,
      maxProducts: 100,
      description: 'Número máximo de produtos permitidos por ciclo de campanha'
    }
  ]);
  
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Check if user has admin permissions
  if (user?.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center text-red-600">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Acesso Negado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Você não tem permissão para acessar esta página. 
              Apenas administradores podem configurar regras de negócio.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const updateRule = (ruleId: string, updates: Partial<BusinessRule>) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, ...updates } : rule
    ));
    setHasChanges(true);
  };

  const validateRules = (): string[] => {
    const errors: string[] = [];
    
    rules.forEach(rule => {
      if (rule.enabled) {
        if (rule.minPercentage !== undefined) {
          if (rule.minPercentage < 0.1 || rule.minPercentage > 100) {
            errors.push(`${rule.name}: Percentual deve ser entre 0,1% e 100%`);
          }
        }
        
        if (rule.minValue !== undefined) {
          if (rule.minValue < 0) {
            errors.push(`${rule.name}: Valor deve ser positivo`);
          }
        }
        
        if (rule.maxProducts !== undefined) {
          if (rule.maxProducts < 1 || rule.maxProducts > 10000) {
            errors.push(`${rule.name}: Limite deve ser entre 1 e 10.000 produtos`);
          }
        }
      }
    });
    
    return errors;
  };

  const handleSave = async () => {
    const errors = validateRules();
    
    if (errors.length > 0) {
      errors.forEach(error => toast.error(error));
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Regras de negócio atualizadas com sucesso!');
      setHasChanges(false);
    } catch (error) {
      toast.error('Erro ao salvar regras de negócio');
    } finally {
      setIsLoading(false);
    }
  };

  const getImpactPreview = () => {
    const activeRules = rules.filter(rule => rule.enabled);
    const inactiveRules = rules.filter(rule => !rule.enabled);
    
    return {
      activeCount: activeRules.length,
      inactiveCount: inactiveRules.length,
      totalRules: rules.length
    };
  };

  const impact = getImpactPreview();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Settings className="h-6 w-6 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">
                Configuração de Regras
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {hasChanges && (
                <span className="text-sm text-orange-600 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  Alterações não salvas
                </span>
              )}
              <Button 
                onClick={handleSave} 
                disabled={!hasChanges || isLoading}
                className="flex items-center"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Salvar Alterações
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Impact Preview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Info className="h-5 w-5 mr-2 text-blue-600" />
              Resumo das Regras
            </CardTitle>
            <CardDescription>
              Visualização do impacto das configurações atuais
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{impact.activeCount}</div>
                <div className="text-sm text-green-700">Regras Ativas</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-600">{impact.inactiveCount}</div>
                <div className="text-sm text-gray-700">Regras Inativas</div>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{impact.totalRules}</div>
                <div className="text-sm text-blue-700">Total de Regras</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Rules Configuration */}
        <div className="space-y-6">
          {rules.map((rule) => (
            <Card key={rule.id} className={`transition-all ${rule.enabled ? 'border-green-200 bg-green-50/30' : 'border-gray-200'}`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      {rule.id === 'incentive-percentage' && <Percent className="h-5 w-5 mr-2 text-blue-600" />}
                      {rule.id === 'incentive-value' && <DollarSign className="h-5 w-5 mr-2 text-green-600" />}
                      {rule.id === 'products-per-cycle' && <Package className="h-5 w-5 mr-2 text-purple-600" />}
                      {rule.name}
                    </CardTitle>
                    <CardDescription>{rule.description}</CardDescription>
                  </div>
                  
                  <button
                    onClick={() => updateRule(rule.id, { enabled: !rule.enabled })}
                    className="flex items-center space-x-2 text-sm"
                  >
                    {rule.enabled ? (
                      <>
                        <ToggleRight className="h-6 w-6 text-green-600" />
                        <span className="text-green-600 font-medium">Ativo</span>
                      </>
                    ) : (
                      <>
                        <ToggleLeft className="h-6 w-6 text-gray-400" />
                        <span className="text-gray-500">Inativo</span>
                      </>
                    )}
                  </button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {rule.minPercentage !== undefined && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Percentual Mínimo (%)
                      </label>
                      <Input
                        type="number"
                        min="0.1"
                        max="100"
                        step="0.1"
                        value={rule.minPercentage}
                        onChange={(e) => updateRule(rule.id, { minPercentage: parseFloat(e.target.value) })}
                        disabled={!rule.enabled}
                        className={!rule.enabled ? 'bg-gray-100' : ''}
                      />
                    </div>
                  )}
                  
                  {rule.minValue !== undefined && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Valor Mínimo (R$)
                      </label>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        value={rule.minValue}
                        onChange={(e) => updateRule(rule.id, { minValue: parseFloat(e.target.value) })}
                        disabled={!rule.enabled}
                        className={!rule.enabled ? 'bg-gray-100' : ''}
                      />
                    </div>
                  )}
                  
                  {rule.maxProducts !== undefined && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Máximo de Produtos
                      </label>
                      <Input
                        type="number"
                        min="1"
                        max="10000"
                        value={rule.maxProducts}
                        onChange={(e) => updateRule(rule.id, { maxProducts: parseInt(e.target.value) })}
                        disabled={!rule.enabled}
                        className={!rule.enabled ? 'bg-gray-100' : ''}
                      />
                    </div>
                  )}
                </div>
                
                {!rule.enabled && (
                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center">
                      <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                      <span className="text-sm text-yellow-700">
                        Esta regra está desabilitada e não será aplicada às campanhas
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Help Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Info className="h-5 w-5 mr-2 text-blue-600" />
              Como Funciona
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm text-gray-600">
              <div>
                <strong>Incentivo Mínimo (%):</strong> Define o percentual mínimo de desconto que deve ser oferecido em campanhas.
              </div>
              <div>
                <strong>Incentivo Mínimo (R$):</strong> Define o valor mínimo em reais que deve ser oferecido como incentivo.
              </div>
              <div>
                <strong>Limite de Produtos por Ciclo:</strong> Controla quantos produtos podem ser incluídos em um único ciclo de campanha.
              </div>
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <strong>Importante:</strong> Alterações nas regras afetarão apenas novas campanhas. 
                Campanhas já ativas continuarão seguindo as regras vigentes no momento de sua criação.
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}

export default withAuth(AdminRulesPage);
