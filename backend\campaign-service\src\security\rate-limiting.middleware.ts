import { Injectable, NestMiddleware, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { RateLimitingService } from './rate-limiting.service';

interface AuthenticatedRequest extends Request {
  user?: {
    sub: string;
    email: string;
    role: string;
    industryId?: string;
  };
}

@Injectable()
export class RateLimitingMiddleware implements NestMiddleware {
  constructor(private readonly rateLimitingService: RateLimitingService) {}

  async use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    const startTime = Date.now();
    
    try {
      // Extract request information
      const endpoint = req.path;
      const method = req.method;
      const ip = this.getClientIP(req);
      const userAgent = req.get('User-Agent');
      
      // Generate rate limiting key
      const key = this.generateKey(req, ip);
      const userType = req.user?.role;
      const industryId = req.user?.industryId;

      // Check rate limit
      const result = await this.rateLimitingService.checkRateLimit(
        endpoint,
        method,
        key,
        userType,
        industryId,
        ip,
      );

      // Add rate limit headers if rule exists
      if (result.rule && result.rule.headers) {
        res.set({
          'X-RateLimit-Limit': result.rule.maxRequests.toString(),
          'X-RateLimit-Remaining': (result.remaining || 0).toString(),
          'X-RateLimit-Reset': result.resetTime ? new Date(result.resetTime).toISOString() : '',
          'X-RateLimit-Window': result.rule.windowMs.toString(),
        });
      }

      if (!result.allowed) {
        // Log the blocked attempt
        await this.rateLimitingService.logAttempt({
          ruleId: result.rule?.id || 'unknown',
          key,
          endpoint,
          method,
          userId: req.user?.sub,
          userType,
          industryId,
          ip,
          userAgent,
          success: false,
          statusCode: 429,
          responseTime: Date.now() - startTime,
          blocked: true,
          reason: result.rule?.message || 'Rate limit exceeded',
        });

        // Add retry-after header
        if (result.retryAfter) {
          res.set('Retry-After', result.retryAfter.toString());
        }

        throw new HttpException(
          {
            statusCode: 429,
            error: 'Too Many Requests',
            message: result.rule?.message || 'Rate limit exceeded',
            retryAfter: result.retryAfter,
          },
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      // Intercept response to log successful attempts
      const originalSend = res.send;
      const rateLimitingService = this.rateLimitingService;
      res.send = function(body) {
        const responseTime = Date.now() - startTime;

        // Log the attempt (don't await to avoid blocking response)
        rateLimitingService.logAttempt({
          ruleId: result.rule?.id || 'none',
          key,
          endpoint,
          method,
          userId: req.user?.sub,
          userType,
          industryId,
          ip,
          userAgent,
          success: res.statusCode < 400,
          statusCode: res.statusCode,
          responseTime,
          blocked: false,
        }).catch(err => {
          console.error('Failed to log rate limit attempt:', err);
        });

        return originalSend.call(this, body);
      };

      next();
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      // Log unexpected errors
      console.error('Rate limiting middleware error:', error);
      next(); // Continue on unexpected errors
    }
  }

  private getClientIP(req: Request): string {
    return (
      req.ip ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      (req.connection as any)?.socket?.remoteAddress ||
      req.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      req.get('X-Real-IP') ||
      req.get('X-Client-IP') ||
      'unknown'
    );
  }

  private generateKey(req: AuthenticatedRequest, ip: string): string {
    // Use user ID if authenticated, otherwise use IP
    if (req.user?.sub) {
      return `user:${req.user.sub}`;
    }
    return `ip:${ip}`;
  }
}
