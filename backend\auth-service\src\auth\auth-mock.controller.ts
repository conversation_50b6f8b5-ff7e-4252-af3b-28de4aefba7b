import { Controller, Post, Body, HttpCode, HttpStatus, Logger } from '@nestjs/common';
import { AuthMockService } from './auth-mock.service';

@Controller('api/v1/auth')
export class AuthMockController {
  private readonly logger = new Logger(AuthMockController.name);

  constructor(private readonly authMockService: AuthMockService) {}

  @Post('industries/request-otp')
  @HttpCode(HttpStatus.OK)
  async requestOtpForIndustry(@Body() body: { email: string }) {
    try {
      this.logger.log(`OTP request for industry: ${body.email}`);
      return await this.authMockService.requestOtpForIndustry(body.email);
    } catch (error) {
      this.logger.error(`Error requesting OTP: ${error.message}`);
      throw error;
    }
  }

  @Post('industries/authenticate')
  @HttpCode(HttpStatus.OK)
  async authenticateIndustry(@Body() body: { email: string; code: string }) {
    try {
      this.logger.log(`Authentication attempt for industry: ${body.email}`);
      return await this.authMockService.authenticateIndustry(body.email, body.code);
    } catch (error) {
      this.logger.error(`Error authenticating: ${error.message}`);
      throw error;
    }
  }
}
