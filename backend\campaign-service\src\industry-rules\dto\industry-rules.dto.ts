import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>tring, IsNumber, IsBoolean, IsEnum, IsUUID, <PERSON>, <PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RuleType, RuleStatus } from '../../database/entities/industry-rules.entity';

export class CreateIndustryRuleDto {
  @ApiProperty({
    description: 'ID da indústria',
    example: 'uuid-da-industria'
  })
  @IsUUID()
  @IsNotEmpty()
  industryId: string;

  @ApiProperty({
    description: 'Tipo da regra',
    enum: RuleType,
    example: RuleType.MINIMUM_PERCENTAGE
  })
  @IsEnum(RuleType)
  @IsNotEmpty()
  ruleType: RuleType;

  @ApiPropertyOptional({
    description: 'Valor mínimo em reais',
    example: 0.50,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  minValue?: number;

  @ApiPropertyOptional({
    description: 'Valor máximo em reais',
    example: 100.00,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  maxValue?: number;

  @ApiPropertyOptional({
    description: 'Percentual mínimo',
    example: 10.5,
    minimum: 0.1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.1)
  @Max(100)
  minPercentage?: number;

  @ApiPropertyOptional({
    description: 'Percentual máximo',
    example: 50.0,
    minimum: 0.1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.1)
  @Max(100)
  maxPercentage?: number;

  @ApiPropertyOptional({
    description: 'Valor inteiro (para limites de produtos, dias, etc.)',
    example: 100,
    minimum: 1
  })
  @IsOptional()
  @IsNumber({}, { message: 'Valor deve ser um número inteiro' })
  @Min(1)
  integerValue?: number;

  @ApiPropertyOptional({
    description: 'Se a regra está habilitada',
    example: true,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @ApiPropertyOptional({
    description: 'Status da regra',
    enum: RuleStatus,
    example: RuleStatus.ACTIVE,
    default: RuleStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(RuleStatus)
  status?: RuleStatus;

  @ApiPropertyOptional({
    description: 'Descrição da regra',
    example: 'Percentual mínimo de incentivo para produtos de higiene'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Metadados adicionais da regra',
    example: { category: 'hygiene', priority: 'high' }
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpdateIndustryRuleDto {
  @ApiPropertyOptional({
    description: 'Valor mínimo em reais',
    example: 0.50,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  minValue?: number;

  @ApiPropertyOptional({
    description: 'Valor máximo em reais',
    example: 100.00,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  maxValue?: number;

  @ApiPropertyOptional({
    description: 'Percentual mínimo',
    example: 10.5,
    minimum: 0.1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.1)
  @Max(100)
  minPercentage?: number;

  @ApiPropertyOptional({
    description: 'Percentual máximo',
    example: 50.0,
    minimum: 0.1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.1)
  @Max(100)
  maxPercentage?: number;

  @ApiPropertyOptional({
    description: 'Valor inteiro (para limites de produtos, dias, etc.)',
    example: 100,
    minimum: 1
  })
  @IsOptional()
  @IsNumber({}, { message: 'Valor deve ser um número inteiro' })
  @Min(1)
  integerValue?: number;

  @ApiPropertyOptional({
    description: 'Se a regra está habilitada',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @ApiPropertyOptional({
    description: 'Status da regra',
    enum: RuleStatus,
    example: RuleStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(RuleStatus)
  status?: RuleStatus;

  @ApiPropertyOptional({
    description: 'Descrição da regra',
    example: 'Percentual mínimo de incentivo para produtos de higiene'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Metadados adicionais da regra',
    example: { category: 'hygiene', priority: 'high' }
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class ValidateCampaignDto {
  @ApiProperty({
    description: 'ID da indústria',
    example: 'uuid-da-industria'
  })
  @IsUUID()
  @IsNotEmpty()
  industryId: string;

  @ApiProperty({
    description: 'Percentual de incentivo',
    example: 15.5,
    minimum: 0.1,
    maximum: 100
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.1)
  @Max(100)
  incentivePercentage: number;

  @ApiProperty({
    description: 'Valor do incentivo em reais',
    example: 2.50,
    minimum: 0
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  incentiveValue: number;

  @ApiProperty({
    description: 'Quantidade de produtos na campanha',
    example: 50,
    minimum: 1
  })
  @IsNumber({}, { message: 'Quantidade deve ser um número inteiro' })
  @Min(1)
  productCount: number;

  @ApiPropertyOptional({
    description: 'Validade do incentivo em dias',
    example: 30,
    minimum: 1,
    maximum: 365,
    default: 30
  })
  @IsOptional()
  @IsNumber({}, { message: 'Validade deve ser um número inteiro' })
  @Min(1)
  @Max(365)
  validityDays?: number;
}
