import axios, { AxiosResponse, AxiosError } from 'axios';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(
  (config) => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      if (typeof window !== 'undefined') {
        try {
          const refreshToken = localStorage.getItem('refreshToken');
          if (refreshToken) {
            const response = await axios.post(`${API_BASE_URL}/api/v1/auth/refresh`, {
              refreshToken,
            });

            const { accessToken, refreshToken: newRefreshToken } = response.data;
            localStorage.setItem('accessToken', accessToken);
            if (newRefreshToken) {
              localStorage.setItem('refreshToken', newRefreshToken);
            }

            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            return api(originalRequest);
          }
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('user');
          window.location.href = '/auth/login';
          return Promise.reject(refreshError);
        }
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  requestOtp: (email: string) =>
    api.post('/api/auth/industries/request-otp', { email }),

  authenticate: (email: string, code: string) =>
    api.post('/api/auth/industries/authenticate', { email, code }),

  updateProfile: (data: any) =>
    api.put('/api/auth/industries/profile', data),

  logout: () =>
    api.post('/api/v1/auth/logout'),

  refreshToken: (refreshToken: string) =>
    api.post('/api/v1/auth/refresh', { refreshToken }),
};

// Campaign API
export const campaignAPI = {
  getCampaigns: (params?: any) =>
    api.get('/api/v1/campaigns', { params }),

  getCampaign: (id: string) =>
    api.get(`/api/v1/campaigns/${id}`),

  createCampaign: (data: any) =>
    api.post('/api/v1/campaigns', data),

  updateCampaign: (id: string, data: any) =>
    api.put(`/api/v1/campaigns/${id}`, data),

  deleteCampaign: (id: string) =>
    api.delete(`/api/v1/campaigns/${id}`),

  getCampaignAnalytics: (id: string) =>
    api.get(`/api/v1/campaigns/${id}/analytics`),

  getCampaignReports: (id: string) =>
    api.get(`/api/v1/campaigns/${id}/reports`),
};

// Reports API
export const reportsAPI = {
  getCampaignPerformance: (filters?: any) =>
    api.post('/api/v1/reports/campaign-performance', filters),

  getProductPerformance: (filters?: any) =>
    api.post('/api/v1/reports/product-performance', filters),

  getIncentiveAnalysis: (filters?: any) =>
    api.post('/api/v1/reports/incentive-analysis', filters),

  getUserActivity: (filters?: any) =>
    api.post('/api/v1/reports/user-activity', filters),

  getDashboardCharts: (params?: any) =>
    api.get('/api/v1/reports/dashboard-charts', { params }),

  exportReport: (reportType: string, filters: any, options: any) =>
    api.post('/api/v1/reports/export', { reportType, filters, options }),

  getReportStatus: (reportId: string) =>
    api.get(`/api/v1/reports/status/${reportId}`),
};

// User API
export const userAPI = {
  getUsers: (params?: any) =>
    api.get('/api/v1/users', { params }),
  
  getUser: (id: string) =>
    api.get(`/api/v1/users/${id}`),
  
  createUser: (data: any) =>
    api.post('/api/v1/users', data),
  
  updateUser: (id: string, data: any) =>
    api.put(`/api/v1/users/${id}`, data),
  
  deleteUser: (id: string) =>
    api.delete(`/api/v1/users/${id}`),
  
  inviteUser: (data: any) =>
    api.post('/api/v1/users/invite', data),
};

// Analytics API
export const analyticsAPI = {
  getDashboardStats: () =>
    api.get('/api/v1/analytics/dashboard'),
  
  getCampaignPerformance: (params?: any) =>
    api.get('/api/v1/analytics/campaigns', { params }),
  
  getRevenueAnalytics: (params?: any) =>
    api.get('/api/v1/analytics/revenue', { params }),
  
  getAudienceInsights: (params?: any) =>
    api.get('/api/v1/analytics/audience', { params }),
};

// Attribution API
export const attributionAPI = {
  getAttributionModels: () =>
    api.get('/api/v1/attribution/models'),
  
  createAttributionModel: (data: any) =>
    api.post('/api/v1/attribution/models', data),
  
  updateAttributionModel: (id: string, data: any) =>
    api.put(`/api/v1/attribution/models/${id}`, data),
  
  getAttributionAnalytics: (params?: any) =>
    api.get('/api/v1/attribution/analytics', { params }),
};

// Health Check API
export const healthAPI = {
  checkHealth: () =>
    api.get('/api/v1/health'),
  
  checkRedisHealth: () =>
    api.get('/api/v1/auth/health/redis'),
  
  checkRabbitMQHealth: () =>
    api.get('/api/v1/auth/health/rabbitmq'),
};

// Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'editor' | 'reader';
  status: 'active' | 'inactive' | 'pending';
  industryId?: string;
  emailVerified: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Campaign {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  startDate: Date;
  endDate: Date;
  budget: number;
  spent: number;
  impressions: number;
  clicks: number;
  conversions: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export default api;
