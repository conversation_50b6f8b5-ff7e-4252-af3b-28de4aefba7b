import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBasicTables1703000000001 implements MigrationInterface {
  name = 'CreateBasicTables1703000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Enable UUID extension
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);

    // Create basic tables without complex indexes to avoid conflicts
    
    // Attribution Models
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "attribution_model_type_enum" AS ENUM(
          'first_click', 'last_click', 'linear', 'time_decay', 'position_based', 'data_driven'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "attribution_models" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(100) NOT NULL UNIQUE,
        "displayName" character varying(255) NOT NULL,
        "description" text,
        "type" "attribution_model_type_enum" NOT NULL,
        "isActive" boolean NOT NULL DEFAULT true,
        "isDefault" boolean NOT NULL DEFAULT false,
        "lookbackWindowDays" integer NOT NULL DEFAULT 7,
        "configuration" json,
        "weights" json,
        "decayRate" decimal(3,2) NOT NULL DEFAULT 0.5,
        "priority" integer NOT NULL DEFAULT 100,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_attribution_models" PRIMARY KEY ("id")
      )
    `);

    // Business Rules
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "business_rules_ruletype_enum" AS ENUM(
          'incentive_percentage', 'incentive_value', 'products_per_cycle',
          'campaign_duration', 'budget_limit'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "business_rules_status_enum" AS ENUM(
          'active', 'inactive', 'pending'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "business_rules" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "type" "business_rules_ruletype_enum" NOT NULL,
        "name" character varying(255) NOT NULL,
        "description" text,
        "status" "business_rules_status_enum" NOT NULL DEFAULT 'active',
        "enabled" boolean NOT NULL DEFAULT true,
        "industryId" uuid,
        "minValue" decimal(10,2),
        "maxValue" decimal(10,2),
        "defaultValue" decimal(10,2),
        "priority" integer NOT NULL DEFAULT 0,
        "metadata" json,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_business_rules" PRIMARY KEY ("id")
      )
    `);

    // Product Categories
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "product_categories_status_enum" AS ENUM(
          'active', 'inactive'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "product_categories" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "description" text,
        "status" "product_categories_status_enum" NOT NULL DEFAULT 'active',
        "parentId" uuid,
        "industryId" character varying(255),
        "metadata" json,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_product_categories" PRIMARY KEY ("id")
      )
    `);

    // Products
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "products_type_enum" AS ENUM(
          'simple', 'variable', 'grouped', 'external'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "products_status_enum" AS ENUM(
          'active', 'inactive', 'draft', 'archived'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "products" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "description" text,
        "sku" character varying(100),
        "barcode" character varying(100),
        "price" decimal(10,2),
        "costPrice" decimal(10,2),
        "categoryId" character varying,
        "subcategoryId" character varying(255),
        "industryId" character varying(255) NOT NULL,
        "brandId" character varying(255) NOT NULL,
        "manufacturerId" character varying(255),
        "type" "products_type_enum" NOT NULL DEFAULT 'simple',
        "status" "products_status_enum" NOT NULL DEFAULT 'active',
        "stockQuantity" integer NOT NULL DEFAULT 0,
        "minStockLevel" integer,
        "trackStock" boolean NOT NULL DEFAULT true,
        "weight" decimal(8,3),
        "dimensions" json,
        "images" json,
        "tags" json,
        "attributes" json,
        "seoTitle" character varying(255),
        "seoDescription" text,
        "seoKeywords" character varying(500),
        "isDigital" boolean NOT NULL DEFAULT false,
        "isEligibleForIncentives" boolean NOT NULL DEFAULT true,
        "maxIncentivePercentage" decimal(5,2),
        "maxIncentiveValue" decimal(10,2),
        "salesCount" integer NOT NULL DEFAULT 0,
        "averageRating" decimal(3,2),
        "reviewCount" integer NOT NULL DEFAULT 0,
        "lastSaleDate" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_products" PRIMARY KEY ("id")
      )
    `);

    // Basic indexes for essential queries
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_products_industry" ON "products" ("industryId")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_products_status" ON "products" ("status")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_business_rules_industry" ON "business_rules" ("industryId")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_product_categories_industry" ON "product_categories" ("industryId")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS "products"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "product_categories"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "business_rules"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "attribution_models"`);
    
    await queryRunner.query(`DROP TYPE IF EXISTS "products_status_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "products_type_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "product_categories_status_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "business_rules_status_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "business_rules_ruletype_enum"`);
  }
}
