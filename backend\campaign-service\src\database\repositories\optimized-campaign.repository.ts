import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { OptimizedCampaign, CampaignStatus } from '../entities/optimized-campaign.entity';

export interface CampaignFilters {
  industryId?: string;
  status?: CampaignStatus[];
  isActive?: boolean;
  startDate?: Date;
  endDate?: Date;
  search?: string;
  createdBy?: string;
  isFeatured?: boolean;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface CampaignSummary {
  totalCampaigns: number;
  activeCampaigns: number;
  totalBudget: number;
  totalSpent: number;
  totalRevenue: number;
  averageROI: number;
}

/**
 * Optimized repository for campaign operations
 * Implements performance best practices:
 * - Query optimization with proper indexing
 * - Pagination with efficient counting
 * - Bulk operations
 * - Query result caching
 * - N+1 query prevention
 */
@Injectable()
export class OptimizedCampaignRepository extends Repository<OptimizedCampaign> {
  constructor(private dataSource: DataSource) {
    super(OptimizedCampaign, dataSource.createEntityManager());
  }

  /**
   * Find campaigns with optimized filtering and pagination
   */
  async findWithFilters(
    filters: CampaignFilters,
    pagination: PaginationOptions
  ): Promise<{ campaigns: OptimizedCampaign[]; total: number }> {
    const queryBuilder = this.createFilteredQuery(filters);
    
    // Apply sorting
    const sortBy = pagination.sortBy || 'createdAt';
    const sortOrder = pagination.sortOrder || 'DESC';
    queryBuilder.orderBy(`campaign.${sortBy}`, sortOrder);

    // Apply pagination
    const offset = (pagination.page - 1) * pagination.limit;
    queryBuilder.skip(offset).take(pagination.limit);

    // Execute query with count (optimized to avoid double query)
    const [campaigns, total] = await queryBuilder.getManyAndCount();

    return { campaigns, total };
  }

  /**
   * Find active campaigns for a specific industry with caching
   */
  async findActiveCampaigns(industryId: string): Promise<OptimizedCampaign[]> {
    return this.createQueryBuilder('campaign')
      .where('campaign.industryId = :industryId', { industryId })
      .andWhere('campaign.status = :status', { status: CampaignStatus.ACTIVE })
      .andWhere('campaign.isActive = true')
      .andWhere('campaign.startDate <= :now', { now: new Date() })
      .andWhere('campaign.endDate > :now', { now: new Date() })
      .orderBy('campaign.createdAt', 'DESC')
      .cache(30000) // Cache for 30 seconds
      .getMany();
  }

  /**
   * Get campaign summary statistics
   */
  async getCampaignSummary(industryId: string): Promise<CampaignSummary> {
    const result = await this.createQueryBuilder('campaign')
      .select([
        'COUNT(*) as "totalCampaigns"',
        'COUNT(CASE WHEN campaign.status = :activeStatus THEN 1 END) as "activeCampaigns"',
        'COALESCE(SUM(campaign.budget), 0) as "totalBudget"',
        'COALESCE(SUM(campaign.spentAmount), 0) as "totalSpent"',
        'COALESCE(SUM(campaign.totalRevenue), 0) as "totalRevenue"',
        'COALESCE(AVG(CASE WHEN campaign.spentAmount > 0 THEN ((campaign.totalRevenue - campaign.spentAmount) / campaign.spentAmount) * 100 END), 0) as "averageROI"'
      ])
      .where('campaign.industryId = :industryId', { industryId })
      .setParameter('activeStatus', CampaignStatus.ACTIVE)
      .cache(60000) // Cache for 1 minute
      .getRawOne();

    return {
      totalCampaigns: parseInt(result.totalCampaigns),
      activeCampaigns: parseInt(result.activeCampaigns),
      totalBudget: parseFloat(result.totalBudget),
      totalSpent: parseFloat(result.totalSpent),
      totalRevenue: parseFloat(result.totalRevenue),
      averageROI: parseFloat(result.averageROI),
    };
  }

  /**
   * Find campaigns expiring soon
   */
  async findExpiringCampaigns(days: number = 7): Promise<OptimizedCampaign[]> {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + days);

    return this.createQueryBuilder('campaign')
      .where('campaign.status = :status', { status: CampaignStatus.ACTIVE })
      .andWhere('campaign.endDate BETWEEN :now AND :expiryDate', {
        now: new Date(),
        expiryDate
      })
      .orderBy('campaign.endDate', 'ASC')
      .getMany();
  }

  /**
   * Bulk update campaign status
   */
  async bulkUpdateStatus(
    campaignIds: string[],
    status: CampaignStatus,
    updatedBy?: string
  ): Promise<void> {
    const updateData: any = { status, updatedAt: new Date() };
    
    if (status === CampaignStatus.PAUSED && updatedBy) {
      updateData.pausedAt = new Date();
      updateData.pausedBy = updatedBy;
    }

    await this.createQueryBuilder()
      .update(OptimizedCampaign)
      .set(updateData)
      .where('id IN (:...ids)', { ids: campaignIds })
      .execute();
  }

  /**
   * Update campaign metrics efficiently
   */
  async updateCampaignMetrics(
    campaignId: string,
    metrics: {
      conversionCount?: number;
      totalRevenue?: number;
      spentAmount?: number;
    }
  ): Promise<void> {
    await this.createQueryBuilder()
      .update(OptimizedCampaign)
      .set({
        ...metrics,
        updatedAt: new Date(),
      })
      .where('id = :id', { id: campaignId })
      .execute();
  }

  /**
   * Find campaigns with low performance
   */
  async findLowPerformanceCampaigns(
    industryId: string,
    minROI: number = 10
  ): Promise<OptimizedCampaign[]> {
    return this.createQueryBuilder('campaign')
      .where('campaign.industryId = :industryId', { industryId })
      .andWhere('campaign.status = :status', { status: CampaignStatus.ACTIVE })
      .andWhere('campaign.spentAmount > 0')
      .andWhere(
        '((campaign.totalRevenue - campaign.spentAmount) / campaign.spentAmount) * 100 < :minROI',
        { minROI }
      )
      .orderBy('((campaign.totalRevenue - campaign.spentAmount) / campaign.spentAmount) * 100', 'ASC')
      .getMany();
  }

  /**
   * Search campaigns with full-text search
   */
  async searchCampaigns(
    industryId: string,
    searchTerm: string,
    limit: number = 10
  ): Promise<OptimizedCampaign[]> {
    return this.createQueryBuilder('campaign')
      .where('campaign.industryId = :industryId', { industryId })
      .andWhere('campaign.status != :draftStatus', { draftStatus: CampaignStatus.DRAFT })
      .andWhere(
        '(campaign.name ILIKE :search OR campaign.description ILIKE :search)',
        { search: `%${searchTerm}%` }
      )
      .orderBy(
        `CASE 
          WHEN campaign.name ILIKE :exactSearch THEN 1
          WHEN campaign.name ILIKE :startSearch THEN 2
          WHEN campaign.description ILIKE :startSearch THEN 3
          ELSE 4
        END`,
        'ASC'
      )
      .setParameters({
        exactSearch: searchTerm,
        startSearch: `${searchTerm}%`
      })
      .take(limit)
      .getMany();
  }

  /**
   * Get campaign performance trends
   */
  async getCampaignTrends(
    industryId: string,
    days: number = 30
  ): Promise<Array<{ date: string; campaigns: number; revenue: number; conversions: number }>> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const result = await this.createQueryBuilder('campaign')
      .select([
        'DATE(campaign.createdAt) as date',
        'COUNT(*) as campaigns',
        'COALESCE(SUM(campaign.totalRevenue), 0) as revenue',
        'COALESCE(SUM(campaign.conversionCount), 0) as conversions'
      ])
      .where('campaign.industryId = :industryId', { industryId })
      .andWhere('campaign.createdAt >= :startDate', { startDate })
      .groupBy('DATE(campaign.createdAt)')
      .orderBy('DATE(campaign.createdAt)', 'ASC')
      .getRawMany();

    return result.map(row => ({
      date: row.date,
      campaigns: parseInt(row.campaigns),
      revenue: parseFloat(row.revenue),
      conversions: parseInt(row.conversions),
    }));
  }

  /**
   * Helper method to create filtered query builder
   */
  private createFilteredQuery(filters: CampaignFilters): SelectQueryBuilder<OptimizedCampaign> {
    const queryBuilder = this.createQueryBuilder('campaign');

    if (filters.industryId) {
      queryBuilder.andWhere('campaign.industryId = :industryId', {
        industryId: filters.industryId
      });
    }

    if (filters.status && filters.status.length > 0) {
      queryBuilder.andWhere('campaign.status IN (:...statuses)', {
        statuses: filters.status
      });
    }

    if (filters.isActive !== undefined) {
      queryBuilder.andWhere('campaign.isActive = :isActive', {
        isActive: filters.isActive
      });
    }

    if (filters.startDate) {
      queryBuilder.andWhere('campaign.startDate >= :startDate', {
        startDate: filters.startDate
      });
    }

    if (filters.endDate) {
      queryBuilder.andWhere('campaign.endDate <= :endDate', {
        endDate: filters.endDate
      });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(campaign.name ILIKE :search OR campaign.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    if (filters.createdBy) {
      queryBuilder.andWhere('campaign.createdBy = :createdBy', {
        createdBy: filters.createdBy
      });
    }

    if (filters.isFeatured !== undefined) {
      queryBuilder.andWhere('campaign.isFeatured = :isFeatured', {
        isFeatured: filters.isFeatured
      });
    }

    return queryBuilder;
  }
}
