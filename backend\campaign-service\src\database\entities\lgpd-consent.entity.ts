import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum ConsentPurpose {
  MARKETING = 'marketing',
  ANALYTICS = 'analytics',
  PERSONALIZATION = 'personalization',
  COMMUNICATION = 'communication',
  PROFILING = 'profiling',
  THIRD_PARTY_SHARING = 'third_party_sharing',
  COOKIES = 'cookies',
  LOCATION_TRACKING = 'location_tracking',
  BEHAVIORAL_ADVERTISING = 'behavioral_advertising',
  RESEARCH = 'research',
  LEGAL_COMPLIANCE = 'legal_compliance',
  SECURITY = 'security',
}

export enum ConsentStatus {
  GIVEN = 'given',
  WITHDRAWN = 'withdrawn',
  EXPIRED = 'expired',
  PENDING = 'pending',
  REFUSED = 'refused',
}

export enum ConsentMethod {
  EXPLICIT = 'explicit', // Explicit consent (checkbox, button)
  OPT_IN = 'opt_in', // Opt-in during registration
  OPT_OUT = 'opt_out', // Opt-out mechanism
  LEGITIMATE_INTEREST = 'legitimate_interest', // Based on legitimate interest
  CONTRACTUAL = 'contractual', // Necessary for contract performance
  LEGAL_OBLIGATION = 'legal_obligation', // Required by law
  VITAL_INTERESTS = 'vital_interests', // Protect vital interests
}

@Entity('lgpd_consents')
@Index(['dataSubjectId', 'purpose'])
@Index(['status', 'createdAt'])
@Index(['purpose', 'status'])
@Index(['expiresAt'])
@Index(['withdrawnAt'])
export class LgpdConsent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  dataSubjectId: string; // User ID or email

  @Column({ type: 'enum', enum: ConsentPurpose })
  purpose: ConsentPurpose;

  @Column({ type: 'enum', enum: ConsentStatus, default: ConsentStatus.PENDING })
  status: ConsentStatus;

  @Column({ type: 'enum', enum: ConsentMethod })
  method: ConsentMethod;

  @Column({ type: 'text' })
  description: string; // What the user consented to

  @Column({ type: 'varchar', length: 255, nullable: true })
  legalBasis: string; // LGPD legal basis (Art. 7)

  @Column({ type: 'json', nullable: true })
  dataCategories: string[]; // Categories of personal data

  @Column({ type: 'json', nullable: true })
  processingPurposes: string[]; // Specific processing purposes

  @Column({ type: 'json', nullable: true })
  recipients: string[]; // Who will receive the data

  @Column({ type: 'varchar', length: 255, nullable: true })
  retentionPeriod: string; // How long data will be kept

  @Column({ type: 'timestamp', nullable: true })
  @Index()
  expiresAt: Date; // When consent expires

  @Column({ type: 'varchar', length: 255, nullable: true })
  source: string; // Where consent was obtained

  @Column({ type: 'varchar', length: 45, nullable: true })
  ipAddress: string; // IP address when consent was given

  @Column({ type: 'varchar', length: 500, nullable: true })
  userAgent: string; // User agent when consent was given

  @Column({ type: 'varchar', length: 255, nullable: true })
  consentString: string; // IAB consent string if applicable

  @Column({ type: 'json', nullable: true })
  evidence: Record<string, any>; // Evidence of consent

  @Column({ type: 'timestamp', nullable: true })
  @Index()
  withdrawnAt: Date; // When consent was withdrawn

  @Column({ type: 'varchar', length: 255, nullable: true })
  withdrawnBy: string; // Who withdrew consent

  @Column({ type: 'text', nullable: true })
  withdrawalReason: string; // Reason for withdrawal

  @Column({ type: 'varchar', length: 255, nullable: true })
  parentConsentId: string; // For child consents

  @Column({ type: 'boolean', default: false })
  isMinor: boolean; // Special handling for minors

  @Column({ type: 'varchar', length: 255, nullable: true })
  guardianId: string; // Guardian for minors

  @Column({ type: 'json', nullable: true })
  preferences: Record<string, any>; // Granular preferences

  @Column({ type: 'varchar', length: 255, nullable: true })
  version: string; // Privacy policy version

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  static createConsent(data: {
    dataSubjectId: string;
    purpose: ConsentPurpose;
    method: ConsentMethod;
    description: string;
    legalBasis?: string;
    dataCategories?: string[];
    processingPurposes?: string[];
    recipients?: string[];
    retentionPeriod?: string;
    expiresAt?: Date;
    source?: string;
    ipAddress?: string;
    userAgent?: string;
    consentString?: string;
    evidence?: Record<string, any>;
    parentConsentId?: string;
    isMinor?: boolean;
    guardianId?: string;
    preferences?: Record<string, any>;
    version?: string;
    metadata?: Record<string, any>;
  }): Partial<LgpdConsent> {
    return {
      dataSubjectId: data.dataSubjectId,
      purpose: data.purpose,
      status: ConsentStatus.GIVEN,
      method: data.method,
      description: data.description,
      legalBasis: data.legalBasis,
      dataCategories: data.dataCategories,
      processingPurposes: data.processingPurposes,
      recipients: data.recipients,
      retentionPeriod: data.retentionPeriod,
      expiresAt: data.expiresAt,
      source: data.source,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      consentString: data.consentString,
      evidence: data.evidence,
      parentConsentId: data.parentConsentId,
      isMinor: data.isMinor || false,
      guardianId: data.guardianId,
      preferences: data.preferences,
      version: data.version,
      metadata: data.metadata,
    };
  }

  getPurposeLabel(): string {
    const labels = {
      [ConsentPurpose.MARKETING]: 'Marketing',
      [ConsentPurpose.ANALYTICS]: 'Análise e Estatísticas',
      [ConsentPurpose.PERSONALIZATION]: 'Personalização',
      [ConsentPurpose.COMMUNICATION]: 'Comunicação',
      [ConsentPurpose.PROFILING]: 'Perfilagem',
      [ConsentPurpose.THIRD_PARTY_SHARING]: 'Compartilhamento com Terceiros',
      [ConsentPurpose.COOKIES]: 'Cookies',
      [ConsentPurpose.LOCATION_TRACKING]: 'Rastreamento de Localização',
      [ConsentPurpose.BEHAVIORAL_ADVERTISING]: 'Publicidade Comportamental',
      [ConsentPurpose.RESEARCH]: 'Pesquisa',
      [ConsentPurpose.LEGAL_COMPLIANCE]: 'Conformidade Legal',
      [ConsentPurpose.SECURITY]: 'Segurança',
    };

    return labels[this.purpose] || this.purpose;
  }

  getStatusLabel(): string {
    const labels = {
      [ConsentStatus.GIVEN]: 'Concedido',
      [ConsentStatus.WITHDRAWN]: 'Retirado',
      [ConsentStatus.EXPIRED]: 'Expirado',
      [ConsentStatus.PENDING]: 'Pendente',
      [ConsentStatus.REFUSED]: 'Recusado',
    };

    return labels[this.status] || this.status;
  }

  getMethodLabel(): string {
    const labels = {
      [ConsentMethod.EXPLICIT]: 'Consentimento Explícito',
      [ConsentMethod.OPT_IN]: 'Opt-in',
      [ConsentMethod.OPT_OUT]: 'Opt-out',
      [ConsentMethod.LEGITIMATE_INTEREST]: 'Interesse Legítimo',
      [ConsentMethod.CONTRACTUAL]: 'Necessário para Contrato',
      [ConsentMethod.LEGAL_OBLIGATION]: 'Obrigação Legal',
      [ConsentMethod.VITAL_INTERESTS]: 'Interesses Vitais',
    };

    return labels[this.method] || this.method;
  }

  isActive(): boolean {
    return this.status === ConsentStatus.GIVEN && !this.isExpired();
  }

  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  isWithdrawn(): boolean {
    return this.status === ConsentStatus.WITHDRAWN;
  }

  getDaysUntilExpiry(): number | null {
    if (!this.expiresAt) return null;
    
    const now = new Date();
    const diffMs = this.expiresAt.getTime() - now.getTime();
    return Math.ceil(diffMs / (1000 * 60 * 60 * 24));
  }

  requiresRenewal(): boolean {
    const daysUntilExpiry = this.getDaysUntilExpiry();
    return daysUntilExpiry !== null && daysUntilExpiry <= 30; // Renew within 30 days
  }

  withdraw(withdrawnBy?: string, reason?: string): void {
    this.status = ConsentStatus.WITHDRAWN;
    this.withdrawnAt = new Date();
    this.withdrawnBy = withdrawnBy;
    this.withdrawalReason = reason;
  }

  renew(expiresAt?: Date): void {
    if (this.status === ConsentStatus.WITHDRAWN) {
      throw new Error('Cannot renew withdrawn consent');
    }
    
    this.status = ConsentStatus.GIVEN;
    this.expiresAt = expiresAt || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year default
    this.withdrawnAt = null;
    this.withdrawnBy = null;
    this.withdrawalReason = null;
  }

  getConsentDuration(): number {
    const start = this.createdAt;
    const end = this.withdrawnAt || new Date();
    return end.getTime() - start.getTime();
  }

  getFormattedConsentDuration(): string {
    const durationMs = this.getConsentDuration();
    const days = Math.floor(durationMs / (1000 * 60 * 60 * 24));
    
    if (days > 365) {
      const years = Math.floor(days / 365);
      return `${years} ano${years > 1 ? 's' : ''}`;
    } else if (days > 30) {
      const months = Math.floor(days / 30);
      return `${months} mês${months > 1 ? 'es' : ''}`;
    } else {
      return `${days} dia${days > 1 ? 's' : ''}`;
    }
  }

  isValidForProcessing(): boolean {
    return this.isActive() && !this.isExpired();
  }

  requiresParentalConsent(): boolean {
    return this.isMinor && !this.guardianId;
  }
}
