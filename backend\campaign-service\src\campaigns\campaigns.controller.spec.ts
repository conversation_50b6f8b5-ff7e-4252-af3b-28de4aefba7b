import { Test, TestingModule } from '@nestjs/testing';
import { CampaignsController } from './campaigns.controller';
import { CampaignsService } from './campaigns.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

describe('CampaignsController', () => {
  let controller: CampaignsController;
  let service: CampaignsService;

  const mockCampaign = {
    id: '1',
    name: 'Test Campaign',
    startDate: new Date(),
    endDate: new Date(),
    status: 'draft',
    budget: 1000,
  };

  const mockRequest = {
      user: {
          sub: '1',
          role: 'admin'
      }
  }

  const mockCampaignsService = {
    findAll: jest.fn().mockResolvedValue({
      data: [mockCampaign],
      meta: { total: 1, page: 1, limit: 20, totalPages: 1 }
    }),
    findOne: jest.fn().mockResolvedValue(mockCampaign),
    create: jest.fn().mockResolvedValue(mockCampaign),
    update: jest.fn().mockResolvedValue(mockCampaign),
    activate: jest.fn().mockResolvedValue(mockCampaign),
    pause: jest.fn().mockResolvedValue(mockCampaign),
    resume: jest.fn().mockResolvedValue(mockCampaign),
    end: jest.fn().mockResolvedValue(mockCampaign),
    schedule: jest.fn().mockResolvedValue(mockCampaign),
    getMetrics: jest.fn().mockResolvedValue({})
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CampaignsController],
      providers: [
        {
          provide: CampaignsService,
          useValue: mockCampaignsService,
        },
        ConfigService,
        JwtService,
      ],
    }).overrideGuard(JwtAuthGuard).useValue({ canActivate: () => true })
      .overrideGuard(RolesGuard).useValue({ canActivate: () => true })
      .compile();

    controller = module.get<CampaignsController>(CampaignsController);
    service = module.get<CampaignsService>(CampaignsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return an array of campaigns', async () => {
    const result = await controller.findAll({}, mockRequest);
    expect(result.data).toEqual([mockCampaign]);
    expect(result.meta).toBeDefined();
  });

  it('should return a single campaign', async () => {
    const campaign = await controller.findOne('1', mockRequest);
    expect(campaign).toEqual(mockCampaign);
  });

  it('should create a campaign', async () => {
    const campaign = await controller.create(mockCampaign as any, mockRequest);
    expect(campaign).toEqual(mockCampaign);
  });

  it('should update a campaign', async () => {
    const campaign = await controller.update('1', mockCampaign as any, mockRequest);
    expect(campaign).toEqual(mockCampaign);
  });

  it('should activate a campaign', async () => {
      const campaign = await controller.activate('1', mockRequest);
      expect(campaign).toEqual(mockCampaign)
  });

  it('should pause a campaign', async () => {
      const campaign = await controller.pause('1', {}, mockRequest);
      expect(campaign).toEqual(mockCampaign)
  });

  it('should resume a campaign', async () => {
      const campaign = await controller.resume('1', mockRequest);
      expect(campaign).toEqual(mockCampaign)
  });

  it('should end a campaign', async () => {
      const campaign = await controller.end('1', {}, mockRequest);
      expect(campaign).toEqual(mockCampaign)
  });
});

