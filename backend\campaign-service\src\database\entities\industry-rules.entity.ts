import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum RuleType {
  MINIMUM_PERCENTAGE = 'minimum_percentage',
  MINIMUM_VALUE = 'minimum_value',
  PRODUCT_LIMIT_PER_CYCLE = 'product_limit_per_cycle',
  INCENTIVE_VALIDITY_DAYS = 'incentive_validity_days',
  MAXIMUM_PERCENTAGE = 'maximum_percentage',
  MAXIMUM_VALUE = 'maximum_value',
}

export enum RuleStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
}

@Entity('industry_rules')
@Index(['industryId', 'ruleType'], { unique: true })
@Index(['industryId'])
@Index(['ruleType'])
export class IndustryRule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  industryId: string;

  @Column({
    type: 'enum',
    enum: RuleType,
  })
  ruleType: RuleType;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  minValue: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxValue: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  minPercentage: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  maxPercentage: number;

  @Column({ type: 'integer', nullable: true })
  integerValue: number;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  @Column({
    type: 'enum',
    enum: RuleStatus,
    default: RuleStatus.ACTIVE,
  })
  status: RuleStatus;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'uuid' })
  createdBy: string;

  @Column({ type: 'uuid', nullable: true })
  updatedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods for validation
  validateMinimumPercentage(percentage: number): boolean {
    if (!this.enabled || this.ruleType !== RuleType.MINIMUM_PERCENTAGE) {
      return true;
    }
    return percentage >= this.minPercentage;
  }

  validateMinimumValue(value: number): boolean {
    if (!this.enabled || this.ruleType !== RuleType.MINIMUM_VALUE) {
      return true;
    }
    return value >= this.minValue;
  }

  validateMaximumPercentage(percentage: number): boolean {
    if (!this.enabled || this.ruleType !== RuleType.MAXIMUM_PERCENTAGE) {
      return true;
    }
    return percentage <= this.maxPercentage;
  }

  validateMaximumValue(value: number): boolean {
    if (!this.enabled || this.ruleType !== RuleType.MAXIMUM_VALUE) {
      return true;
    }
    return value <= this.maxValue;
  }

  validateProductLimit(productCount: number): boolean {
    if (!this.enabled || this.ruleType !== RuleType.PRODUCT_LIMIT_PER_CYCLE) {
      return true;
    }
    return productCount <= this.integerValue;
  }

  validateIncentiveValidity(days: number): boolean {
    if (!this.enabled || this.ruleType !== RuleType.INCENTIVE_VALIDITY_DAYS) {
      return true;
    }
    return days >= 1 && days <= this.integerValue;
  }

  getValidationErrorMessage(value: number, type: 'percentage' | 'value' | 'count' | 'days'): string {
    switch (this.ruleType) {
      case RuleType.MINIMUM_PERCENTAGE:
        return `Incentivo abaixo do mínimo de ${this.minPercentage}%`;
      case RuleType.MINIMUM_VALUE:
        return `Incentivo abaixo do mínimo de R$ ${this.minValue.toFixed(2)}`;
      case RuleType.MAXIMUM_PERCENTAGE:
        return `Incentivo acima do máximo de ${this.maxPercentage}%`;
      case RuleType.MAXIMUM_VALUE:
        return `Incentivo acima do máximo de R$ ${this.maxValue.toFixed(2)}`;
      case RuleType.PRODUCT_LIMIT_PER_CYCLE:
        return `Limite de ${this.integerValue} produtos por ciclo excedido`;
      case RuleType.INCENTIVE_VALIDITY_DAYS:
        return `Validade deve ser entre 1 e ${this.integerValue} dias`;
      default:
        return 'Valor inválido para esta regra';
    }
  }

  toJSON() {
    return {
      id: this.id,
      industryId: this.industryId,
      ruleType: this.ruleType,
      minValue: this.minValue,
      maxValue: this.maxValue,
      minPercentage: this.minPercentage,
      maxPercentage: this.maxPercentage,
      integerValue: this.integerValue,
      enabled: this.enabled,
      status: this.status,
      description: this.description,
      metadata: this.metadata,
      createdBy: this.createdBy,
      updatedBy: this.updatedBy,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
