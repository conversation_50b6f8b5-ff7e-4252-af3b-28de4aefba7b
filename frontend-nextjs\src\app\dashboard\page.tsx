'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
// Removed shadcn/ui components for simplicity
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Eye, 
  MousePointer, 
  ShoppingCart,
  LogOut,
  Settings,
  Plus
} from 'lucide-react';

function DashboardPage() {
  const { user, logout, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  const handleNewCampaign = () => {
    router.push('/campaigns/create');
  };

  const handleReports = () => {
    router.push('/reports');
  };

  // const handleAudiences = () => {
  //   router.push('/audiences');
  // };

  const handleSettings = () => {
    router.push('/settings');
  };

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  const stats = [
    {
      title: 'Campanhas Ativas',
      value: '12',
      change: '+2 esta semana',
      icon: BarChart3,
      color: 'text-blue-600',
    },
    // {
    //   title: 'Impressões',
    //   value: '2.4M',
    //   change: '+15% vs mês anterior',
    //   icon: Eye,
    //   color: 'text-green-600',
    // },
    // {
    //   title: 'Cliques',
    //   value: '48.2K',
    //   change: '+8% vs mês anterior',
    //   icon: MousePointer,
    //   color: 'text-purple-600',
    // },
    // {
    //   title: 'Conversões',
    //   value: '1.2K',
    //   change: '+12% vs mês anterior',
    //   icon: ShoppingCart,
    //   color: 'text-orange-600',
    // },
    {
      title: 'Receita',
      value: 'R$ 125.4K',
      change: '+18% vs mês anterior',
      icon: DollarSign,
      color: 'text-emerald-600',
    },
    {
      title: 'ROAS',
      value: '4.2x',
      change: '+0.3x vs mês anterior',
      icon: TrendingUp,
      color: 'text-indigo-600',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Retail Media
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-800">
                Olá, {user?.name || user?.email}
              </span>
              <button
                onClick={handleSettings}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Settings className="h-4 w-4 mr-2" />
                Configurações
              </button>
              <button
                onClick={logout}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sair
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Dashboard
          </h2>
          <p className="text-gray-700">
            Acompanhe o desempenho das suas campanhas de retail media
          </p>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-4">
            <button
              onClick={handleNewCampaign}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm !text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2 !text-white" />
              Nova Campanha
            </button>
            <button
              onClick={handleReports}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Relatórios
            </button>
            {/* <button
              onClick={handleAudiences}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Users className="h-4 w-4 mr-2" />
              Audiências
            </button> */}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-700">
                    {stat.title}
                  </h3>
                  <Icon className={`h-5 w-5 ${stat.color}`} />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {stat.value}
                </div>
                <p className="text-xs text-gray-700">
                  {stat.change}
                </p>
              </div>
            );
          })}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Campanhas Recentes</h3>
              <p className="text-sm text-gray-700">
                Suas campanhas mais recentes e seu status
              </p>
            </div>
            <div>
              <div className="space-y-4">
                {[
                  { name: 'Campanha Black Friday 2024', status: 'Ativa', performance: '+15%' },
                  { name: 'Promoção Natal Antecipado', status: 'Pausada', performance: '+8%' },
                  { name: 'Lançamento Produto X', status: 'Ativa', performance: '+22%' },
                ].map((campaign, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{campaign.name}</p>
                      <p className="text-sm text-gray-500">Status: {campaign.status}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-green-600">{campaign.performance}</p>
                      <p className="text-xs text-gray-500">vs. meta</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* <div className="bg-white rounded-lg shadow p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Performance Geral</h3>
              <p className="text-sm text-gray-700">
                Resumo do desempenho dos últimos 30 dias
              </p>
            </div>
            <div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">CTR Médio</span>
                  <span className="font-medium">2.1%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">CPC Médio</span>
                  <span className="font-medium">R$ 0.85</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Taxa de Conversão</span>
                  <span className="font-medium">2.5%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">ROAS Médio</span>
                  <span className="font-medium text-green-600">4.2x</span>
                </div>
              </div>
            </div>
          </div> */}
        </div>
      </main>
    </div>
  );
}

export default DashboardPage;
