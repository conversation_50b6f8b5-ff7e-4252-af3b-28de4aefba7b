services:
  # Database Services
  postgres-auth:
    image: postgres:15-alpine
    container_name: retail-media-postgres-auth
    environment:
      POSTGRES_DB: retail_media_auth
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_auth_data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres/init-auth.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - retail-media-network

  postgres-campaign:
    image: postgres:15-alpine
    container_name: retail-media-postgres-campaign
    environment:
      POSTGRES_DB: retail_media_campaign
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5433:5432"
    volumes:
      - postgres_campaign_data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres/init-campaign.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - retail-media-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: retail-media-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass redis123
    networks:
      - retail-media-network

  # RabbitMQ for message queuing
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: retail-media-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: rabbitmq
      RABBITMQ_DEFAULT_PASS: rabbitmq123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - retail-media-network

  # Backend Services
  auth-service:
    build:
      context: ./backend/auth-service
      dockerfile: Dockerfile
    container_name: retail-media-auth-service
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ****************************************************/retail_media_auth
      REDIS_URL: redis://:redis123@redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 8h
      JWT_REFRESH_EXPIRES_IN: 7d
    ports:
      - "3001:3001"
    depends_on:
      - postgres-auth
      - redis
    volumes:
      - ./backend/auth-service:/app
      - /app/node_modules
    networks:
      - retail-media-network
    command: npm run start:dev

  campaign-service:
    build:
      context: ./backend/campaign-service
      dockerfile: Dockerfile
    container_name: retail-media-campaign-service
    environment:
      NODE_ENV: development
      PORT: 3002
      DATABASE_URL: ********************************************************/retail_media_campaign
      REDIS_URL: redis://:redis123@redis:6379
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: redis123
      REDIS_DB: 1
      RABBITMQ_URL: amqp://rabbitmq:rabbitmq123@rabbitmq:5672
      AUTH_SERVICE_URL: http://auth-service:3001
    ports:
      - "3002:3002"
    depends_on:
      - postgres-campaign
      - redis
      - rabbitmq
      - auth-service
    volumes:
      - ./backend/campaign-service:/app
      - /app/node_modules
    networks:
      - retail-media-network
    command: npm run start:dev

  # Frontend Next.js
  frontend:
    build:
      context: ./frontend-nextjs
      dockerfile: Dockerfile
    container_name: retail-media-frontend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8080
    ports:
      - "3000:3000"
    depends_on:
      - auth-service
      - campaign-service
    volumes:
      - ./frontend-nextjs:/app
      - /app/node_modules
      - /app/.next
    networks:
      - retail-media-network
    command: npm run dev

  # API Gateway (Nginx)
  api-gateway:
    image: nginx:alpine
    container_name: retail-media-api-gateway
    ports:
      - "8080:80"
    volumes:
      - ./infrastructure/docker/nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - auth-service
      - campaign-service
    networks:
      - retail-media-network

volumes:
  postgres_auth_data:
  postgres_campaign_data:
  redis_data:
  rabbitmq_data:

networks:
  retail-media-network:
    driver: bridge


