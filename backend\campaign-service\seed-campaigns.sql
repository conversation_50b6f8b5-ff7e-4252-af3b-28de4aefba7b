-- Seed data for campaigns table
-- This script creates fictional campaign data for testing

-- First, let's check if we have any industries (we'll use a default one if not)
-- Insert campaigns with realistic data

INSERT INTO campaigns (
  id,
  name,
  description,
  "industryId",
  "createdBy",
  status,
  "incentiveType",
  "incentiveValue",
  "startDate",
  "endDate",
  "totalInvestment",
  "totalRevenue",
  "impactedConsumers",
  "convertedConsumers",
  "conversionRate",
  roi,
  "createdAt",
  "updatedAt"
) VALUES
-- Campaign 1: Active Electronics Summer Promotion
(
  '0c220932-22f3-4bb5-9b67-7674cb9290c4',
  'Promoção Eletrônicos Verão',
  'Campanha de desconto em eletrônicos para o verão 2025',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'active',
  'percentage',
  15.00,
  '2025-01-01',
  '2025-03-31',
  14000.00,
  40000.00,
  1800,
  540,
  30.00,
  186.36,
  NOW(),
  NOW()
),
-- Campaign 2: Active Black Friday 2024
(
  '8ef1b7af-e7b3-41f2-9545-c7123b6308f2',
  'Campanha Black Friday 2024',
  'Mega promoção de Black Friday com descontos especiais',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'active',
  'percentage',
  25.00,
  '2024-11-20',
  '2024-11-30',
  19500.00,
  50000.00,
  2500,
  875,
  35.00,
  156.41,
  NOW(),
  NOW()
),
-- Campaign 3: Ended Back to School 2024
(
  '88ece6ed-a420-4c0a-b485-6dd81a485edf',
  'Volta às Aulas 2024',
  'Campanha especial para material escolar e eletrônicos',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'ended',
  'percentage',
  20.00,
  '2024-01-15',
  '2024-02-28',
  32000.00,
  70000.00,
  1500,
  420,
  28.00,
  118.75,
  NOW(),
  NOW()
),
-- Campaign 4: Paused Christmas 2024
(
  'f31f783e-3849-4051-8e67-f7bc0110b0f9',
  'Campanha Natal 2024',
  'Promoções especiais de Natal',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'paused',
  'percentage',
  30.00,
  '2024-12-01',
  '2024-12-25',
  15000.00,
  30000.00,
  1200,
  300,
  25.00,
  100.00,
  NOW(),
  NOW()
),
-- Campaign 5: Draft Summer 2025
(
  'ee31219a-d6ff-4efd-bb81-ff55919d36a4',
  'Promoção Verão 2025',
  'Campanha de verão com produtos selecionados',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'draft',
  'percentage',
  18.00,
  '2025-12-01',
  '2025-02-28',
  0.00,
  0.00,
  0,
  0,
  0.00,
  0.00,
  NOW(),
  NOW()
),
-- Campaign 6: Active Mother's Day
(
  'a1b2c3d4-e5f6-4a5b-8c9d-0e1f2a3b4c5d',
  'Dia das Mães 2025',
  'Presentes especiais para o Dia das Mães',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'active',
  'percentage',
  22.00,
  '2025-04-20',
  '2025-05-15',
  18000.00,
  45000.00,
  2000,
  650,
  32.50,
  150.00,
  NOW(),
  NOW()
),
-- Campaign 7: Active Father's Day
(
  'b2c3d4e5-f6a7-4b5c-9d0e-1f2a3b4c5d6e',
  'Dia dos Pais 2025',
  'Ofertas especiais para o Dia dos Pais',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'active',
  'percentage',
  20.00,
  '2025-07-20',
  '2025-08-15',
  16000.00,
  38000.00,
  1700,
  510,
  30.00,
  137.50,
  NOW(),
  NOW()
),
-- Campaign 8: Ended Easter 2024
(
  'c3d4e5f6-a7b8-4c5d-0e1f-2a3b4c5d6e7f',
  'Páscoa 2024',
  'Promoção de Páscoa com chocolates e presentes',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'ended',
  'percentage',
  15.00,
  '2024-03-15',
  '2024-04-10',
  12000.00,
  28000.00,
  1300,
  390,
  30.00,
  133.33,
  NOW(),
  NOW()
),
-- Campaign 9: Active Winter Sale
(
  'd4e5f6a7-b8c9-4d5e-1f2a-3b4c5d6e7f8a',
  'Liquidação de Inverno',
  'Mega liquidação de produtos de inverno',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'active',
  'percentage',
  35.00,
  '2025-06-01',
  '2025-07-31',
  25000.00,
  55000.00,
  2200,
  770,
  35.00,
  120.00,
  NOW(),
  NOW()
),
-- Campaign 10: Draft Cyber Monday
(
  'e5f6a7b8-c9d0-4e5f-2a3b-4c5d6e7f8a9b',
  'Cyber Monday 2025',
  'Ofertas exclusivas online para Cyber Monday',
  '00000000-0000-0000-0000-000000000001',
  'system',
  'draft',
  'percentage',
  28.00,
  '2025-11-28',
  '2025-12-02',
  0.00,
  0.00,
  0,
  0,
  0.00,
  0.00,
  NOW(),
  NOW()
);

-- Verify the inserted data
SELECT 
  id,
  name,
  status,
  "startDate",
  "endDate",
  "impactedConsumers" as reach,
  "convertedConsumers" as conversions,
  "totalRevenue" as revenue,
  roi
FROM campaigns
ORDER BY "createdAt" DESC;

