import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ReportsService } from './reports.service';
import { CampaignReport } from './entities/campaign-report.entity';
import { Product } from '../database/entities/product.entity';
import { ProductCategory } from '../database/entities/product-category.entity';
import { ExportService } from './export.service';

describe('ReportsService', () => {
  let service: ReportsService;
  let campaignRepository: Repository<CampaignReport>;
  let productRepository: Repository<Product>;
  let categoryRepository: Repository<ProductCategory>;
  let exportService: ExportService;

  const mockCampaignRepository = {
    createQueryBuilder: jest.fn(() => ({
      andWhere: jest.fn().mockReturnThis(),
      getMany: jest.fn().mockResolvedValue([
        {
          id: '1',
          name: 'Test Campaign',
          status: 'active',
          startDate: new Date('2025-01-01'),
          endDate: new Date('2025-12-31'),
          impactedConsumers: 1000,
          convertedConsumers: 100,
          totalRevenue: 50000,
          roi: 150,
          incentiveValue: 15,
        },
      ]),
    })),
  };

  const mockProductRepository = {
    createQueryBuilder: jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getMany: jest.fn().mockResolvedValue([]),
    })),
  };

  const mockCategoryRepository = {
    find: jest.fn().mockResolvedValue([]),
  };

  const mockExportService = {
    exportToExcel: jest.fn().mockResolvedValue('/path/to/file.xlsx'),
    exportToPDF: jest.fn().mockResolvedValue('/path/to/file.pdf'),
    getFileStats: jest.fn().mockReturnValue({ exists: true, size: 1024 }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReportsService,
        {
          provide: getRepositoryToken(CampaignReport),
          useValue: mockCampaignRepository,
        },
        {
          provide: getRepositoryToken(Product),
          useValue: mockProductRepository,
        },
        {
          provide: getRepositoryToken(ProductCategory),
          useValue: mockCategoryRepository,
        },
        {
          provide: ExportService,
          useValue: mockExportService,
        },
      ],
    }).compile();

    service = module.get<ReportsService>(ReportsService);
    campaignRepository = module.get<Repository<CampaignReport>>(
      getRepositoryToken(CampaignReport),
    );
    productRepository = module.get<Repository<Product>>(
      getRepositoryToken(Product),
    );
    categoryRepository = module.get<Repository<ProductCategory>>(
      getRepositoryToken(ProductCategory),
    );
    exportService = module.get<ExportService>(ExportService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateCampaignPerformanceReport', () => {
    it('should generate campaign performance report with filters', async () => {
      const filters = {
        dateRange: {
          startDate: '2025-01-01',
          endDate: '2025-12-31',
        },
        status: ['active'],
      };

      const result = await service.generateCampaignPerformanceReport(filters);

      expect(result).toBeDefined();
      expect(result.summary).toBeDefined();
      expect(result.campaigns).toBeDefined();
      expect(result.trends).toBeDefined();
      expect(result.topPerformers).toBeDefined();
      expect(result.summary.totalCampaigns).toBe(1);
      expect(result.summary.activeCampaigns).toBe(1);
    });

    it('should handle empty campaign results', async () => {
      mockCampaignRepository.createQueryBuilder = jest.fn(() => ({
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      }));

      const result = await service.generateCampaignPerformanceReport({});

      expect(result.summary.totalCampaigns).toBe(0);
      expect(result.campaigns).toHaveLength(0);
    });

    it('should calculate summary metrics correctly', async () => {
      const result = await service.generateCampaignPerformanceReport({});

      expect(result.summary.totalRevenue).toBeGreaterThanOrEqual(0);
      expect(result.summary.totalCampaigns).toBeGreaterThanOrEqual(0);
    });
  });

  describe('generateProductPerformanceReport', () => {
    it('should generate product performance report', async () => {
      const result = await service.generateProductPerformanceReport({});

      expect(result).toBeDefined();
      expect(result.summary).toBeDefined();
      expect(result.products).toBeDefined();
      expect(result.categories).toBeDefined();
      expect(result.insights).toBeDefined();
    });

    it('should handle errors gracefully', async () => {
      mockProductRepository.createQueryBuilder = jest.fn(() => ({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockRejectedValue(new Error('Database error')),
      }));

      await expect(
        service.generateProductPerformanceReport({}),
      ).rejects.toThrow('Database error');
    });
  });

  describe('exportReport', () => {
    beforeEach(() => {
      mockProductRepository.createQueryBuilder = jest.fn(() => ({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      }));
    });

    it('should export campaign report as XLSX', async () => {
      const result = await service.exportReport(
        'campaign',
        {},
        { format: 'xlsx', includeCharts: true, includeRawData: true },
      );

      expect(result).toBeDefined();
      expect(result.downloadUrl).toContain('.xlsx');
      expect(result.filename).toContain('campaign_report');
      expect(exportService.exportToExcel).toHaveBeenCalled();
    });

    it('should export campaign report as PDF', async () => {
      const result = await service.exportReport(
        'campaign',
        {},
        { format: 'pdf', includeCharts: false, includeRawData: false },
      );

      expect(result).toBeDefined();
      expect(result.downloadUrl).toContain('.pdf');
      expect(result.filename).toContain('campaign_report');
      expect(exportService.exportToPDF).toHaveBeenCalled();
    });
  });

  describe('generateIncentiveAnalysisReport', () => {
    beforeEach(() => {
      mockProductRepository.createQueryBuilder = jest.fn(() => ({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      }));
    });

    it('should generate incentive analysis report', async () => {
      const result = await service.generateIncentiveAnalysisReport({});

      expect(result).toBeDefined();
      expect(result.summary).toBeDefined();
      expect(result.incentiveRanges).toBeDefined();
      expect(result.productAnalysis).toBeDefined();
      expect(result.trends).toBeDefined();
      expect(result.recommendations).toBeDefined();
    });
  });

  describe('generateUserActivityReport', () => {
    it('should generate user activity report', async () => {
      const result = await service.generateUserActivityReport({});

      expect(result).toBeDefined();
      expect(result.summary).toBeDefined();
      expect(result.activity).toBeDefined();
      expect(result.topUsers).toBeDefined();
      expect(result.userTypes).toBeDefined();
    });
  });
});

