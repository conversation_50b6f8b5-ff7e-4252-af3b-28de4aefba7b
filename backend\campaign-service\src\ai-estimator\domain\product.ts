/**
 * Domain model representing a product for estimation purposes
 */
export class ProductForEstimation {
  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly price: number,
    public readonly category: string,
    public readonly brand: string,
    public readonly salesCount: number,
    public readonly averageRating: number,
    public readonly isEligibleForIncentives: boolean
  ) {
    this.validateProduct();
  }

  private validateProduct(): void {
    if (!this.id || this.id.trim().length === 0) {
      throw new Error('Product ID cannot be empty');
    }
    
    if (this.price < 0) {
      throw new Error('Product price cannot be negative');
    }
    
    if (this.salesCount < 0) {
      throw new Error('Sales count cannot be negative');
    }
    
    if (this.averageRating < 0 || this.averageRating > 5) {
      throw new Error('Average rating must be between 0 and 5');
    }
  }

  get popularityScore(): number {
    // Normalize sales count and rating to calculate popularity
    const salesScore = Math.min(this.salesCount / 1000, 1); // Cap at 1000 sales
    const ratingScore = this.averageRating / 5;
    return (salesScore * 0.7) + (ratingScore * 0.3);
  }

  get priceCategory(): 'budget' | 'mid-range' | 'premium' {
    if (this.price <= 20) return 'budget';
    if (this.price <= 100) return 'mid-range';
    return 'premium';
  }

  isHighPerforming(): boolean {
    return this.salesCount > 500 && this.averageRating >= 4.0;
  }
}
