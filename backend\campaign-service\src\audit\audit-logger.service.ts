import {
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AuditLog, AuditAction, AuditLevel, AuditCategory } from '../database/entities/audit-log.entity';
import { SecurityEvent, SecurityEventType, SecuritySeverity } from '../database/entities/security-event.entity';
import { DataAccessLog, DataAccessType, DataSensitivity, AccessPurpose } from '../database/entities/data-access-log.entity';
import { ComplianceEvent, ComplianceType, ComplianceEventType } from '../database/entities/compliance-event.entity';

@Injectable()
export class AuditLoggerService {
  private readonly logger = new Logger(AuditLoggerService.name);

  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    @InjectRepository(SecurityEvent)
    private readonly securityEventRepository: Repository<SecurityEvent>,
    @InjectRepository(DataAccessLog)
    private readonly dataAccessLogRepository: Repository<DataAccessLog>,
    @InjectRepository(ComplianceEvent)
    private readonly complianceEventRepository: Repository<ComplianceEvent>,
  ) {}

  async logAuditEvent(data: {
    userId?: string;
    sessionId?: string;
    action: AuditAction;
    level?: AuditLevel;
    category: AuditCategory;
    entityType?: string;
    entityId?: string;
    description: string;
    details?: Record<string, any>;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
    source?: string;
    correlationId?: string;
    duration?: number;
    isSuccessful?: boolean;
    errorMessage?: string;
    errorCode?: string;
    metadata?: Record<string, any>;
  }): Promise<AuditLog> {
    try {
      const auditLog = this.auditLogRepository.create(
        AuditLog.createAuditLog(data)
      );

      const savedLog = await this.auditLogRepository.save(auditLog);
      
      // Log to console for immediate visibility
      const logLevel = data.level || AuditLevel.INFO;
      const message = `[${data.category}] ${data.action}: ${data.description}`;
      
      switch (logLevel) {
        case AuditLevel.CRITICAL:
        case AuditLevel.ERROR:
          this.logger.error(message, data.errorMessage);
          break;
        case AuditLevel.WARNING:
          this.logger.warn(message);
          break;
        default:
          this.logger.log(message);
      }

      return savedLog;
    } catch (error) {
      this.logger.error('Failed to save audit log:', error);
      throw error;
    }
  }

  async logSecurityEvent(data: {
    userId?: string;
    sessionId?: string;
    type: SecurityEventType;
    severity?: SecuritySeverity;
    description: string;
    ipAddress?: string;
    userAgent?: string;
    endpoint?: string;
    httpMethod?: string;
    httpStatusCode?: number;
    referer?: string;
    countryCode?: string;
    city?: string;
    requestHeaders?: Record<string, any>;
    requestBody?: Record<string, any>;
    responseData?: Record<string, any>;
    riskFactors?: Record<string, any>;
    riskScore?: number;
    isBlocked?: boolean;
    isAutomated?: boolean;
    detectionRule?: string;
    correlationId?: string;
    mitigationActions?: string[];
    metadata?: Record<string, any>;
  }): Promise<SecurityEvent> {
    try {
      const securityEvent = this.securityEventRepository.create(
        SecurityEvent.createSecurityEvent(data)
      );

      const savedEvent = await this.securityEventRepository.save(securityEvent);
      
      // Log to console for immediate visibility
      const severity = data.severity || SecuritySeverity.MEDIUM;
      const message = `[SECURITY] ${data.type}: ${data.description}`;
      
      switch (severity) {
        case SecuritySeverity.CRITICAL:
          this.logger.error(`🚨 CRITICAL SECURITY EVENT: ${message}`);
          break;
        case SecuritySeverity.HIGH:
          this.logger.error(`⚠️ HIGH SECURITY EVENT: ${message}`);
          break;
        case SecuritySeverity.MEDIUM:
          this.logger.warn(`⚡ MEDIUM SECURITY EVENT: ${message}`);
          break;
        default:
          this.logger.log(`ℹ️ LOW SECURITY EVENT: ${message}`);
      }

      // Also create an audit log for security events
      await this.logAuditEvent({
        userId: data.userId,
        sessionId: data.sessionId,
        action: AuditAction.READ, // or appropriate action
        level: this.mapSecuritySeverityToAuditLevel(severity),
        category: AuditCategory.SECURITY,
        description: `Security event: ${data.description}`,
        details: { securityEventId: savedEvent.id, type: data.type },
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        correlationId: data.correlationId,
        isSuccessful: !data.isBlocked,
        metadata: data.metadata,
      });

      return savedEvent;
    } catch (error) {
      this.logger.error('Failed to save security event:', error);
      throw error;
    }
  }

  async logDataAccess(data: {
    userId: string;
    sessionId?: string;
    accessType: DataAccessType;
    dataType: string;
    dataId?: string;
    sensitivity?: DataSensitivity;
    purpose: AccessPurpose;
    description: string;
    endpoint?: string;
    httpMethod?: string;
    queryParameters?: Record<string, any>;
    requestBody?: Record<string, any>;
    recordCount?: number;
    responseSize?: number;
    ipAddress?: string;
    userAgent?: string;
    countryCode?: string;
    city?: string;
    isAuthorized?: boolean;
    authorizationMethod?: string;
    permissions?: string[];
    businessJustification?: string;
    approvedBy?: string;
    approvedAt?: Date;
    duration?: number;
    isExported?: boolean;
    exportFormat?: string;
    exportDestination?: string;
    dataFields?: string[];
    filters?: Record<string, any>;
    correlationId?: string;
    metadata?: Record<string, any>;
  }): Promise<DataAccessLog> {
    try {
      const dataAccessLog = this.dataAccessLogRepository.create(
        DataAccessLog.createDataAccessLog(data)
      );

      const savedLog = await this.dataAccessLogRepository.save(dataAccessLog);
      
      // Log to console for data access tracking
      const sensitivity = data.sensitivity || DataSensitivity.INTERNAL;
      const message = `[DATA ACCESS] ${data.accessType} on ${data.dataType}: ${data.description}`;
      
      if (sensitivity === DataSensitivity.RESTRICTED || sensitivity === DataSensitivity.PII) {
        this.logger.warn(`🔒 SENSITIVE DATA ACCESS: ${message}`);
      } else {
        this.logger.log(`📊 DATA ACCESS: ${message}`);
      }

      // Also create an audit log for data access
      await this.logAuditEvent({
        userId: data.userId,
        sessionId: data.sessionId,
        action: AuditAction.READ,
        level: this.mapDataSensitivityToAuditLevel(sensitivity),
        category: AuditCategory.DATA_ACCESS,
        entityType: data.dataType,
        entityId: data.dataId,
        description: data.description,
        details: { 
          dataAccessLogId: savedLog.id, 
          accessType: data.accessType,
          sensitivity: sensitivity,
          purpose: data.purpose,
          recordCount: data.recordCount,
        },
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        correlationId: data.correlationId,
        duration: data.duration,
        isSuccessful: data.isAuthorized,
        metadata: data.metadata,
      });

      return savedLog;
    } catch (error) {
      this.logger.error('Failed to save data access log:', error);
      throw error;
    }
  }

  async logComplianceEvent(data: {
    userId?: string;
    dataSubjectId?: string;
    type: ComplianceType;
    eventType: ComplianceEventType;
    description: string;
    legalBasis?: string;
    processingPurpose?: string;
    dataCategories?: string[];
    dataFields?: string[];
    dataSource?: string;
    dataDestination?: string;
    consentDetails?: Record<string, any>;
    consentTimestamp?: Date;
    consentMethod?: string;
    isAutomated?: boolean;
    isProfiling?: boolean;
    isCrossBorder?: boolean;
    recipientCountry?: string;
    adequacyDecision?: string;
    safeguards?: string[];
    retentionPeriod?: number;
    scheduledDeletion?: Date;
    requestType?: string;
    requestId?: string;
    responseDeadline?: Date;
    responseDate?: Date;
    responseMethod?: string;
    breachDetails?: Record<string, any>;
    isNotifiable?: boolean;
    notificationDate?: Date;
    notificationReference?: string;
    mitigationMeasures?: string[];
    responsiblePerson?: string;
    notes?: string;
    evidence?: Record<string, any>;
    correlationId?: string;
    metadata?: Record<string, any>;
  }): Promise<ComplianceEvent> {
    try {
      const complianceEvent = this.complianceEventRepository.create(
        ComplianceEvent.createComplianceEvent(data)
      );

      const savedEvent = await this.complianceEventRepository.save(complianceEvent);
      
      // Log to console for compliance tracking
      const message = `[COMPLIANCE] ${data.type} - ${data.eventType}: ${data.description}`;
      
      if (data.eventType === ComplianceEventType.DATA_BREACH) {
        this.logger.error(`🚨 COMPLIANCE BREACH: ${message}`);
      } else if (data.eventType === ComplianceEventType.CONSENT_WITHDRAWN) {
        this.logger.warn(`⚠️ CONSENT WITHDRAWN: ${message}`);
      } else {
        this.logger.log(`📋 COMPLIANCE EVENT: ${message}`);
      }

      // Also create an audit log for compliance events
      await this.logAuditEvent({
        userId: data.userId,
        action: this.mapComplianceEventToAuditAction(data.eventType),
        level: this.mapComplianceEventToAuditLevel(data.eventType),
        category: AuditCategory.COMPLIANCE,
        entityType: 'ComplianceEvent',
        entityId: savedEvent.id,
        description: data.description,
        details: { 
          complianceEventId: savedEvent.id,
          type: data.type,
          eventType: data.eventType,
          dataSubjectId: data.dataSubjectId,
          isNotifiable: data.isNotifiable,
        },
        correlationId: data.correlationId,
        metadata: data.metadata,
      });

      return savedEvent;
    } catch (error) {
      this.logger.error('Failed to save compliance event:', error);
      throw error;
    }
  }

  // Helper methods for mapping between different enum types
  private mapSecuritySeverityToAuditLevel(severity: SecuritySeverity): AuditLevel {
    switch (severity) {
      case SecuritySeverity.CRITICAL:
        return AuditLevel.CRITICAL;
      case SecuritySeverity.HIGH:
        return AuditLevel.ERROR;
      case SecuritySeverity.MEDIUM:
        return AuditLevel.WARNING;
      case SecuritySeverity.LOW:
      default:
        return AuditLevel.INFO;
    }
  }

  private mapDataSensitivityToAuditLevel(sensitivity: DataSensitivity): AuditLevel {
    switch (sensitivity) {
      case DataSensitivity.RESTRICTED:
      case DataSensitivity.PII:
      case DataSensitivity.FINANCIAL:
      case DataSensitivity.HEALTH:
      case DataSensitivity.LEGAL:
        return AuditLevel.WARNING;
      case DataSensitivity.CONFIDENTIAL:
        return AuditLevel.INFO;
      case DataSensitivity.INTERNAL:
      case DataSensitivity.PUBLIC:
      default:
        return AuditLevel.INFO;
    }
  }

  private mapComplianceEventToAuditAction(eventType: ComplianceEventType): AuditAction {
    switch (eventType) {
      case ComplianceEventType.DATA_COLLECTION:
      case ComplianceEventType.CONSENT_GIVEN:
        return AuditAction.CREATE;
      case ComplianceEventType.DATA_PROCESSING:
      case ComplianceEventType.DATA_SHARING:
        return AuditAction.UPDATE;
      case ComplianceEventType.DATA_DELETION:
      case ComplianceEventType.DATA_ANONYMIZATION:
      case ComplianceEventType.CONSENT_WITHDRAWN:
        return AuditAction.DELETE;
      case ComplianceEventType.DATA_EXPORT:
        return AuditAction.EXPORT;
      case ComplianceEventType.DATA_SUBJECT_REQUEST:
        return AuditAction.READ;
      default:
        return AuditAction.UPDATE;
    }
  }

  private mapComplianceEventToAuditLevel(eventType: ComplianceEventType): AuditLevel {
    switch (eventType) {
      case ComplianceEventType.DATA_BREACH:
        return AuditLevel.CRITICAL;
      case ComplianceEventType.CONSENT_WITHDRAWN:
      case ComplianceEventType.DATA_SUBJECT_REQUEST:
        return AuditLevel.WARNING;
      default:
        return AuditLevel.INFO;
    }
  }

  // Convenience methods for common audit scenarios
  async logUserAction(
    userId: string,
    action: AuditAction,
    entityType: string,
    entityId: string,
    description: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.logAuditEvent({
      userId,
      action,
      category: this.getCategoryForAction(action),
      entityType,
      entityId,
      description,
      oldValues,
      newValues,
      ipAddress,
      userAgent,
    });
  }

  async logSystemEvent(
    action: AuditAction,
    description: string,
    level: AuditLevel = AuditLevel.INFO,
    details?: Record<string, any>
  ): Promise<AuditLog> {
    return this.logAuditEvent({
      action,
      level,
      category: AuditCategory.SYSTEM_CONFIGURATION,
      description,
      details,
      source: 'system',
    });
  }

  async logError(
    error: Error,
    context: string,
    userId?: string,
    entityType?: string,
    entityId?: string
  ): Promise<AuditLog> {
    return this.logAuditEvent({
      userId,
      action: AuditAction.READ, // or appropriate action
      level: AuditLevel.ERROR,
      category: AuditCategory.ERROR,
      entityType,
      entityId,
      description: `Error in ${context}: ${error.message}`,
      isSuccessful: false,
      errorMessage: error.message,
      errorCode: error.name,
      details: {
        stack: error.stack,
        context,
      },
    });
  }

  private getCategoryForAction(action: AuditAction): AuditCategory {
    switch (action) {
      case AuditAction.LOGIN:
      case AuditAction.LOGOUT:
        return AuditCategory.AUTHENTICATION;
      case AuditAction.CREATE:
      case AuditAction.UPDATE:
      case AuditAction.DELETE:
        return AuditCategory.DATA_MODIFICATION;
      case AuditAction.READ:
      case AuditAction.EXPORT:
        return AuditCategory.DATA_ACCESS;
      case AuditAction.CONFIGURE:
        return AuditCategory.SYSTEM_CONFIGURATION;
      default:
        return AuditCategory.BUSINESS_LOGIC;
    }
  }
}
