import { Injectable, Logger, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

/**
 * Serviço de cache Redis otimizado conforme requisitos da empresa
 * - Cache de preços com fallback
 * - Cache de sessões JWT
 * - Rate limiting distribuído
 * - Cache de consultas frequentes
 */
@Injectable()
export class RedisCacheService {
  private readonly logger = new Logger(RedisCacheService.name);

  constructor(
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
  ) {}

  /**
   * Cache de preços conforme S-017 do backlog
   * - Cache inteligente de preços
   * - Fallback para último preço conhecido
   * - TTL configurável por tipo de produto
   */
  async cachePrice(productId: string, price: number, source: 'PDV' | 'ERP' = 'PDV'): Promise<void> {
    const key = `price:${productId}`;
    const data = {
      price,
      source,
      timestamp: new Date().toISOString(),
      stale: false,
    };

    // Cache por 1 hora para PDV, 4 horas para ERP
    const ttl = source === 'PDV' ? 3600 : 14400;
    
    await this.cacheManager.set(key, data, ttl);
    this.logger.debug(`Price cached for product ${productId}: ${price} from ${source}`);
  }

  async getPrice(productId: string): Promise<{ price: number; source: string; stale: boolean } | null> {
    const key = `price:${productId}`;
    const cached = await this.cacheManager.get<any>(key);
    
    if (!cached) {
      return null;
    }

    // Verificar se o preço está desatualizado (>24h)
    const age = Date.now() - new Date(cached.timestamp).getTime();
    const isStale = age > 24 * 60 * 60 * 1000; // 24 horas

    return {
      price: cached.price,
      source: cached.source,
      stale: isStale,
    };
  }

  /**
   * Cache de sessões JWT para invalidação rápida
   */
  async cacheSession(userId: string, tokenId: string, expiresAt: Date): Promise<void> {
    const key = `session:${userId}:${tokenId}`;
    const ttl = Math.floor((expiresAt.getTime() - Date.now()) / 1000);
    
    if (ttl > 0) {
      await this.cacheManager.set(key, { userId, tokenId, expiresAt }, ttl);
      this.logger.debug(`Session cached for user ${userId}`);
    }
  }

  async isSessionValid(userId: string, tokenId: string): Promise<boolean> {
    const key = `session:${userId}:${tokenId}`;
    const session = await this.cacheManager.get(key);
    return !!session;
  }

  async invalidateSession(userId: string, tokenId: string): Promise<void> {
    const key = `session:${userId}:${tokenId}`;
    await this.cacheManager.del(key);
    this.logger.debug(`Session invalidated for user ${userId}`);
  }

  async invalidateAllUserSessions(userId: string): Promise<void> {
    // Note: This requires Redis SCAN command for pattern matching
    // For now, we'll use a simpler approach with user session list
    const userSessionsKey = `user_sessions:${userId}`;
    const sessions = await this.cacheManager.get<string[]>(userSessionsKey) || [];
    
    for (const tokenId of sessions) {
      await this.invalidateSession(userId, tokenId);
    }
    
    await this.cacheManager.del(userSessionsKey);
    this.logger.debug(`All sessions invalidated for user ${userId}`);
  }

  /**
   * Rate limiting distribuído conforme S-009 do backlog
   */
  async checkRateLimit(key: string, limit: number, windowMs: number): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const now = Date.now();
    const window = Math.floor(now / windowMs);
    const rateLimitKey = `rate_limit:${key}:${window}`;
    
    const current = await this.cacheManager.get<number>(rateLimitKey) || 0;
    const remaining = Math.max(0, limit - current - 1);
    const allowed = current < limit;
    
    if (allowed) {
      const ttl = Math.ceil(windowMs / 1000);
      await this.cacheManager.set(rateLimitKey, current + 1, ttl);
    }
    
    const resetTime = (window + 1) * windowMs;
    
    return {
      allowed,
      remaining,
      resetTime,
    };
  }

  /**
   * Cache de consultas frequentes para performance
   */
  async cacheQuery(queryKey: string, data: any, ttl: number = 300): Promise<void> {
    const key = `query:${queryKey}`;
    await this.cacheManager.set(key, data, ttl);
    this.logger.debug(`Query cached: ${queryKey}`);
  }

  async getCachedQuery<T>(queryKey: string): Promise<T | null> {
    const key = `query:${queryKey}`;
    return await this.cacheManager.get<T>(key);
  }

  /**
   * Cache de configurações de regras de negócio
   */
  async cacheBusinessRules(industryId: string, rules: any): Promise<void> {
    const key = `business_rules:${industryId}`;
    await this.cacheManager.set(key, rules, 1800); // 30 minutos
    this.logger.debug(`Business rules cached for industry ${industryId}`);
  }

  async getBusinessRules(industryId: string): Promise<any | null> {
    const key = `business_rules:${industryId}`;
    return await this.cacheManager.get(key);
  }

  /**
   * Cache de produtos por indústria para filtragem automática
   */
  async cacheIndustryProducts(industryId: string, productIds: string[]): Promise<void> {
    const key = `industry_products:${industryId}`;
    await this.cacheManager.set(key, productIds, 3600); // 1 hora
    this.logger.debug(`Products cached for industry ${industryId}: ${productIds.length} products`);
  }

  async getIndustryProducts(industryId: string): Promise<string[] | null> {
    const key = `industry_products:${industryId}`;
    return await this.cacheManager.get<string[]>(key);
  }

  /**
   * Utilitários de cache
   */
  async invalidatePattern(pattern: string): Promise<void> {
    // Note: This would require Redis SCAN in a real implementation
    this.logger.warn(`Pattern invalidation not implemented: ${pattern}`);
  }

  async getCacheStats(): Promise<{ hits: number; misses: number; keys: number }> {
    // Note: This would require Redis INFO command in a real implementation
    return {
      hits: 0,
      misses: 0,
      keys: 0,
    };
  }

  async clearCache(): Promise<void> {
    await this.cacheManager.reset();
    this.logger.warn('Cache cleared');
  }

  /**
   * Health check para Redis
   */
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; latency?: number }> {
    try {
      const start = Date.now();
      await this.cacheManager.set('health_check', 'ok', 10);
      const result = await this.cacheManager.get('health_check');
      const latency = Date.now() - start;
      
      if (result === 'ok') {
        await this.cacheManager.del('health_check');
        return { status: 'healthy', latency };
      } else {
        return { status: 'unhealthy' };
      }
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      return { status: 'unhealthy' };
    }
  }
}
