/**
 * Domain model representing the result of a campaign performance estimation
 */
export interface EstimationResult {
  readonly reach: number;
  readonly engagement: number;
  readonly conversions: number;
  readonly revenue: number;
  readonly roi: number;
  readonly confidence: number;
  readonly factors: EstimationFactors;
  readonly recommendations: string[];
  readonly warnings: string[];
}

export interface EstimationFactors {
  readonly productPopularity: number;
  readonly incentiveAttractiveness: number;
  readonly seasonality: number;
  readonly marketTrends: number;
  readonly historicalPerformance: number;
}

export interface EstimationInput {
  readonly industryId: string;
  readonly productIds: string[];
  readonly incentivePercentage: number;
  readonly validityDays: number;
  readonly targetAudience?: TargetAudience;
  readonly budget?: number;
  readonly campaignType?: CampaignType;
}

export interface TargetAudience {
  readonly ageRange?: [number, number];
  readonly gender?: 'male' | 'female' | 'all';
  readonly location?: string[];
  readonly interests?: string[];
}

export type CampaignType = 'awareness' | 'conversion' | 'retention' | 'acquisition';

/**
 * Value object representing anomaly detection results
 */
export class AnomalyDetectionResult {
  constructor(
    public readonly isAnomalous: boolean,
    public readonly warnings: string[]
  ) {}

  static normal(): AnomalyDetectionResult {
    return new AnomalyDetectionResult(false, []);
  }

  static anomalous(warnings: string[]): AnomalyDetectionResult {
    return new AnomalyDetectionResult(true, warnings);
  }
}

/**
 * Value object for rate limiting information
 */
export class RateLimitInfo {
  constructor(
    public readonly requestCount: number,
    public readonly resetTime: number,
    public readonly isExceeded: boolean
  ) {}

  static create(count: number, resetTime: number, limit: number): RateLimitInfo {
    return new RateLimitInfo(count, resetTime, count >= limit);
  }
}
