import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { RolePermission } from './role-permission.entity';

export enum PermissionResource {
  CAMPAIGNS = 'campaigns',
  USERS = 'users',
  ROLES = 'roles',
  PERMISSIONS = 'permissions',
  INDUSTRY_RULES = 'industry_rules',
  PRODUCTS = 'products',
  REPORTS = 'reports',
  INVITATIONS = 'invitations',
  SETTINGS = 'settings',
  AUDIT_LOGS = 'audit_logs',
}

export enum PermissionAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  EXECUTE = 'execute',
  APPROVE = 'approve',
  EXPORT = 'export',
  IMPORT = 'import',
}

@Entity('permissions')
@Index(['resource', 'action'])
@Index(['resource'])
@Index(['action'])
export class Permission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  @Index()
  name: string;

  @Column({ type: 'varchar', length: 255 })
  displayName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: PermissionResource })
  resource: PermissionResource;

  @Column({ type: 'enum', enum: PermissionAction })
  action: PermissionAction;

  @Column({ type: 'varchar', length: 100, nullable: true })
  scope: string; // e.g., 'own', 'industry', 'all'

  @Column({ type: 'json', nullable: true })
  conditions: Record<string, any>; // Additional conditions for permission

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isSystem: boolean; // System permissions cannot be deleted

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => RolePermission, rolePermission => rolePermission.permission)
  rolePermissions: RolePermission[];

  // Helper methods
  getFullName(): string {
    return `${this.resource}:${this.action}${this.scope ? `:${this.scope}` : ''}`;
  }

  canBeDeleted(): boolean {
    return !this.isSystem;
  }

  matches(resource: PermissionResource, action: PermissionAction, scope?: string): boolean {
    if (this.resource !== resource || this.action !== action) {
      return false;
    }

    if (scope && this.scope && this.scope !== scope) {
      return false;
    }

    return true;
  }
}
