import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  ParseIntPipe,
  ParseEnumPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, IsBoolean, IsObject, Min, Max } from 'class-validator';

import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

import { AttributionService } from './attribution.service';
import { TouchpointService } from './touchpoint.service';
import { ConversionService } from './conversion.service';
import { AttributionEngineService } from './attribution-engine.service';

import { AttributionModel } from '../database/entities/attribution-model.entity';
import { TouchpointType, TouchpointChannel, DeviceType } from '../database/entities/touchpoint.entity';
import { ConversionType, ConversionStatus } from '../database/entities/conversion.entity';

// DTOs
class CreateTouchpointDto {
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  campaignId?: string;

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsEnum(TouchpointType)
  type: TouchpointType;

  @IsEnum(TouchpointChannel)
  channel: TouchpointChannel;

  @IsOptional()
  @IsString()
  url?: string;

  @IsOptional()
  @IsString()
  referrer?: string;

  @IsOptional()
  @IsString()
  source?: string;

  @IsOptional()
  @IsString()
  medium?: string;

  @IsOptional()
  @IsString()
  campaign?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  term?: string;

  @IsOptional()
  @IsEnum(DeviceType)
  deviceType?: DeviceType;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsString()
  ipAddress?: string;

  @IsOptional()
  @IsObject()
  customAttributes?: Record<string, any>;

  @IsOptional()
  @IsNumber()
  @Min(0)
  value?: number;

  @IsOptional()
  @IsString()
  currency?: string;
}

class CreateConversionDto {
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  campaignId?: string;

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsOptional()
  @IsString()
  orderId?: string;

  @IsEnum(ConversionType)
  type: ConversionType;

  @IsNumber()
  @Min(0)
  value: number;

  @IsOptional()
  @IsString()
  currency?: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  quantity?: number;

  @IsOptional()
  @IsString()
  productId?: string;

  @IsOptional()
  @IsString()
  productName?: string;

  @IsOptional()
  @IsString()
  productCategory?: string;

  @IsOptional()
  @IsString()
  productBrand?: string;

  @IsOptional()
  @IsString()
  productSku?: string;

  @IsOptional()
  @IsString()
  conversionUrl?: string;

  @IsOptional()
  @IsString()
  referrer?: string;

  @IsOptional()
  @IsString()
  source?: string;

  @IsOptional()
  @IsString()
  medium?: string;

  @IsOptional()
  @IsString()
  campaign?: string;

  @IsOptional()
  @IsString()
  ipAddress?: string;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsObject()
  customAttributes?: Record<string, any>;

  @IsOptional()
  @IsObject()
  productDetails?: Record<string, any>;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsBoolean()
  processAttribution?: boolean;
}

class CreateAttributionModelDto {
  @IsString()
  name: string;

  @IsString()
  displayName: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(AttributionModel)
  type: AttributionModel;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  lookbackWindowDays?: number;

  @IsOptional()
  @IsObject()
  configuration?: Record<string, any>;

  @IsOptional()
  @IsObject()
  weights?: Record<string, number>;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  decayRate?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  priority?: number;
}

@ApiTags('Attribution')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('api/v1/attribution')
export class AttributionController {
  constructor(
    private readonly attributionService: AttributionService,
    private readonly touchpointService: TouchpointService,
    private readonly conversionService: ConversionService,
    private readonly attributionEngineService: AttributionEngineService,
  ) {}

  // Touchpoint endpoints
  @Post('touchpoints')
  @ApiOperation({ summary: 'Create a touchpoint' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Touchpoint created successfully' })
  async createTouchpoint(@Body(ValidationPipe) createTouchpointDto: CreateTouchpointDto) {
    return this.touchpointService.createTouchpoint(createTouchpointDto);
  }

  @Get('touchpoints')
  @ApiOperation({ summary: 'Get touchpoints' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'campaignId', required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'limit', required: false })
  async getTouchpoints(
    @Query('userId') userId?: string,
    @Query('campaignId') campaignId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit?: number,
  ) {
    return this.touchpointService.getTouchpoints(
      userId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      campaignId,
      limit,
    );
  }

  @Get('touchpoints/analytics')
  @Roles('admin', 'manager')
  @ApiOperation({ summary: 'Get touchpoint analytics' })
  async getTouchpointAnalytics(
    @Query('campaignId') campaignId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.touchpointService.getTouchpointAnalytics(
      campaignId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('users/:userId/journey')
  @ApiOperation({ summary: 'Get user journey' })
  async getUserJourney(
    @Param('userId') userId: string,
    @Query('sessionId') sessionId?: string,
    @Query('windowDays', new DefaultValuePipe(30), ParseIntPipe) windowDays?: number,
  ) {
    return this.touchpointService.getUserJourney(userId, sessionId, windowDays);
  }

  // Conversion endpoints
  @Post('conversions')
  @ApiOperation({ summary: 'Create a conversion' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Conversion created successfully' })
  async createConversion(@Body(ValidationPipe) createConversionDto: CreateConversionDto) {
    return this.conversionService.createConversion(createConversionDto);
  }

  @Get('conversions')
  @ApiOperation({ summary: 'Get conversions' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'campaignId', required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'type', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'offset', required: false })
  async getConversions(
    @Query('userId') userId?: string,
    @Query('campaignId') campaignId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('type') type?: ConversionType,
    @Query('status') status?: ConversionStatus,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit?: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset?: number,
  ) {
    return this.conversionService.getConversions(
      userId,
      campaignId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      type,
      status,
      limit,
      offset,
    );
  }

  @Get('conversions/analytics')
  @Roles('admin', 'manager')
  @ApiOperation({ summary: 'Get conversion analytics' })
  async getConversionAnalytics(
    @Query('campaignId') campaignId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.conversionService.getConversionAnalytics(
      campaignId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Put('conversions/:id/status')
  @Roles('admin', 'manager')
  @ApiOperation({ summary: 'Update conversion status' })
  async updateConversionStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('status', new ParseEnumPipe(ConversionStatus)) status: ConversionStatus,
    @Body('notes') notes?: string,
  ) {
    return this.conversionService.updateConversionStatus(id, status, notes);
  }

  // Attribution endpoints
  @Get('attributions')
  @ApiOperation({ summary: 'Get attributions' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'campaignId', required: false })
  @ApiQuery({ name: 'conversionId', required: false })
  @ApiQuery({ name: 'model', required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'offset', required: false })
  async getAttributions(
    @Query('userId') userId?: string,
    @Query('campaignId') campaignId?: string,
    @Query('conversionId') conversionId?: string,
    @Query('model') model?: AttributionModel,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit?: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset?: number,
  ) {
    return this.attributionService.getAttributions(
      userId,
      campaignId,
      conversionId,
      model,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      limit,
      offset,
    );
  }

  @Get('reports')
  @Roles('admin', 'manager')
  @ApiOperation({ summary: 'Get attribution report' })
  async getAttributionReport(
    @Query('campaignId') campaignId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('model') model?: AttributionModel,
  ) {
    return this.attributionService.getAttributionReport(
      campaignId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      model,
    );
  }

  @Post('conversions/:id/process')
  @Roles('admin', 'manager')
  @ApiOperation({ summary: 'Process attribution for a conversion' })
  async processConversionAttribution(
    @Param('id', ParseUUIDPipe) conversionId: string,
    @Query('model') modelName?: string,
  ) {
    return this.attributionEngineService.processConversion(conversionId, modelName);
  }

  @Post('reprocess')
  @Roles('admin')
  @ApiOperation({ summary: 'Reprocess attributions for a date range' })
  async reprocessAttributions(
    @Body('startDate') startDate: string,
    @Body('endDate') endDate: string,
    @Body('modelName') modelName?: string,
  ) {
    return this.attributionService.reprocessAttributions(
      new Date(startDate),
      new Date(endDate),
      modelName,
    );
  }

  // Attribution model management
  @Get('models')
  @ApiOperation({ summary: 'Get attribution models' })
  async getAttributionModels() {
    return this.attributionService.getAttributionModels();
  }

  @Post('models')
  @Roles('admin')
  @ApiOperation({ summary: 'Create attribution model' })
  async createAttributionModel(@Body(ValidationPipe) createModelDto: CreateAttributionModelDto) {
    return this.attributionService.createAttributionModel(createModelDto);
  }

  @Put('models/:id')
  @Roles('admin')
  @ApiOperation({ summary: 'Update attribution model' })
  async updateAttributionModel(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateData: Partial<CreateAttributionModelDto>,
  ) {
    return this.attributionService.updateAttributionModel(id, updateData);
  }
}
