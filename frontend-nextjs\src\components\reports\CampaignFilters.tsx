'use client';

import { useState, useEffect } from 'react';

import { Filter, X, Search, CheckCircle } from 'lucide-react';
import { campaignAPI } from '@/lib/api';

export interface CampaignFilter {
  campaignIds: string[];
  status: string[];
  search: string;
}

interface Campaign {
  id: string;
  name: string;
  status: string;
  startDate: string;
  endDate: string;
}

interface CampaignFiltersProps {
  filters: CampaignFilter;
  onFiltersChange: (filters: CampaignFilter) => void;
  isOpen: boolean;
  onClose: () => void;
}

export default function CampaignFilters({ 
  filters, 
  onFiltersChange, 
  isOpen, 
  onClose 
}: CampaignFiltersProps) {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState(filters.search);
  const [selectedCampaigns, setSelectedCampaigns] = useState<string[]>(filters.campaignIds);
  const [selectedStatus, setSelectedStatus] = useState<string[]>(filters.status);

  const statusOptions = [
    { value: 'active', label: 'Ativas' },
    { value: 'ended', label: 'Concluídas' },
    { value: 'paused', label: 'Pausadas' },
    { value: 'draft', label: 'Rascunho' }
  ];

  // Sync local state with props when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm(filters.search);
      setSelectedCampaigns(filters.campaignIds);
      setSelectedStatus(filters.status);
    }
  }, [isOpen, filters]);

  useEffect(() => {
    console.log('🔍 CampaignFilters useEffect - isOpen:', isOpen);
    if (isOpen) {
      loadCampaigns();
    }
  }, [isOpen]);

  const loadCampaigns = async () => {
    setIsLoading(true);
    try {
      const response = await campaignAPI.getCampaigns();
      setCampaigns(response.data.campaigns || []);
    } catch (error) {
      console.warn('API não disponível, usando dados mock:', error);
      // Use mock data when API is not available
      const mockCampaigns = [
        {
          id: '1',
          name: 'Campanha Black Friday',
          status: 'active',
          startDate: '2024-11-01',
          endDate: '2024-11-30'
        },
        {
          id: '2',
          name: 'Promoção Eletrônicos',
          status: 'active',
          startDate: '2024-10-15',
          endDate: '2024-12-15'
        },
        {
          id: '3',
          name: 'Volta às Aulas',
          status: 'ended',
          startDate: '2024-01-15',
          endDate: '2024-02-28'
        },
        {
          id: '4',
          name: 'Campanha Natal',
          status: 'paused',
          startDate: '2024-12-01',
          endDate: '2024-12-25'
        },
        {
          id: '5',
          name: 'Promoção Verão',
          status: 'draft',
          startDate: '2024-12-15',
          endDate: '2025-03-15'
        }
      ];
      setCampaigns(mockCampaigns);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCampaignToggle = (campaignId: string) => {
    setSelectedCampaigns(prev => 
      prev.includes(campaignId)
        ? prev.filter(id => id !== campaignId)
        : [...prev, campaignId]
    );
  };

  const handleStatusToggle = (status: string) => {
    setSelectedStatus(prev => 
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const handleApplyFilters = () => {
    const newFilters = {
      campaignIds: selectedCampaigns,
      status: selectedStatus,
      search: searchTerm
    };
    console.log('🔧 Aplicando filtros:', newFilters);
    onFiltersChange(newFilters);
    onClose();
  };

  const handleClearFilters = () => {
    setSelectedCampaigns([]);
    setSelectedStatus([]);
    setSearchTerm('');
    onFiltersChange({
      campaignIds: [],
      status: [],
      search: ''
    });
  };

  const filteredCampaigns = campaigns.filter(campaign =>
    campaign.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[80vh] overflow-hidden mx-4">
        <div className="flex flex-row items-center justify-between p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filtros de Campanhas
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          >
            <X className="h-4 w-4 text-gray-500" />
          </button>
        </div>

        <div className="p-6 space-y-6 overflow-y-auto">
          {/* Search */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-900">Buscar Campanhas</label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                placeholder="Digite o nome da campanha..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-900">Status das Campanhas</label>
            <div className="grid grid-cols-2 gap-2">
              {statusOptions.map(option => (
                <label
                  key={option.value}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedStatus.includes(option.value)}
                    onChange={() => handleStatusToggle(option.value)}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">{option.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Campaign Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-900">Campanhas Específicas</label>
            <div className="max-h-48 overflow-y-auto border rounded-md p-2 space-y-1">
              {isLoading ? (
                <div className="text-center py-4 text-gray-500">
                  Carregando campanhas...
                </div>
              ) : filteredCampaigns.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  Nenhuma campanha encontrada
                </div>
              ) : (
                filteredCampaigns.map(campaign => (
                  <div
                    key={campaign.id}
                    className={`
                      flex items-center justify-between p-2 rounded cursor-pointer
                      ${selectedCampaigns.includes(campaign.id) 
                        ? 'bg-blue-50 border border-blue-200' 
                        : 'hover:bg-gray-50'
                      }
                    `}
                    onClick={() => handleCampaignToggle(campaign.id)}
                  >
                    <div className="flex-1">
                      <div className="font-medium text-sm">{campaign.name}</div>
                      <div className="text-xs text-gray-500">
                        Status: {campaign.status} | 
                        {new Date(campaign.startDate).toLocaleDateString()} - 
                        {new Date(campaign.endDate).toLocaleDateString()}
                      </div>
                    </div>
                    {selectedCampaigns.includes(campaign.id) && (
                      <CheckCircle className="h-4 w-4 text-blue-600" />
                    )}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between pt-4 border-t">
            <button
              onClick={handleClearFilters}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Limpar Filtros
            </button>
            <div className="space-x-2">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                onClick={handleApplyFilters}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Aplicar Filtros
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
