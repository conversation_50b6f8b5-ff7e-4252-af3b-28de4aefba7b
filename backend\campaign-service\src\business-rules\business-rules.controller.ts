import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { BusinessRulesService, CreateBusinessRuleDto, UpdateBusinessRuleDto } from './business-rules.service';
import { BusinessRule, BusinessRuleType, BusinessRuleStatus } from '../database/entities/business-rule.entity';
// Note: Auth guards and decorators would be imported from a shared auth module
// For now, we'll comment these out and implement basic auth later
// import { JwtAuthGuard } from '../auth/jwt-auth.guard';
// import { RolesGuard } from '../auth/roles.guard';
// import { Roles } from '../auth/roles.decorator';

enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  USER = 'user',
}

@ApiTags('Business Rules')
@ApiBearerAuth()
@Controller('api/v1/business-rules')
export class BusinessRulesController {
  constructor(private readonly businessRulesService: BusinessRulesService) {}

  @Post()
  // @Roles(UserRole.ADMIN, UserRole.MANAGER) // Commented out for now
  @ApiOperation({ summary: 'Create a new business rule' })
  @ApiResponse({ status: 201, description: 'Business rule created successfully', type: BusinessRule })
  @ApiResponse({ status: 400, description: 'Invalid rule configuration' })
  @ApiResponse({ status: 409, description: 'Rule already exists for this industry' })
  async createRule(
    @Body(ValidationPipe) createDto: CreateBusinessRuleDto,
    @Request() req: any,
  ): Promise<BusinessRule> {
    return this.businessRulesService.createRule(createDto, req.user?.sub || 'system');
  }

  @Get()
  @ApiOperation({ summary: 'Get all business rules with optional filtering' })
  @ApiQuery({ name: 'industryId', required: false, description: 'Filter by industry ID' })
  @ApiQuery({ name: 'type', required: false, enum: BusinessRuleType, description: 'Filter by rule type' })
  @ApiQuery({ name: 'enabled', required: false, type: Boolean, description: 'Filter by enabled status' })
  @ApiQuery({ name: 'status', required: false, enum: BusinessRuleStatus, description: 'Filter by rule status' })
  @ApiResponse({ status: 200, description: 'Business rules retrieved successfully', type: [BusinessRule] })
  async getRules(
    @Query('industryId') industryId?: string,
    @Query('type') type?: BusinessRuleType,
    @Query('enabled') enabled?: boolean,
    @Query('status') status?: BusinessRuleStatus,
  ): Promise<BusinessRule[]> {
    const filters = {
      ...(industryId && { industryId }),
      ...(type && { type }),
      ...(enabled !== undefined && { enabled }),
      ...(status && { status }),
    };

    return this.businessRulesService.getRules(Object.keys(filters).length > 0 ? filters : undefined);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific business rule by ID' })
  @ApiResponse({ status: 200, description: 'Business rule retrieved successfully', type: BusinessRule })
  @ApiResponse({ status: 404, description: 'Business rule not found' })
  async getRuleById(@Param('id', ParseUUIDPipe) id: string): Promise<BusinessRule> {
    return this.businessRulesService.getRuleById(id);
  }

  @Put(':id')
  // @Roles(UserRole.ADMIN, UserRole.MANAGER) // Commented out for now
  @ApiOperation({ summary: 'Update a business rule' })
  @ApiResponse({ status: 200, description: 'Business rule updated successfully', type: BusinessRule })
  @ApiResponse({ status: 400, description: 'Invalid rule configuration' })
  @ApiResponse({ status: 404, description: 'Business rule not found' })
  async updateRule(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateBusinessRuleDto,
    @Request() req: any,
  ): Promise<BusinessRule> {
    return this.businessRulesService.updateRule(id, updateDto, req.user?.sub || 'system');
  }

  @Delete(':id')
  // @Roles(UserRole.ADMIN) // Commented out for now
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a business rule' })
  @ApiResponse({ status: 204, description: 'Business rule deleted successfully' })
  @ApiResponse({ status: 404, description: 'Business rule not found' })
  async deleteRule(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.businessRulesService.deleteRule(id);
  }

  @Put(':id/toggle')
  // @Roles(UserRole.ADMIN, UserRole.MANAGER) // Commented out for now
  @ApiOperation({ summary: 'Enable or disable a business rule' })
  @ApiResponse({ status: 200, description: 'Business rule toggled successfully', type: BusinessRule })
  @ApiResponse({ status: 404, description: 'Business rule not found' })
  async toggleRule(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('enabled') enabled: boolean,
    @Request() req: any,
  ): Promise<BusinessRule> {
    return this.businessRulesService.toggleRule(id, enabled, req.user?.sub || 'system');
  }

  @Post('validate-campaign')
  @ApiOperation({ summary: 'Validate campaign data against business rules' })
  @ApiResponse({ 
    status: 200, 
    description: 'Campaign validation completed',
    schema: {
      type: 'object',
      properties: {
        isValid: { type: 'boolean' },
        errors: { type: 'array', items: { type: 'string' } },
        warnings: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  async validateCampaign(
    @Body() campaignData: any,
    @Query('industryId') industryId?: string,
  ) {
    return this.businessRulesService.validateCampaign(campaignData, industryId);
  }

  @Get('industry/:industryId/applicable')
  @ApiOperation({ summary: 'Get rules applicable to a specific industry' })
  @ApiResponse({ status: 200, description: 'Applicable rules retrieved successfully', type: [BusinessRule] })
  async getApplicableRules(
    @Param('industryId', ParseUUIDPipe) industryId: string,
  ): Promise<BusinessRule[]> {
    return this.businessRulesService.getApplicableRules(industryId);
  }

  @Post('industry/:industryId/initialize-defaults')
  // @Roles(UserRole.ADMIN, UserRole.MANAGER) // Commented out for now
  @ApiOperation({ summary: 'Initialize default business rules for a new industry' })
  @ApiResponse({ status: 201, description: 'Default rules initialized successfully', type: [BusinessRule] })
  async initializeDefaultRules(
    @Param('industryId', ParseUUIDPipe) industryId: string,
    @Request() req: any,
  ): Promise<BusinessRule[]> {
    return this.businessRulesService.initializeDefaultRules(industryId, req.user?.sub || 'system');
  }

  @Get('types/available')
  @ApiOperation({ summary: 'Get available business rule types' })
  @ApiResponse({ 
    status: 200, 
    description: 'Available rule types retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        types: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              value: { type: 'string' },
              label: { type: 'string' },
              description: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async getAvailableTypes() {
    return {
      types: [
        {
          value: BusinessRuleType.INCENTIVE_PERCENTAGE,
          label: 'Incentivo Mínimo (%)',
          description: 'Define o percentual mínimo de incentivo permitido',
        },
        {
          value: BusinessRuleType.INCENTIVE_VALUE,
          label: 'Incentivo Mínimo (R$)',
          description: 'Define o valor mínimo em reais de incentivo permitido',
        },
        {
          value: BusinessRuleType.PRODUCTS_PER_CYCLE,
          label: 'Limite de Produtos por Ciclo',
          description: 'Define o número máximo de produtos por ciclo de campanha',
        },
        {
          value: BusinessRuleType.CAMPAIGN_DURATION,
          label: 'Duração Máxima da Campanha',
          description: 'Define a duração máxima permitida para campanhas',
        },
        {
          value: BusinessRuleType.BUDGET_LIMIT,
          label: 'Limite de Orçamento',
          description: 'Define limites de orçamento para campanhas',
        },
      ],
    };
  }

  @Get('status/summary')
  @ApiOperation({ summary: 'Get summary of business rules status' })
  @ApiResponse({ 
    status: 200, 
    description: 'Rules status summary retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        active: { type: 'number' },
        inactive: { type: 'number' },
        pending: { type: 'number' },
        enabled: { type: 'number' },
        disabled: { type: 'number' },
        byType: {
          type: 'object',
          additionalProperties: { type: 'number' },
        },
      },
    },
  })
  async getRulesSummary(@Query('industryId') industryId?: string) {
    const rules = await this.businessRulesService.getRules(
      industryId ? { industryId } : undefined
    );

    const summary = {
      total: rules.length,
      active: rules.filter(r => r.status === BusinessRuleStatus.ACTIVE).length,
      inactive: rules.filter(r => r.status === BusinessRuleStatus.INACTIVE).length,
      pending: rules.filter(r => r.status === BusinessRuleStatus.PENDING).length,
      enabled: rules.filter(r => r.enabled).length,
      disabled: rules.filter(r => !r.enabled).length,
      byType: {} as Record<string, number>,
    };

    // Count by type
    for (const rule of rules) {
      summary.byType[rule.type] = (summary.byType[rule.type] || 0) + 1;
    }

    return summary;
  }

  @Post('bulk-update')
  // @Roles(UserRole.ADMIN) // Commented out for now
  @ApiOperation({ summary: 'Bulk update multiple business rules' })
  @ApiResponse({ status: 200, description: 'Rules updated successfully' })
  async bulkUpdateRules(
    @Body() updateRequests: Array<{ id: string; updates: UpdateBusinessRuleDto }>,
    @Request() req: any,
  ) {
    const results = [];

    for (const { id, updates } of updateRequests) {
      try {
        const updatedRule = await this.businessRulesService.updateRule(id, updates, req.user?.sub || 'system');
        results.push({ id, success: true, rule: updatedRule });
      } catch (error) {
        results.push({ id, success: false, error: error.message });
      }
    }

    return {
      total: updateRequests.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results,
    };
  }
}
