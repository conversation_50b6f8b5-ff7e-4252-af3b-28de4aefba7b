import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { RateLimitingService } from './rate-limiting.service';
import { RateLimitingMiddleware } from './rate-limiting.middleware';
import { SecurityController } from './security.controller';

@Module({
  controllers: [SecurityController],
  providers: [RateLimitingService],
  exports: [RateLimitingService],
})
export class SecurityModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(RateLimitingMiddleware)
      .forRoutes('*'); // Apply to all routes
  }
}
