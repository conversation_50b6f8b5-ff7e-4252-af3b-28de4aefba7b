import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IncentiveValidityConfig, ValidityType, ValidityStatus } from '../database/entities/incentive-validity.entity';

export interface ValidityCalculationResult {
  startDate: Date;
  endDate: Date;
  validityDays: number;
  timezone: string;
  businessHours?: {
    start: string;
    end: string;
  };
  excludedDates: string[];
  warnings: string[];
}

export class CreateValidityConfigDto {
  industryId?: string;
  validityType: ValidityType;
  categoryId?: string;
  campaignType?: string;
  defaultValidityDays: number;
  minValidityDays?: number;
  maxValidityDays?: number;
  timezone?: string;
  defaultStartTime?: string;
  defaultEndTime?: string;
  allowWeekends?: boolean;
  allowHolidays?: boolean;
  excludedDates?: string[];
  businessHours?: any;
  description?: string;
}

export class UpdateValidityConfigDto {
  industryId?: string;
  validityType?: ValidityType;
  categoryId?: string;
  campaignType?: string;
  defaultValidityDays?: number;
  minValidityDays?: number;
  maxValidityDays?: number;
  timezone?: string;
  defaultStartTime?: string;
  defaultEndTime?: string;
  allowWeekends?: boolean;
  allowHolidays?: boolean;
  excludedDates?: string[];
  businessHours?: any;
  description?: string;
  enabled?: boolean;
  status?: ValidityStatus;
}

@Injectable()
export class IncentiveValidityService {
  private readonly logger = new Logger(IncentiveValidityService.name);

  constructor(
    @InjectRepository(IncentiveValidityConfig)
    private readonly validityConfigRepository: Repository<IncentiveValidityConfig>,
  ) {}

  async createConfig(createDto: CreateValidityConfigDto, userId: string): Promise<IncentiveValidityConfig> {
    // Validate input
    if (createDto.defaultValidityDays < 1 || createDto.defaultValidityDays > 365) {
      throw new BadRequestException('Validade deve estar entre 1 e 365 dias');
    }

    if (createDto.minValidityDays && createDto.maxValidityDays) {
      if (createDto.minValidityDays > createDto.maxValidityDays) {
        throw new BadRequestException('Validade mínima não pode ser maior que a máxima');
      }
    }

    // Check for existing config with same criteria
    const existingConfig = await this.validityConfigRepository.findOne({
      where: {
        industryId: createDto.industryId,
        validityType: createDto.validityType,
        categoryId: createDto.categoryId,
        campaignType: createDto.campaignType,
        status: ValidityStatus.ACTIVE,
      },
    });

    if (existingConfig) {
      throw new BadRequestException('Já existe uma configuração ativa para estes critérios');
    }

    const config = this.validityConfigRepository.create({
      ...createDto,
      timezone: createDto.timezone || 'America/Sao_Paulo',
      createdBy: userId,
    });

    const savedConfig = await this.validityConfigRepository.save(config);
    
    this.logger.log(`Validity config created: ${savedConfig.id} by user ${userId}`);
    
    return savedConfig;
  }

  async updateConfig(id: string, updateDto: UpdateValidityConfigDto, userId: string): Promise<IncentiveValidityConfig> {
    const config = await this.validityConfigRepository.findOne({ where: { id } });
    
    if (!config) {
      throw new NotFoundException('Configuração de validade não encontrada');
    }

    // Validate updates
    if (updateDto.defaultValidityDays !== undefined) {
      if (updateDto.defaultValidityDays < 1 || updateDto.defaultValidityDays > 365) {
        throw new BadRequestException('Validade deve estar entre 1 e 365 dias');
      }
    }

    if (updateDto.minValidityDays && updateDto.maxValidityDays) {
      if (updateDto.minValidityDays > updateDto.maxValidityDays) {
        throw new BadRequestException('Validade mínima não pode ser maior que a máxima');
      }
    }

    Object.assign(config, updateDto, { updatedBy: userId });
    
    const updatedConfig = await this.validityConfigRepository.save(config);
    
    this.logger.log(`Validity config updated: ${id} by user ${userId}`);
    
    return updatedConfig;
  }

  async deleteConfig(id: string): Promise<void> {
    const config = await this.validityConfigRepository.findOne({ where: { id } });
    
    if (!config) {
      throw new NotFoundException('Configuração de validade não encontrada');
    }

    // Soft delete by changing status
    config.status = ValidityStatus.ARCHIVED;
    config.enabled = false;
    
    await this.validityConfigRepository.save(config);
    
    this.logger.log(`Validity config archived: ${id}`);
  }

  async findAll(industryId?: string): Promise<IncentiveValidityConfig[]> {
    const query = this.validityConfigRepository.createQueryBuilder('config')
      .where('config.status != :archivedStatus', { archivedStatus: ValidityStatus.ARCHIVED })
      .orderBy('config.validityType', 'ASC')
      .addOrderBy('config.createdAt', 'DESC');

    if (industryId) {
      query.andWhere('(config.industryId = :industryId OR config.industryId IS NULL)', { industryId });
    }

    return query.getMany();
  }

  async findById(id: string): Promise<IncentiveValidityConfig> {
    const config = await this.validityConfigRepository.findOne({ where: { id } });
    
    if (!config) {
      throw new NotFoundException('Configuração de validade não encontrada');
    }

    return config;
  }

  async getValidityForCampaign(
    industryId: string,
    categoryId?: string,
    campaignType?: string,
  ): Promise<IncentiveValidityConfig> {
    // Priority order: campaign type > product category > industry specific > default
    const searchCriteria = [
      { industryId, validityType: ValidityType.CAMPAIGN_TYPE, campaignType },
      { industryId, validityType: ValidityType.PRODUCT_CATEGORY, categoryId },
      { industryId, validityType: ValidityType.INDUSTRY_SPECIFIC },
      { validityType: ValidityType.DEFAULT },
    ];

    for (const criteria of searchCriteria) {
      const config = await this.validityConfigRepository.findOne({
        where: {
          ...criteria,
          status: ValidityStatus.ACTIVE,
          enabled: true,
        },
      });

      if (config) {
        return config;
      }
    }

    // Return default config if none found
    return this.getDefaultConfig();
  }

  async calculateValidity(
    startDate: Date,
    industryId: string,
    categoryId?: string,
    campaignType?: string,
  ): Promise<ValidityCalculationResult> {
    const config = await this.getValidityForCampaign(industryId, categoryId, campaignType);
    
    const result = config.calculateValidityPeriod(startDate);
    const warnings: string[] = [];

    // Add warnings
    if (result.excludedDays.length > 0) {
      warnings.push(`${result.excludedDays.length} dias excluídos do período de validade`);
    }

    if (!config.allowWeekends) {
      warnings.push('Fins de semana não são considerados válidos');
    }

    if (!config.allowHolidays) {
      warnings.push('Feriados não são considerados válidos');
    }

    // Get business hours for start date
    const dayOfWeek = startDate.getDay();
    const businessHours = config.getBusinessHoursForDay(dayOfWeek);

    return {
      startDate: result.startDate,
      endDate: result.endDate,
      validityDays: result.validDays,
      timezone: config.timezone,
      businessHours,
      excludedDates: result.excludedDays,
      warnings,
    };
  }

  async validateValidityPeriod(
    startDate: Date,
    endDate: Date,
    industryId: string,
    categoryId?: string,
    campaignType?: string,
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    const config = await this.getValidityForCampaign(industryId, categoryId, campaignType);
    
    const errors: string[] = [];
    const warnings: string[] = [];

    // Calculate days difference
    const diffTime = endDate.getTime() - startDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Check minimum validity
    if (config.minValidityDays && diffDays < config.minValidityDays) {
      errors.push(`Período de validade deve ser de pelo menos ${config.minValidityDays} dias`);
    }

    // Check maximum validity
    if (config.maxValidityDays && diffDays > config.maxValidityDays) {
      errors.push(`Período de validade não pode exceder ${config.maxValidityDays} dias`);
    }

    // Check if start date is valid
    if (!config.isValidDate(startDate)) {
      errors.push('Data de início não é válida conforme as regras configuradas');
    }

    // Check if end date is valid
    if (!config.isValidDate(endDate)) {
      warnings.push('Data de fim pode não ser válida conforme as regras configuradas');
    }

    // Check timezone consistency
    const now = new Date();
    if (startDate < now) {
      warnings.push('Data de início está no passado');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private async getDefaultConfig(): Promise<IncentiveValidityConfig> {
    let defaultConfig = await this.validityConfigRepository.findOne({
      where: {
        validityType: ValidityType.DEFAULT,
        status: ValidityStatus.ACTIVE,
        enabled: true,
      },
    });

    if (!defaultConfig) {
      // Create default config if it doesn't exist
      defaultConfig = this.validityConfigRepository.create({
        validityType: ValidityType.DEFAULT,
        defaultValidityDays: 30,
        timezone: 'America/Sao_Paulo',
        allowWeekends: true,
        allowHolidays: true,
        description: 'Configuração padrão do sistema',
        createdBy: 'system',
      });

      defaultConfig = await this.validityConfigRepository.save(defaultConfig);
      
      this.logger.log('Default validity config created');
    }

    return defaultConfig;
  }

  async getTimezoneInfo(): Promise<{ timezone: string; currentTime: string; offset: string }> {
    const timezone = 'America/Sao_Paulo';
    const now = new Date();
    
    const currentTime = now.toLocaleString('pt-BR', { 
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });

    const offset = now.toLocaleString('pt-BR', { 
      timeZone: timezone,
      timeZoneName: 'short',
    }).split(' ').pop();

    return {
      timezone,
      currentTime,
      offset,
    };
  }
}
