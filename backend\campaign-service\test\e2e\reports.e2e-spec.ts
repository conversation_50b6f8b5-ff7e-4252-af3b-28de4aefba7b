import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe, ExecutionContext } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { RolesGuard } from '../../src/common/guards/roles.guard';
import { JwtAuthGuard } from '../../src/common/guards/jwt-auth.guard';

describe('ReportsController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
    .overrideGuard(JwtAuthGuard)
    .useValue({
      canActivate: (context: ExecutionContext) => {
        const request = context.switchToHttp().getRequest();
        request.user = {
          sub: 'test-user-id',
          role: 'admin',
          email: '<EMAIL>',
          industryId: 'test-industry-id'
        };
        return true;
      },
    })
    .overrideGuard(RolesGuard)
    .useValue({
      canActivate: (context: ExecutionContext) => {
        const request = context.switchToHttp().getRequest();
        request.user = request.user || {
          sub: 'test-user-id',
          role: 'admin',
          email: '<EMAIL>',
          industryId: 'test-industry-id'
        };
        return true;
      },
    })
    .compile();

    app = moduleFixture.createNestApplication();

    app.setGlobalPrefix('api/v1');

    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/api/v1/reports/campaign-performance (POST)', () => {
    it('should return campaign performance report', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/campaign-performance')
        .send({
          dateRange: {
            startDate: '2025-01-01',
            endDate: '2025-12-31',
          },
          status: ['active', 'paused'],
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('summary');
          expect(res.body).toHaveProperty('campaigns');
          expect(res.body).toHaveProperty('trends');
          expect(res.body).toHaveProperty('topPerformers');
          
          expect(res.body.summary).toHaveProperty('totalCampaigns');
          expect(res.body.summary).toHaveProperty('activeCampaigns');
          expect(res.body.summary).toHaveProperty('totalRevenue');
          
          expect(Array.isArray(res.body.campaigns)).toBe(true);
          expect(res.body.trends).toHaveProperty('daily');
          expect(res.body.trends).toHaveProperty('weekly');
          expect(res.body.trends).toHaveProperty('monthly');
        });
    });

    it('should return empty report when no campaigns match filters', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/campaign-performance')
        .send({
          status: ['ended'],
          dateRange: {
            startDate: '2020-01-01',
            endDate: '2020-12-31',
          },
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.summary.totalCampaigns).toBe(0);
          expect(res.body.campaigns).toHaveLength(0);
        });
    });

    it('should handle request without filters', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/campaign-performance')
        .send({})
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('summary');
          expect(res.body).toHaveProperty('campaigns');
        });
    });
  });

  describe('/api/v1/reports/export (POST)', () => {
    it('should export campaign report as XLSX', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/export')
        .send({
          reportType: 'campaign',
          filters: {
            dateRange: {
              startDate: '2025-01-01',
              endDate: '2025-12-31',
            },
          },
          options: {
            format: 'xlsx',
            includeCharts: true,
            includeRawData: true,
          },
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('downloadUrl');
          expect(res.body).toHaveProperty('filename');
          expect(res.body).toHaveProperty('size');
          expect(res.body.filename).toContain('.xlsx');
        });
    });

    it('should export campaign report as PDF', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/export')
        .send({
          reportType: 'campaign',
          filters: {},
          options: {
            format: 'pdf',
            includeCharts: false,
            includeRawData: false,
          },
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('downloadUrl');
          expect(res.body.filename).toContain('.pdf');
        });
    });

    it('should return 400 for invalid report type', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/export')
        .send({
          reportType: 'invalid',
          filters: {},
          options: {
            format: 'xlsx',
          },
        })
        .expect(400);
    });
  });

  describe('/api/v1/reports/product-performance (POST)', () => {
    it('should return product performance report', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/product-performance')
        .send({
          dateRange: {
            startDate: '2025-01-01',
            endDate: '2025-12-31',
          },
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('summary');
          expect(res.body).toHaveProperty('products');
          expect(res.body).toHaveProperty('categories');
          expect(res.body).toHaveProperty('insights');
        });
    });
  });

  describe('/api/v1/reports/incentive-analysis (POST)', () => {
    it('should return incentive analysis report', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/incentive-analysis')
        .send({})
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('summary');
          expect(res.body).toHaveProperty('incentiveRanges');
          expect(res.body).toHaveProperty('productAnalysis');
          expect(res.body).toHaveProperty('trends');
          expect(res.body).toHaveProperty('recommendations');
        });
    });
  });

  describe('/api/v1/reports/user-activity (POST)', () => {
    it('should return user activity report', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/user-activity')
        .send({})
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('summary');
          expect(res.body).toHaveProperty('userTypes');
          expect(res.body).toHaveProperty('activity');
          expect(res.body).toHaveProperty('topUsers');
        });
    });
  });

  describe('Error handling', () => {
    it('should handle database connection errors gracefully', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/campaign-performance')
        .send({
          dateRange: {
            startDate: 'invalid-date',
            endDate: '2025-12-31',
          },
        })
        .expect((res) => {
          expect([201, 400, 500]).toContain(res.status);
        });
    });
  });

  describe('Performance', () => {
    it('should respond within acceptable time', async () => {
      const startTime = Date.now();
      
      await request(app.getHttpServer())
        .post('/api/v1/reports/campaign-performance')
        .send({})
        .expect(201);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(5000);
    });
  });
});

