'use client';

import { useState, useEffect, useCallback } from 'react';
import { reportsAPI } from '@/lib/api';
import toast from 'react-hot-toast';
import { CampaignFilter } from '@/components/reports/CampaignFilters';
import { PeriodFilter } from '@/components/reports/PeriodSelector';
import { ExportOptions } from '@/components/reports/ExportModal';

export interface ReportData {
  summary: {
    totalCampaigns: number;
    activeCampaigns: number;
    completedCampaigns: number;
    totalReach: number;
    totalEngagement: number;
    totalConversions: number;
    totalRevenue: number;
    averageROI: number;
    averageIncentive: number;
  };
  campaigns: Array<{
    id: string;
    name: string;
    status: string;
    startDate: string;
    endDate: string;
    reach: number;
    engagement: number;
    conversions: number;
    revenue: number;
    roi: number;
    incentivePercentage: number;
    productsCount: number;
  }>;
  trends: {
    daily: Array<{ date: string; reach: number; conversions: number; revenue: number }>;
    weekly: Array<{ week: string; reach: number; conversions: number; revenue: number }>;
    monthly: Array<{ month: string; reach: number; conversions: number; revenue: number }>;
  };
  topPerformers: {
    campaigns: Array<{ id: string; name: string; roi: number }>;
    products: Array<{ id: string; name: string; conversions: number }>;
    categories: Array<{ id: string; name: string; revenue: number }>;
  };
}

export function useReports() {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFilters, setLastFilters] = useState<any>(null);

  const buildFilters = useCallback((campaignFilters: CampaignFilter, periodFilter: PeriodFilter) => {
    const filters: any = {};

    // Campaign filters
    if (campaignFilters.campaignIds.length > 0) {
      filters.campaignIds = campaignFilters.campaignIds;
    }
    
    if (campaignFilters.status.length > 0) {
      filters.status = campaignFilters.status;
    }
    
    if (campaignFilters.search.trim()) {
      filters.search = campaignFilters.search.trim();
    }

    // Period filters
    if (periodFilter.type === 'preset' && periodFilter.preset !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      switch (periodFilter.preset) {
        case '1d':
          filters.dateRange = {
            startDate: new Date(today.getTime() - 24 * 60 * 60 * 1000).toISOString(),
            endDate: now.toISOString()
          };
          break;
        case '7d':
          filters.dateRange = {
            startDate: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: now.toISOString()
          };
          break;
        case '30d':
          filters.dateRange = {
            startDate: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: now.toISOString()
          };
          break;
        case '60d':
          filters.dateRange = {
            startDate: new Date(today.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: now.toISOString()
          };
          break;
        case '90d':
          filters.dateRange = {
            startDate: new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: now.toISOString()
          };
          break;
      }
    } else if (periodFilter.type === 'custom' && periodFilter.startDate && periodFilter.endDate) {
      console.log('📅 Aplicando período customizado:', periodFilter.startDate, 'até', periodFilter.endDate);
      filters.dateRange = {
        startDate: new Date(periodFilter.startDate).toISOString(),
        endDate: new Date(periodFilter.endDate + 'T23:59:59').toISOString()
      };
    }

    return filters;
  }, []);

  const loadReportData = useCallback(async (campaignFilters: CampaignFilter, periodFilter: PeriodFilter) => {
    setIsLoading(true);
    setError(null);

    try {
      const filters = buildFilters(campaignFilters, periodFilter);
      setLastFilters(filters);

      const response = await reportsAPI.getCampaignPerformance(filters);
      setReportData(response.data);
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Erro ao carregar relatório';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [buildFilters]);

  const exportReport = useCallback(async (
    campaignFilters: CampaignFilter,
    periodFilter: PeriodFilter,
    exportOptions: ExportOptions
  ) => {
    try {
      const filters = buildFilters(campaignFilters, periodFilter);

      const response = await reportsAPI.exportReport(
        exportOptions.reportType,
        filters,
        {
          format: exportOptions.format === 'excel' ? 'xlsx' : 'pdf',
          includeCharts: exportOptions.includeCharts,
          includeRawData: exportOptions.includeRawData
        }
      );

      if (response.data.downloadUrl) {
        toast.success(`Relatório gerado: ${response.data.filename}`);

        const downloadUrl = `${process.env.NEXT_PUBLIC_API_URL}${response.data.downloadUrl}`;
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = response.data.filename;
        link.target = '_blank';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success('Download iniciado!');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Erro ao exportar relatório. Tente novamente.';
      toast.error(errorMessage);
      throw err;
    }
  }, [buildFilters]);

  const refreshData = useCallback(() => {
    if (lastFilters) {
      // Reconstruct filters to reload with same parameters
      const defaultCampaignFilters: CampaignFilter = {
        campaignIds: lastFilters.campaignIds || [],
        status: lastFilters.status || [],
        search: lastFilters.search || ''
      };
      
      const defaultPeriodFilter: PeriodFilter = lastFilters.dateRange 
        ? {
            type: 'custom',
            startDate: lastFilters.dateRange.startDate.split('T')[0],
            endDate: lastFilters.dateRange.endDate.split('T')[0]
          }
        : { type: 'preset', preset: 'all' };
      
      loadReportData(defaultCampaignFilters, defaultPeriodFilter);
    }
  }, [lastFilters, loadReportData]);

  // Load initial data
  useEffect(() => {
    console.log('🔍 useReports hook inicializado');
    const defaultCampaignFilters: CampaignFilter = {
      campaignIds: [],
      status: [],
      search: ''
    };

    const defaultPeriodFilter: PeriodFilter = {
      type: 'preset',
      preset: 'all'
    };

    loadReportData(defaultCampaignFilters, defaultPeriodFilter);
  }, [loadReportData]);

  return {
    reportData,
    isLoading,
    error,
    loadReportData,
    exportReport,
    refreshData
  };
}
