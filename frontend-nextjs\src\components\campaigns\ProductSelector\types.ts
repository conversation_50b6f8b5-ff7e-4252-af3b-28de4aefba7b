export interface Product {
  readonly id: string;
  readonly sku: string;
  readonly name: string;
  readonly brand: string;
  readonly category: string;
  readonly price: number;
  readonly imageUrl?: string;
  readonly isActive: boolean;
  readonly isInActiveCampaign: boolean;
  readonly description?: string;
}

export interface AIEstimation {
  readonly estimatedAudience: number;
  readonly estimatedCost: number;
  readonly confidence: number;
  readonly isAnomalous: boolean;
  readonly factors: string[];
}

export interface ProductSelectorProps {
  readonly industryId: string;
  readonly selectedProducts: Product[];
  readonly onProductsChange: (products: Product[]) => void;
  readonly maxProducts?: number;
  readonly incentiveValue?: number;
}

export interface FilterState {
  readonly searchTerm: string;
  readonly selectedCategory: string;
  readonly selectedBrand: string;
}

export interface LoadingState {
  readonly isLoading: boolean;
  readonly isLoadingAI: boolean;
  readonly error: string | null;
}
