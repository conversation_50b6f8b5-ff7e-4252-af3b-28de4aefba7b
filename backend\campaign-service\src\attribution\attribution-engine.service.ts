import {
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Attribution } from '../database/entities/attribution.entity';
import { Touchpoint } from '../database/entities/touchpoint.entity';
import { Conversion, ConversionStatus } from '../database/entities/conversion.entity';
import { AttributionModel } from '../database/entities/attribution-model.entity';
import { AttributionModelConfig } from '../database/entities/attribution-model.entity';

export interface AttributionResult {
  conversionId: string;
  totalValue: number;
  attributions: Array<{
    touchpointId: string;
    campaignId?: string;
    weight: number;
    attributedValue: number;
    position: number;
    timeSinceTouch: number;
  }>;
  model: AttributionModel;
  touchpointCount: number;
}

@Injectable()
export class AttributionEngineService {
  private readonly logger = new Logger(AttributionEngineService.name);

  constructor(
    @InjectRepository(Attribution)
    private readonly attributionRepository: Repository<Attribution>,
    @InjectRepository(Touchpoint)
    private readonly touchpointRepository: Repository<Touchpoint>,
    @InjectRepository(Conversion)
    private readonly conversionRepository: Repository<Conversion>,
    @InjectRepository(AttributionModelConfig)
    private readonly modelConfigRepository: Repository<AttributionModelConfig>,
  ) {}

  async processConversion(
    conversionId: string,
    modelName?: string
  ): Promise<AttributionResult[]> {
    // Get conversion
    const conversion = await this.conversionRepository.findOne({
      where: { id: conversionId },
    });

    if (!conversion) {
      throw new Error(`Conversion ${conversionId} not found`);
    }

    // Get attribution models to use
    const models = modelName
      ? await this.modelConfigRepository.find({ where: { name: modelName, isActive: true } })
      : await this.modelConfigRepository.find({ where: { isActive: true } });

    if (models.length === 0) {
      throw new Error('No active attribution models found');
    }

    const results: AttributionResult[] = [];

    for (const model of models) {
      try {
        const result = await this.processConversionWithModel(conversion, model);
        results.push(result);
        
        // Save attributions to database
        await this.saveAttributions(result, model);
      } catch (error) {
        this.logger.error(`Error processing conversion ${conversionId} with model ${model.name}:`, error);
      }
    }

    return results;
  }

  private async processConversionWithModel(
    conversion: Conversion,
    model: AttributionModelConfig
  ): Promise<AttributionResult> {
    // Get touchpoints within the attribution window
    const windowStart = new Date(
      conversion.timestamp.getTime() - (model.lookbackWindowDays * 24 * 60 * 60 * 1000)
    );

    const touchpoints = await this.touchpointRepository.find({
      where: {
        userId: conversion.userId,
        timestamp: {
          $gte: windowStart,
          $lte: conversion.timestamp,
        } as any,
      },
      order: { timestamp: 'ASC' },
    });

    if (touchpoints.length === 0) {
      return {
        conversionId: conversion.id,
        totalValue: conversion.getTotalValue(),
        attributions: [],
        model: model.type,
        touchpointCount: 0,
      };
    }

    // Calculate attribution weights
    const attributions = this.calculateAttributionWeights(
      touchpoints,
      conversion,
      model
    );

    return {
      conversionId: conversion.id,
      totalValue: conversion.getTotalValue(),
      attributions,
      model: model.type,
      touchpointCount: touchpoints.length,
    };
  }

  private calculateAttributionWeights(
    touchpoints: Touchpoint[],
    conversion: Conversion,
    model: AttributionModelConfig
  ): Array<{
    touchpointId: string;
    campaignId?: string;
    weight: number;
    attributedValue: number;
    position: number;
    timeSinceTouch: number;
  }> {
    const totalValue = conversion.getTotalValue();
    const totalTouchpoints = touchpoints.length;

    return touchpoints.map((touchpoint, index) => {
      const position = index + 1;
      const timeSinceTouch = conversion.timestamp.getTime() - touchpoint.timestamp.getTime();

      let weight = 0;

      switch (model.type) {
        case AttributionModel.LAST_CLICK:
          weight = position === totalTouchpoints ? 1.0 : 0.0;
          break;

        case AttributionModel.FIRST_CLICK:
          weight = position === 1 ? 1.0 : 0.0;
          break;

        case AttributionModel.LINEAR:
          weight = 1.0 / totalTouchpoints;
          break;

        case AttributionModel.TIME_DECAY:
          const baseWeight = 1.0 / totalTouchpoints;
          const decayWeight = model.getTimeDecayWeight(timeSinceTouch);
          weight = baseWeight * decayWeight;
          break;

        case AttributionModel.POSITION_BASED:
          weight = model.getPositionWeight(position, totalTouchpoints);
          break;

        default:
          weight = 1.0 / totalTouchpoints;
      }

      // Apply interaction weight (different touchpoint types have different importance)
      weight *= touchpoint.getInteractionWeight();

      return {
        touchpointId: touchpoint.id,
        campaignId: touchpoint.campaignId,
        weight,
        attributedValue: totalValue * weight,
        position,
        timeSinceTouch,
      };
    });
  }

  private async saveAttributions(
    result: AttributionResult,
    model: AttributionModelConfig
  ): Promise<void> {
    // Delete existing attributions for this conversion and model
    await this.attributionRepository.delete({
      conversionId: result.conversionId,
      model: model.type,
    });

    // Get conversion for user ID
    const conversion = await this.conversionRepository.findOne({
      where: { id: result.conversionId },
    });

    if (!conversion) return;

    // Normalize weights so they sum to 1
    const totalWeight = result.attributions.reduce((sum, attr) => sum + attr.weight, 0);
    
    if (totalWeight === 0) return;

    const normalizedAttributions = result.attributions.map(attr => ({
      ...attr,
      weight: attr.weight / totalWeight,
      attributedValue: (attr.weight / totalWeight) * result.totalValue,
    }));

    // Create attribution records
    const attributions = normalizedAttributions.map(attr =>
      Attribution.createAttribution({
        userId: conversion.userId,
        conversionId: result.conversionId,
        touchpointId: attr.touchpointId,
        campaignId: attr.campaignId,
        model: model.type,
        weight: attr.weight,
        attributedValue: attr.attributedValue,
        currency: conversion.currency,
        position: attr.position,
        timeSinceTouch: attr.timeSinceTouch,
        touchpointCount: result.touchpointCount,
        metadata: {
          modelName: model.name,
          processedAt: new Date().toISOString(),
        },
      })
    );

    await this.attributionRepository.save(attributions);
    
    this.logger.log(
      `Saved ${attributions.length} attributions for conversion ${result.conversionId} using model ${model.name}`
    );
  }

  async reprocessConversions(
    startDate: Date,
    endDate: Date,
    modelName?: string
  ): Promise<{ processed: number; errors: number }> {
    const conversions = await this.conversionRepository.find({
      where: {
        timestamp: {
          $gte: startDate,
          $lte: endDate,
        } as any,
        status: ConversionStatus.COMPLETED,
      },
      order: { timestamp: 'ASC' },
    });

    this.logger.log(`Reprocessing ${conversions.length} conversions from ${startDate.toISOString()} to ${endDate.toISOString()}`);

    let processed = 0;
    let errors = 0;

    for (const conversion of conversions) {
      try {
        await this.processConversion(conversion.id, modelName);
        processed++;
      } catch (error) {
        this.logger.error(`Error reprocessing conversion ${conversion.id}:`, error);
        errors++;
      }
    }

    this.logger.log(`Reprocessing completed: ${processed} processed, ${errors} errors`);
    return { processed, errors };
  }

  async getAttributionSummary(
    campaignId?: string,
    startDate?: Date,
    endDate?: Date,
    model?: AttributionModel
  ): Promise<{
    totalAttributedValue: number;
    totalConversions: number;
    totalTouchpoints: number;
    averageJourneyLength: number;
    topCampaigns: Array<{
      campaignId: string;
      attributedValue: number;
      conversions: number;
      touchpoints: number;
    }>;
  }> {
    const queryBuilder = this.attributionRepository.createQueryBuilder('attribution')
      .leftJoinAndSelect('attribution.conversion', 'conversion')
      .leftJoinAndSelect('attribution.touchpoint', 'touchpoint')
      .leftJoinAndSelect('attribution.campaign', 'campaign');

    if (campaignId) {
      queryBuilder.andWhere('attribution.campaignId = :campaignId', { campaignId });
    }

    if (startDate) {
      queryBuilder.andWhere('conversion.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('conversion.timestamp <= :endDate', { endDate });
    }

    if (model) {
      queryBuilder.andWhere('attribution.model = :model', { model });
    }

    const attributions = await queryBuilder.getMany();

    const totalAttributedValue = attributions.reduce((sum, attr) => sum + attr.attributedValue, 0);
    const uniqueConversions = new Set(attributions.map(attr => attr.conversionId)).size;
    const uniqueTouchpoints = new Set(attributions.map(attr => attr.touchpointId)).size;
    const averageJourneyLength = attributions.length > 0 
      ? attributions.reduce((sum, attr) => sum + attr.touchpointCount, 0) / uniqueConversions
      : 0;

    // Group by campaign
    const campaignStats = new Map();
    attributions.forEach(attr => {
      if (!attr.campaignId) return;
      
      const existing = campaignStats.get(attr.campaignId) || {
        campaignId: attr.campaignId,
        attributedValue: 0,
        conversions: new Set(),
        touchpoints: new Set(),
      };
      
      existing.attributedValue += attr.attributedValue;
      existing.conversions.add(attr.conversionId);
      existing.touchpoints.add(attr.touchpointId);
      
      campaignStats.set(attr.campaignId, existing);
    });

    const topCampaigns = Array.from(campaignStats.values())
      .map(stats => ({
        campaignId: stats.campaignId,
        attributedValue: stats.attributedValue,
        conversions: stats.conversions.size,
        touchpoints: stats.touchpoints.size,
      }))
      .sort((a, b) => b.attributedValue - a.attributedValue)
      .slice(0, 10);

    return {
      totalAttributedValue,
      totalConversions: uniqueConversions,
      totalTouchpoints: uniqueTouchpoints,
      averageJourneyLength,
      topCampaigns,
    };
  }
}
