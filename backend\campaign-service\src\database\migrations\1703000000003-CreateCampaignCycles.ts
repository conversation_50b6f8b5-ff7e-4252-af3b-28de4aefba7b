import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateCampaignCycles1703000000003 implements MigrationInterface {
  name = 'CreateCampaignCycles1703000000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'campaign_cycles',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'industryId',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['weekly', 'monthly', 'quarterly', 'custom'],
            default: "'custom'",
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['draft', 'active', 'paused', 'completed', 'cancelled', 'expired'],
            default: "'draft'",
          },
          {
            name: 'startDate',
            type: 'timestamp with time zone',
          },
          {
            name: 'endDate',
            type: 'timestamp with time zone',
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'maxCampaigns',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'maxProducts',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'maxBudget',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'maxIncentivePercentage',
            type: 'decimal',
            precision: 5,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'maxParticipants',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'currentCampaigns',
            type: 'integer',
            default: 0,
          },
          {
            name: 'currentProducts',
            type: 'integer',
            default: 0,
          },
          {
            name: 'currentBudget',
            type: 'decimal',
            precision: 15,
            scale: 2,
            default: 0,
          },
          {
            name: 'currentParticipants',
            type: 'integer',
            default: 0,
          },
          {
            name: 'autoExtend',
            type: 'boolean',
            default: false,
          },
          {
            name: 'extensionDays',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'notificationThresholds',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdBy',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'updatedBy',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'campaign_cycles',
      new TableIndex({
        name: 'IDX_campaign_cycles_industry_status',
        columnNames: ['industryId', 'status'],
      }),
    );

    await queryRunner.createIndex(
      'campaign_cycles',
      new TableIndex({
        name: 'IDX_campaign_cycles_dates',
        columnNames: ['startDate', 'endDate'],
      }),
    );

    await queryRunner.createIndex(
      'campaign_cycles',
      new TableIndex({
        name: 'IDX_campaign_cycles_status_active',
        columnNames: ['status', 'isActive'],
      }),
    );

    // Insert sample data
    await queryRunner.query(`
      INSERT INTO campaign_cycles (
        name, description, "industryId", type, status, 
        "startDate", "endDate", "maxCampaigns", "maxProducts", 
        "maxBudget", "maxIncentivePercentage", "maxParticipants",
        "createdBy"
      ) VALUES 
      (
        'Ciclo Q1 2024', 
        'Primeiro trimestre de 2024 - Campanhas de verão',
        '123',
        'quarterly',
        'active',
        '2024-01-01 00:00:00+00',
        '2024-03-31 23:59:59+00',
        50,
        500,
        100000.00,
        30.00,
        10000,
        'system'
      ),
      (
        'Ciclo Q2 2024', 
        'Segundo trimestre de 2024 - Campanhas de outono',
        '123',
        'quarterly',
        'draft',
        '2024-04-01 00:00:00+00',
        '2024-06-30 23:59:59+00',
        40,
        400,
        80000.00,
        25.00,
        8000,
        'system'
      ),
      (
        'Ciclo Especial Natal', 
        'Ciclo especial para campanhas de Natal',
        '456',
        'custom',
        'draft',
        '2024-12-01 00:00:00+00',
        '2024-12-31 23:59:59+00',
        20,
        200,
        50000.00,
        40.00,
        5000,
        'system'
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('campaign_cycles');
  }
}
