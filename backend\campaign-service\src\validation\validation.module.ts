import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ValidationService } from './validation.service';
import { ValidationController } from './validation.controller';
import { IndustryRule } from '../database/entities/industry-rules.entity';

@Module({
  imports: [TypeOrmModule.forFeature([IndustryRule])],
  controllers: [ValidationController],
  providers: [ValidationService],
  exports: [ValidationService],
})
export class ValidationModule {}
