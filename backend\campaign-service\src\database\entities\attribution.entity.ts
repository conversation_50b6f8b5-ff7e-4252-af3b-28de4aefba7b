import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Campaign } from './campaign.entity';
import { Conversion } from './conversion.entity';
import { Touchpoint } from './touchpoint.entity';
import { AttributionModel } from './attribution-model.entity';

@Entity('attributions')
@Index(['userId', 'conversionId'])
@Index(['campaignId', 'conversionId'])
@Index(['touchpointId', 'conversionId'])
@Index(['model', 'createdAt'])
@Index(['conversionId'])
export class Attribution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  userId: string;

  @Column({ type: 'uuid' })
  @Index()
  conversionId: string;

  @Column({ type: 'uuid' })
  @Index()
  touchpointId: string;

  @Column({ type: 'uuid', nullable: true })
  @Index()
  campaignId: string;

  @Column({ type: 'enum', enum: AttributionModel })
  model: AttributionModel;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  weight: number; // Attribution weight (0-1)

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  attributedValue: number; // Value attributed to this touchpoint

  @Column({ type: 'varchar', length: 3, default: 'BRL' })
  currency: string;

  @Column({ type: 'int', default: 1 })
  position: number; // Position in the customer journey (1 = first, etc.)

  @Column({ type: 'int', default: 0 })
  timeSinceTouch: number; // Time between touchpoint and conversion (milliseconds)

  @Column({ type: 'int', default: 0 })
  touchpointCount: number; // Total touchpoints in the journey

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // Additional attribution data

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Conversion, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'conversionId' })
  conversion: Conversion;

  @ManyToOne(() => Touchpoint, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'touchpointId' })
  touchpoint: Touchpoint;

  @ManyToOne(() => Campaign, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'campaignId' })
  campaign: Campaign;

  // Helper methods
  getFormattedAttributedValue(): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: this.currency,
    }).format(this.attributedValue);
  }

  getWeightPercentage(): string {
    return (this.weight * 100).toFixed(1) + '%';
  }

  getModelLabel(): string {
    const labels = {
      [AttributionModel.LAST_CLICK]: 'Último Clique',
      [AttributionModel.FIRST_CLICK]: 'Primeiro Clique',
      [AttributionModel.LINEAR]: 'Linear',
      [AttributionModel.TIME_DECAY]: 'Decaimento Temporal',
      [AttributionModel.POSITION_BASED]: 'Baseado em Posição',
      [AttributionModel.DATA_DRIVEN]: 'Orientado por Dados',
    };

    return labels[this.model] || this.model;
  }

  getTimeSinceTouchFormatted(): string {
    const hours = Math.floor(this.timeSinceTouch / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days} dia${days > 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours} hora${hours > 1 ? 's' : ''}`;
    } else {
      const minutes = Math.floor(this.timeSinceTouch / (1000 * 60));
      return `${minutes} minuto${minutes > 1 ? 's' : ''}`;
    }
  }

  isFirstTouch(): boolean {
    return this.position === 1;
  }

  isLastTouch(): boolean {
    return this.position === this.touchpointCount;
  }

  static createAttribution(data: {
    userId: string;
    conversionId: string;
    touchpointId: string;
    campaignId?: string;
    model: AttributionModel;
    weight: number;
    attributedValue: number;
    currency?: string;
    position: number;
    timeSinceTouch: number;
    touchpointCount: number;
    metadata?: Record<string, any>;
  }): Partial<Attribution> {
    return {
      userId: data.userId,
      conversionId: data.conversionId,
      touchpointId: data.touchpointId,
      campaignId: data.campaignId,
      model: data.model,
      weight: data.weight,
      attributedValue: data.attributedValue,
      currency: data.currency || 'BRL',
      position: data.position,
      timeSinceTouch: data.timeSinceTouch,
      touchpointCount: data.touchpointCount,
      metadata: data.metadata,
    };
  }
}
