import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IsString, IsArray, IsNumber, IsOptional, <PERSON>, Max, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { Product } from '../database/entities/product.entity';
import { ProductCategory } from '../database/entities/product-category.entity';
import { EstimationResult, EstimationInput } from './domain/estimation-result';
import { ProductForEstimation } from './domain/product';
import { RateLimiterService } from './services/rate-limiter.service';
import { AnomalyDetectorService } from './services/anomaly-detector.service';
import { EstimationCalculatorService } from './services/estimation-calculator.service';

export class CampaignEstimationInput {
  @IsString()
  industryId: string;

  @IsArray()
  @IsString({ each: true })
  productIds: string[];

  @IsNumber()
  @Min(0.1)
  @Max(100)
  incentivePercentage: number;

  @IsNumber()
  @Min(1)
  @Max(365)
  validityDays: number;

  @IsOptional()
  targetAudience?: {
    ageRange?: [number, number];
    gender?: 'male' | 'female' | 'all';
    location?: string[];
    interests?: string[];
  };

  @IsOptional()
  @IsNumber()
  @Min(0)
  budget?: number;

  @IsOptional()
  @IsString()
  campaignType?: 'awareness' | 'conversion' | 'retention' | 'acquisition';
}

export interface PerformanceEstimation {
  estimatedReach: number;
  estimatedEngagement: number;
  estimatedConversions: number;
  estimatedRevenue: number;
  estimatedROI: number;
  confidenceScore: number; // 0-100
  factors: {
    productPopularity: number;
    incentiveAttractiveness: number;
    seasonality: number;
    marketTrends: number;
    historicalPerformance: number;
  };
  recommendations: string[];
  warnings: string[];
}

export interface CampaignSuggestion {
  id: string;
  name: string;
  description: string;
  suggestedProducts: string[];
  suggestedIncentive: number;
  suggestedDuration: number;
  estimatedPerformance: PerformanceEstimation;
  reasoning: string[];
  priority: 'high' | 'medium' | 'low';
  category: string;
}

export interface AIInsights {
  marketTrends: {
    trending: string[];
    declining: string[];
    seasonal: string[];
  };
  competitorAnalysis: {
    averageIncentive: number;
    popularProducts: string[];
    successfulStrategies: string[];
  };
  optimization: {
    bestTimeToLaunch: string;
    optimalIncentiveRange: [number, number];
    recommendedDuration: number;
    targetAudienceInsights: string[];
  };
  riskFactors: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    mitigation: string[];
  };
}

/**
 * Main service for AI-powered campaign estimation
 * Orchestrates various estimation services following SOLID principles
 */
@Injectable()
export class AIEstimatorService {
  private readonly logger = new Logger(AIEstimatorService.name);
  private readonly rateLimitMap = new Map<string, { count: number; resetTime: number }>();
  private readonly RATE_LIMIT_PER_HOUR = 100;

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(ProductCategory)
    private readonly categoryRepository: Repository<ProductCategory>,
    private readonly rateLimiterService: RateLimiterService,
    private readonly anomalyDetectorService: AnomalyDetectorService,
    private readonly calculatorService: EstimationCalculatorService,
  ) {}

  /**
   * Estimate campaign performance using AI algorithms
   * @param input - Campaign estimation input
   * @returns Performance estimation result
   */
  async estimateCampaignPerformance(input: CampaignEstimationInput): Promise<PerformanceEstimation> {
    const startTime = Date.now();

    try {
      // Validate input
      this.validateEstimationInput(input);

      // Check rate limiting
      const rateLimitInfo = this.rateLimiterService.checkRateLimit(input.industryId);
      if (rateLimitInfo.isExceeded) {
        const resetMinutes = this.rateLimiterService.getResetTimeInMinutes(input.industryId);
        throw new BadRequestException(`Limite de estimativas excedido, tente em ${resetMinutes} minutos`);
      }

      // Get and validate products
      const products = await this.getValidatedProducts(input.productIds);

      // Convert to domain objects
      const domainProducts = this.convertToDomainProducts(products);

      // Create estimation input
      const estimationInput: EstimationInput = {
        industryId: input.industryId,
        productIds: input.productIds,
        incentivePercentage: input.incentivePercentage,
        validityDays: input.validityDays,
        targetAudience: input.targetAudience,
        budget: input.budget,
        campaignType: input.campaignType,
      };

      // Calculate estimation
      const estimation = this.calculatorService.calculateEstimation(estimationInput, domainProducts);

      // Detect anomalies
      const anomalyResult = this.anomalyDetectorService.detectAnomalies(
        estimation.revenue * 0.3, // Estimated cost
        estimation.revenue,
        domainProducts
      );

      if (anomalyResult.isAnomalous) {
        throw new BadRequestException('Padrão anômalo detectado - custo 10x maior que histórico');
      }

      // Combine warnings
      const allWarnings = [...estimation.warnings, ...anomalyResult.warnings];

      const result: PerformanceEstimation = {
        estimatedReach: estimation.reach,
        estimatedEngagement: estimation.engagement,
        estimatedConversions: estimation.conversions,
        estimatedRevenue: estimation.revenue,
        estimatedROI: estimation.roi,
        confidenceScore: estimation.confidence,
        factors: estimation.factors,
        recommendations: estimation.recommendations,
        warnings: allWarnings,
      };

      this.logger.log(`Estimation completed in ${Date.now() - startTime}ms with ${estimation.confidence}% confidence`);
      return result;

    } catch (error) {
      this.logger.error('Error in campaign estimation:', error);
      this.handleEstimationError(error);
    }
  }

  /**
   * Validate estimation input parameters
   */
  private validateEstimationInput(input: CampaignEstimationInput): void {
    if (!input.industryId?.trim()) {
      throw new BadRequestException('Industry ID is required');
    }

    if (!input.productIds || input.productIds.length === 0) {
      throw new BadRequestException('At least one product is required');
    }

    if (input.productIds.length > 1000) {
      throw new BadRequestException('Maximum 1000 products per estimation');
    }

    if (input.incentivePercentage <= 0 || input.incentivePercentage > 100) {
      throw new BadRequestException('Incentive percentage must be between 0 and 100');
    }

    if (input.validityDays <= 0 || input.validityDays > 365) {
      throw new BadRequestException('Validity days must be between 1 and 365');
    }
  }

  /**
   * Get and validate products from database
   */
  private async getValidatedProducts(productIds: string[]): Promise<Product[]> {
    const products = await this.productRepository.findByIds(productIds);

    if (products.length === 0) {
      throw new BadRequestException('Nenhum produto válido encontrado');
    }

    return products;
  }

  /**
   * Convert database products to domain objects
   */
  private convertToDomainProducts(products: Product[]): ProductForEstimation[] {
    return products.map(product => new ProductForEstimation(
      product.id,
      product.name,
      product.price || 0,
      product.category?.name || 'Geral',
      product.brandId || 'Sem marca',
      product.salesCount || 0,
      product.averageRating || 0,
      product.isEligibleForIncentives || false
    ));
  }

  /**
   * Handle estimation errors with appropriate messages
   */
  private handleEstimationError(error: any): never {
    if (error instanceof BadRequestException) {
      throw error;
    }

    if (error.message?.includes('Padrão anômalo')) {
      throw new BadRequestException('Padrão anômalo detectado - custo 10x maior que histórico');
    }

    if (error.message?.includes('Limite de estimativas')) {
      throw new BadRequestException('Limite de estimativas excedido, tente em 1 hora');
    }

    throw new BadRequestException('Serviço temporariamente indisponível');
  }



  async generateCampaignSuggestions(industryId: string, limit = 5): Promise<CampaignSuggestion[]> {
    // Get popular and trending products
    const popularProducts = await this.productRepository.find({
      where: { 
        industryId,
        isEligibleForIncentives: true,
        status: 'active' as any,
      },
      order: { salesCount: 'DESC', averageRating: 'DESC' },
      take: 20,
    });

    const suggestions: CampaignSuggestion[] = [];

    // Generate different types of campaign suggestions
    const campaignTypes = [
      {
        type: 'high-conversion',
        name: 'Campanha de Alta Conversão',
        description: 'Focada em produtos com histórico de alta conversão',
        incentiveRange: [15, 25],
        priority: 'high' as const,
      },
      {
        type: 'awareness',
        name: 'Campanha de Awareness',
        description: 'Aumentar visibilidade de produtos novos ou menos conhecidos',
        incentiveRange: [10, 20],
        priority: 'medium' as const,
      },
      {
        type: 'seasonal',
        name: 'Campanha Sazonal',
        description: 'Aproveitar tendências sazonais do mercado',
        incentiveRange: [20, 30],
        priority: 'high' as const,
      },
      {
        type: 'retention',
        name: 'Campanha de Retenção',
        description: 'Manter engajamento de clientes existentes',
        incentiveRange: [12, 18],
        priority: 'medium' as const,
      },
      {
        type: 'clearance',
        name: 'Campanha de Liquidação',
        description: 'Acelerar vendas de produtos com estoque alto',
        incentiveRange: [25, 40],
        priority: 'low' as const,
      },
    ];

    for (let i = 0; i < Math.min(limit, campaignTypes.length); i++) {
      const campaignType = campaignTypes[i];
      const selectedProducts = this.selectProductsForCampaign(popularProducts, campaignType.type);
      const suggestedIncentive = campaignType.incentiveRange[0] + 
        Math.random() * (campaignType.incentiveRange[1] - campaignType.incentiveRange[0]);

      // Estimate performance for this suggestion
      const estimation = await this.estimateCampaignPerformance({
        industryId,
        productIds: selectedProducts.map(p => p.id),
        incentivePercentage: suggestedIncentive,
        validityDays: 30,
        campaignType: campaignType.type as any,
      });

      suggestions.push({
        id: `suggestion_${i + 1}`,
        name: campaignType.name,
        description: campaignType.description,
        suggestedProducts: selectedProducts.map(p => p.id),
        suggestedIncentive: Math.round(suggestedIncentive * 10) / 10,
        suggestedDuration: this.calculateOptimalDuration(campaignType.type),
        estimatedPerformance: estimation,
        reasoning: this.generateReasoningForSuggestion(campaignType, selectedProducts, estimation),
        priority: campaignType.priority,
        category: campaignType.type,
      });
    }

    // Sort by estimated ROI and priority
    return suggestions.sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, low: 1 };
      const scoreA = a.estimatedPerformance.estimatedROI + (priorityWeight[a.priority] * 10);
      const scoreB = b.estimatedPerformance.estimatedROI + (priorityWeight[b.priority] * 10);
      return scoreB - scoreA;
    });
  }

  async getAIInsights(industryId: string): Promise<AIInsights> {
    // Simulate AI analysis of market data
    const products = await this.productRepository.find({
      where: { industryId },
      order: { salesCount: 'DESC' },
      take: 100,
    });

    // Analyze trends (simplified simulation)
    const trending = products
      .filter(p => p.salesCount > 100)
      .slice(0, 5)
      .map(p => p.name);

    const declining = products
      .filter(p => p.salesCount < 10)
      .slice(0, 3)
      .map(p => p.name);

    // Seasonal analysis (simplified)
    const currentMonth = new Date().getMonth();
    const seasonal = this.getSeasonalProducts(currentMonth);

    // Calculate market averages
    const avgIncentive = 15; // Simulated market average
    const popularProducts = products.slice(0, 10).map(p => p.name);

    return {
      marketTrends: {
        trending,
        declining,
        seasonal,
      },
      competitorAnalysis: {
        averageIncentive: avgIncentive,
        popularProducts,
        successfulStrategies: [
          'Incentivos entre 15-25% mostram melhor ROI',
          'Campanhas de 2-4 semanas têm maior engajamento',
          'Produtos com rating > 4.0 convertem 40% mais',
        ],
      },
      optimization: {
        bestTimeToLaunch: this.getBestLaunchTime(),
        optimalIncentiveRange: [12, 22] as [number, number],
        recommendedDuration: 21, // days
        targetAudienceInsights: [
          'Público 25-45 anos responde melhor a incentivos',
          'Campanhas em fins de semana têm 20% mais engajamento',
          'Produtos de categoria premium precisam de incentivos menores',
        ],
      },
      riskFactors: {
        level: 'medium' as const,
        factors: [
          'Sazonalidade pode afetar performance',
          'Concorrência alta no setor',
          'Flutuações econômicas recentes',
        ],
        mitigation: [
          'Diversificar produtos na campanha',
          'Monitorar concorrentes ativamente',
          'Ajustar incentivos conforme performance',
        ],
      },
    };
  }

  private async calculateEstimationFactors(products: Product[], input: CampaignEstimationInput) {
    // Product popularity factor (0.5 - 1.5)
    const avgSales = products.reduce((sum, p) => sum + (p.salesCount || 0), 0) / products.length;
    const productPopularity = Math.min(1.5, Math.max(0.5, 0.8 + (avgSales / 1000)));

    // Incentive attractiveness factor (0.7 - 1.3)
    const incentiveAttractiveness = Math.min(1.3, Math.max(0.7, 0.8 + (input.incentivePercentage / 100)));

    // Seasonality factor (0.8 - 1.2)
    const seasonality = this.calculateSeasonalityFactor();

    // Market trends factor (0.9 - 1.1)
    const marketTrends = 1.0; // Simplified - would use real market data

    // Historical performance factor (0.6 - 1.4)
    const avgRating = products.reduce((sum, p) => sum + (p.averageRating || 0), 0) / products.length;
    const historicalPerformance = Math.min(1.4, Math.max(0.6, 0.5 + (avgRating / 5)));

    return {
      productPopularity,
      incentiveAttractiveness,
      seasonality,
      marketTrends,
      historicalPerformance,
    };
  }

  private estimateReach(products: Product[], incentivePercentage: number): number {
    // Base reach calculation (simplified)
    const baseReach = products.length * 1000; // 1000 potential customers per product
    const incentiveMultiplier = 1 + (incentivePercentage / 100);
    return Math.round(baseReach * incentiveMultiplier);
  }

  private calculateBaseConversionRate(avgPrice: number, incentivePercentage: number): number {
    // Conversion rate based on price and incentive (simplified model)
    let baseRate = 0.05; // 5% base conversion rate
    
    // Adjust for price (higher price = lower conversion)
    if (avgPrice > 100) baseRate *= 0.8;
    if (avgPrice > 500) baseRate *= 0.7;
    
    // Adjust for incentive
    baseRate *= (1 + (incentivePercentage / 200)); // Incentive boost
    
    return Math.min(0.25, baseRate); // Cap at 25%
  }

  private calculateConfidenceScore(factors: any, productCount: number, totalSales: number): number {
    let confidence = 70; // Base confidence
    
    // Adjust based on data quality
    if (productCount >= 5) confidence += 10;
    if (totalSales > 1000) confidence += 10;
    if (factors.historicalPerformance > 1.0) confidence += 5;
    
    return Math.min(95, Math.max(30, confidence));
  }

  private generateInsights(input: CampaignEstimationInput, factors: any, metrics: any) {
    const recommendations: string[] = [];
    const warnings: string[] = [];

    // Generate recommendations
    if (factors.incentiveAttractiveness < 1.0) {
      recommendations.push('Considere aumentar o incentivo para melhorar a atratividade');
    }
    
    if (factors.productPopularity > 1.2) {
      recommendations.push('Produtos populares selecionados - boa escolha para alcance');
    }
    
    if (metrics.roi > 100) {
      recommendations.push('ROI estimado excelente - campanha muito promissora');
    }

    // Generate warnings
    if (factors.seasonality < 0.9) {
      warnings.push('Período pode não ser ideal devido à sazonalidade');
    }
    
    if (input.incentivePercentage > 30) {
      warnings.push('Incentivo muito alto pode impactar margem de lucro');
    }

    return { recommendations, warnings };
  }

  private selectProductsForCampaign(products: Product[], campaignType: string): Product[] {
    switch (campaignType) {
      case 'high-conversion':
        return products.filter(p => p.averageRating >= 4.0).slice(0, 5);
      case 'awareness':
        return products.filter(p => p.salesCount < 50).slice(0, 8);
      case 'seasonal':
        return products.slice(0, 6);
      case 'retention':
        return products.filter(p => p.salesCount > 100).slice(0, 4);
      case 'clearance':
        return products.filter(p => p.stockQuantity > 100).slice(0, 10);
      default:
        return products.slice(0, 5);
    }
  }

  private calculateOptimalDuration(campaignType: string): number {
    const durations = {
      'high-conversion': 14,
      'awareness': 28,
      'seasonal': 21,
      'retention': 30,
      'clearance': 7,
    };
    return durations[campaignType] || 21;
  }

  private generateReasoningForSuggestion(campaignType: any, products: Product[], estimation: PerformanceEstimation): string[] {
    const reasoning = [
      `Tipo de campanha: ${campaignType.description}`,
      `${products.length} produtos selecionados com base em ${campaignType.type}`,
      `ROI estimado de ${estimation.estimatedROI.toFixed(1)}%`,
    ];

    if (estimation.confidenceScore > 80) {
      reasoning.push('Alta confiança na estimativa baseada em dados históricos');
    }

    return reasoning;
  }

  private calculateSeasonalityFactor(): number {
    const month = new Date().getMonth();
    // Simplified seasonality (higher in Nov-Dec, lower in Jan-Feb)
    const seasonalFactors = [0.8, 0.8, 0.9, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.1, 1.2, 1.2];
    return seasonalFactors[month];
  }

  private getSeasonalProducts(month: number): string[] {
    const seasonalMap = {
      0: ['Produtos de Ano Novo', 'Itens de Dieta'],
      1: ['Produtos Românticos', 'Chocolates'],
      2: ['Produtos de Primavera', 'Limpeza'],
      // ... more months
    };
    return seasonalMap[month] || ['Produtos Gerais'];
  }

  private getBestLaunchTime(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Manhã (9h-12h)';
    if (hour < 18) return 'Tarde (14h-17h)';
    return 'Noite (19h-21h)';
  }

  /**
   * Check rate limiting (S-008 requirement)
   */
  private async checkRateLimit(industryId: string): Promise<void> {
    const now = Date.now();
    const hourInMs = 60 * 60 * 1000;

    const rateData = this.rateLimitMap.get(industryId);

    if (!rateData || now > rateData.resetTime) {
      // Reset or initialize rate limit
      this.rateLimitMap.set(industryId, {
        count: 1,
        resetTime: now + hourInMs,
      });
      return;
    }

    if (rateData.count >= this.RATE_LIMIT_PER_HOUR) {
      const resetInMinutes = Math.ceil((rateData.resetTime - now) / (60 * 1000));
      throw new Error(`Limite de estimativas excedido, tente em ${resetInMinutes} minutos`);
    }

    rateData.count++;
  }

  /**
   * Detect anomalies in estimation (S-008 requirement)
   */
  private detectAnomalies(
    estimatedCost: number,
    estimatedRevenue: number,
    avgPrice: number
  ): { isAnomalous: boolean; warnings: string[] } {
    const warnings: string[] = [];
    let isAnomalous = false;

    // Check for cost anomalies (10x higher than normal)
    const normalCostPerConversion = avgPrice * 0.1; // Normal 10% of product price
    const costPerConversion = estimatedCost / (estimatedRevenue / avgPrice);

    if (costPerConversion > normalCostPerConversion * 10) {
      isAnomalous = true;
      warnings.push('Padrão anômalo detectado: custo por conversão muito alto');
    }

    // Check for revenue anomalies
    if (estimatedRevenue > avgPrice * 100000) { // More than 100k units
      warnings.push('Receita estimada muito alta - verifique os parâmetros');
    }

    // Check for low confidence scenarios
    if (estimatedCost > estimatedRevenue) {
      warnings.push('Custo estimado maior que receita - campanha não rentável');
    }

    return { isAnomalous, warnings };
  }
}
