import { Test } from '@nestjs/testing';

// Global test setup
beforeAll(async () => {
  // Setup global test configuration
  jest.setTimeout(30000);
});

afterAll(async () => {
  // Cleanup after all tests
});

// Mock external dependencies
jest.mock('amqplib', () => ({
  connect: jest.fn().mockResolvedValue({
    createChannel: jest.fn().mockResolvedValue({
      assertQueue: jest.fn(),
      sendToQueue: jest.fn(),
      consume: jest.fn(),
      close: jest.fn(),
    }),
    close: jest.fn(),
  }),
}));

// Mock Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    disconnect: jest.fn(),
  }));
});

// Mock external HTTP calls
global.fetch = jest.fn();
