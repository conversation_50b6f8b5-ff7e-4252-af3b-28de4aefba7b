import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateCampaignsTable1703000000004 implements MigrationInterface {
  name = 'CreateCampaignsTable1703000000004';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create campaign status enum
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "campaign_status_enum" AS ENUM(
          'draft', 'scheduled', 'active', 'paused', 'ended', 'reactivated'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create incentive type enum
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "incentive_type_enum" AS ENUM(
          'percentage', 'amount_brl'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create campaigns table
    await queryRunner.createTable(
      new Table({
        name: 'campaigns',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'industryId',
            type: 'uuid',
          },
          {
            name: 'createdBy',
            type: 'uuid',
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['draft', 'scheduled', 'active', 'paused', 'ended', 'reactivated'],
            default: "'draft'",
          },
          {
            name: 'incentiveType',
            type: 'enum',
            enum: ['percentage', 'amount_brl'],
            default: "'percentage'",
          },
          {
            name: 'incentiveValue',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'maxIncentiveValue',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'startDate',
            type: 'timestamp with time zone',
          },
          {
            name: 'endDate',
            type: 'timestamp with time zone',
          },
          {
            name: 'budget',
            type: 'decimal',
            precision: 12,
            scale: 2,
            default: 0,
          },
          {
            name: 'totalInvestment',
            type: 'decimal',
            precision: 12,
            scale: 2,
            default: 0,
          },
          {
            name: 'totalRevenue',
            type: 'decimal',
            precision: 12,
            scale: 2,
            default: 0,
          },
          {
            name: 'impactedConsumers',
            type: 'integer',
            default: 0,
          },
          {
            name: 'convertedConsumers',
            type: 'integer',
            default: 0,
          },
          {
            name: 'conversionRate',
            type: 'decimal',
            precision: 5,
            scale: 2,
            default: 0,
          },
          {
            name: 'roi',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'cycleId',
            type: 'varchar',
            length: '50',
            isNullable: true,
          },
          {
            name: 'nextTransitionAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'nextTransitionAction',
            type: 'varchar',
            length: '50',
            isNullable: true,
          },
          {
            name: 'aiEstimation',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'publishedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'pausedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'endedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'pausedBy',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'endedBy',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create indexes
    await queryRunner.query(`CREATE INDEX "IDX_campaigns_industry_id" ON "campaigns" ("industryId")`);
    await queryRunner.query(`CREATE INDEX "IDX_campaigns_status" ON "campaigns" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_campaigns_dates" ON "campaigns" ("startDate", "endDate")`);
    await queryRunner.query(`CREATE INDEX "IDX_campaigns_created_by" ON "campaigns" ("createdBy")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('campaigns');
    await queryRunner.query(`DROP TYPE IF EXISTS "campaign_status_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "incentive_type_enum"`);
  }
}
