import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CycleManagementService } from './cycle-management.service';
import { CycleManagementController } from './cycle-management.controller';
import { CampaignCycle } from '../database/entities/campaign-cycle.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CampaignCycle])],
  controllers: [CycleManagementController],
  providers: [CycleManagementService],
  exports: [CycleManagementService],
})
export class CycleManagementModule {}
