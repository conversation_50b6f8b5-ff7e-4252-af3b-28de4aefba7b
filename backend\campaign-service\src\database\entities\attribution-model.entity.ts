import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum AttributionModel {
  FIRST_CLICK = 'first_click',
  LAST_CLICK = 'last_click',
  LINEAR = 'linear',
  TIME_DECAY = 'time_decay',
  POSITION_BASED = 'position_based',
  DATA_DRIVEN = 'data_driven',
}

@Entity('attribution_models')
@Index(['name'])
@Index(['isActive'])
export class AttributionModelConfig {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  @Index()
  name: string;

  @Column({ type: 'varchar', length: 255 })
  displayName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: AttributionModel })
  type: AttributionModel;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isDefault: boolean;

  @Column({ type: 'int', default: 7 })
  lookbackWindowDays: number; // Attribution window in days

  @Column({ type: 'json', nullable: true })
  configuration: Record<string, any>; // Model-specific configuration

  @Column({ type: 'json', nullable: true })
  weights: Record<string, number>; // Position-based weights

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0.5 })
  decayRate: number; // For time decay model

  @Column({ type: 'int', default: 100 })
  priority: number; // Model priority for selection

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  getConfigurationValue(key: string, defaultValue: any = null): any {
    return this.configuration?.[key] ?? defaultValue;
  }

  getPositionWeight(position: number, totalPositions: number): number {
    if (this.type === AttributionModel.POSITION_BASED && this.weights) {
      // Position-based model: first and last touch get higher weights
      if (position === 1) {
        return this.weights['first'] || 0.4;
      } else if (position === totalPositions) {
        return this.weights['last'] || 0.4;
      } else {
        const middleWeight = this.weights['middle'] || 0.2;
        const middlePositions = Math.max(1, totalPositions - 2);
        return middleWeight / middlePositions;
      }
    }

    if (this.type === AttributionModel.LINEAR) {
      // Linear model: equal weight for all positions
      return 1.0 / totalPositions;
    }

    if (this.type === AttributionModel.FIRST_CLICK) {
      return position === 1 ? 1.0 : 0.0;
    }

    if (this.type === AttributionModel.LAST_CLICK) {
      return position === totalPositions ? 1.0 : 0.0;
    }

    // Default to equal weight
    return 1.0 / totalPositions;
  }

  getTimeDecayWeight(timeSinceTouch: number): number {
    if (this.type !== AttributionModel.TIME_DECAY) {
      return 1.0;
    }

    // Time decay: more recent touchpoints get higher weight
    const daysSinceTouch = timeSinceTouch / (1000 * 60 * 60 * 24);
    return Math.exp(-this.decayRate * daysSinceTouch);
  }

  static getDefaultModels(): Partial<AttributionModelConfig>[] {
    return [
      {
        name: 'last_click_7d',
        displayName: 'Último Clique (7 dias)',
        description: 'Atribui 100% do valor ao último clique antes da conversão',
        type: AttributionModel.LAST_CLICK,
        isDefault: true,
        lookbackWindowDays: 7,
        priority: 100,
      },
      {
        name: 'first_click_7d',
        displayName: 'Primeiro Clique (7 dias)',
        description: 'Atribui 100% do valor ao primeiro clique na jornada',
        type: AttributionModel.FIRST_CLICK,
        lookbackWindowDays: 7,
        priority: 90,
      },
      {
        name: 'linear_7d',
        displayName: 'Linear (7 dias)',
        description: 'Distribui o valor igualmente entre todos os touchpoints',
        type: AttributionModel.LINEAR,
        lookbackWindowDays: 7,
        priority: 80,
      },
      {
        name: 'time_decay_7d',
        displayName: 'Decaimento Temporal (7 dias)',
        description: 'Dá mais peso aos touchpoints mais recentes',
        type: AttributionModel.TIME_DECAY,
        lookbackWindowDays: 7,
        decayRate: 0.5,
        priority: 70,
      },
      {
        name: 'position_based_7d',
        displayName: 'Baseado em Posição (7 dias)',
        description: 'Dá 40% para primeiro e último clique, 20% para os do meio',
        type: AttributionModel.POSITION_BASED,
        lookbackWindowDays: 7,
        weights: {
          first: 0.4,
          last: 0.4,
          middle: 0.2,
        },
        priority: 60,
      },
    ];
  }
}
