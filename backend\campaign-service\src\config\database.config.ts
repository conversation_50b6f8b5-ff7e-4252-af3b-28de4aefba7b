import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions } from 'typeorm';

@Injectable()
export class DatabaseConfig implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    const isProduction = this.configService.get('NODE_ENV') === 'production';
    const isDevelopment = this.configService.get('NODE_ENV') === 'development';

    return {
      type: 'postgres',
      url: this.configService.get('DATABASE_URL'),
      entities: [__dirname + '/../**/*.entity{.ts,.js}'],
      migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
      synchronize: false, // Disabled due to index conflicts
      logging: isDevelopment ? ['query', 'error', 'warn'] : ['error'],
      ssl: isProduction ? { rejectUnauthorized: false } : false,

      // Connection Pool Optimization
      extra: {
        max: isProduction ? 20 : 10, // Maximum connections in pool
        min: isProduction ? 5 : 2,   // Minimum connections in pool
        idleTimeoutMillis: 30000,    // Close idle connections after 30s
        connectionTimeoutMillis: 2000, // Connection timeout
        acquireTimeoutMillis: 60000,   // Acquire connection timeout
        createTimeoutMillis: 30000,    // Create connection timeout
        destroyTimeoutMillis: 5000,    // Destroy connection timeout
        reapIntervalMillis: 1000,      // Check for idle connections every 1s
        createRetryIntervalMillis: 200, // Retry connection creation

        // PostgreSQL specific optimizations
        statement_timeout: 30000,      // 30s statement timeout
        query_timeout: 30000,          // 30s query timeout
        application_name: 'retail-media-campaign-service',

        // Performance settings
        shared_preload_libraries: 'pg_stat_statements',
        log_statement: isDevelopment ? 'all' : 'none',
        log_min_duration_statement: isDevelopment ? 0 : 1000, // Log slow queries
      },

      // Retry configuration
      retryAttempts: 3,
      retryDelay: 3000,

      // Entity loading
      autoLoadEntities: true,

      // Cache configuration - temporarily disabled
      // cache: {
      //   type: 'redis',
      //   options: {
      //     host: this.configService.get('REDIS_HOST', 'localhost'),
      //     port: this.configService.get('REDIS_PORT', 6379),
      //     password: this.configService.get('REDIS_PASSWORD'),
      //     db: this.configService.get('REDIS_DB', 1), // Use different DB for cache
      //   },
      //   duration: 30000, // 30 seconds default cache
      // },

      // Query optimization
      maxQueryExecutionTime: 30000, // 30s max query time

      // Migration settings
      migrationsRun: false, // Don't auto-run migrations
      migrationsTableName: 'typeorm_migrations',

      // Subscriber and entity metadata
      subscribers: [__dirname + '/../**/*.subscriber{.ts,.js}'],
    };
  }
}

// DataSource for migrations
const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  url: process.env.DATABASE_URL || 'postgresql://postgres:postgres123@localhost:5433/retail_media_campaign',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
  synchronize: false,
  logging: process.env.NODE_ENV === 'development',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};

export const AppDataSource = new DataSource(dataSourceOptions);
