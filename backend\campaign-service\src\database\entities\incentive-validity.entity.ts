import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum ValidityType {
  DEFAULT = 'default',
  INDUSTRY_SPECIFIC = 'industry_specific',
  PRODUCT_CATEGORY = 'product_category',
  CAMPAIGN_TYPE = 'campaign_type',
}

export enum ValidityStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
}

@Entity('incentive_validity_configs')
@Index(['industryId', 'validityType', 'status'])
@Index(['validityType', 'status'])
export class IncentiveValidityConfig {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  industryId: string;

  @Column({
    type: 'enum',
    enum: ValidityType,
    default: ValidityType.DEFAULT,
  })
  validityType: ValidityType;

  @Column({ type: 'varchar', length: 255, nullable: true })
  categoryId: string; // For product category specific rules

  @Column({ type: 'varchar', length: 255, nullable: true })
  campaignType: string; // For campaign type specific rules

  @Column({ type: 'int', default: 30 })
  defaultValidityDays: number;

  @Column({ type: 'int', nullable: true })
  minValidityDays: number;

  @Column({ type: 'int', nullable: true })
  maxValidityDays: number;

  @Column({ type: 'varchar', length: 100, default: 'America/Sao_Paulo' })
  timezone: string;

  @Column({ type: 'time', nullable: true })
  defaultStartTime: string; // HH:MM format

  @Column({ type: 'time', nullable: true })
  defaultEndTime: string; // HH:MM format

  @Column({ type: 'boolean', default: true })
  allowWeekends: boolean;

  @Column({ type: 'boolean', default: true })
  allowHolidays: boolean;

  @Column({ type: 'json', nullable: true })
  excludedDates: string[]; // Array of dates in YYYY-MM-DD format

  @Column({ type: 'json', nullable: true })
  businessHours: {
    monday?: { start: string; end: string };
    tuesday?: { start: string; end: string };
    wednesday?: { start: string; end: string };
    thursday?: { start: string; end: string };
    friday?: { start: string; end: string };
    saturday?: { start: string; end: string };
    sunday?: { start: string; end: string };
  };

  @Column({
    type: 'enum',
    enum: ValidityStatus,
    default: ValidityStatus.ACTIVE,
  })
  status: ValidityStatus;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  updatedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  getValidityEndDate(startDate: Date): Date {
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + this.defaultValidityDays);
    return endDate;
  }

  isValidDate(date: Date): boolean {
    const dayOfWeek = date.getDay();
    
    // Check weekends
    if (!this.allowWeekends && (dayOfWeek === 0 || dayOfWeek === 6)) {
      return false;
    }

    // Check excluded dates
    if (this.excludedDates) {
      const dateStr = date.toISOString().split('T')[0];
      if (this.excludedDates.includes(dateStr)) {
        return false;
      }
    }

    return true;
  }

  getBusinessHoursForDay(dayOfWeek: number): { start: string; end: string } | null {
    if (!this.businessHours) {
      return null;
    }

    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayName = days[dayOfWeek];
    
    return this.businessHours[dayName] || null;
  }

  calculateValidityPeriod(startDate: Date): {
    startDate: Date;
    endDate: Date;
    validDays: number;
    excludedDays: string[];
  } {
    const endDate = this.getValidityEndDate(startDate);
    const excludedDays: string[] = [];
    let validDays = 0;

    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      if (this.isValidDate(currentDate)) {
        validDays++;
      } else {
        excludedDays.push(currentDate.toISOString().split('T')[0]);
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return {
      startDate,
      endDate,
      validDays,
      excludedDays,
    };
  }
}
