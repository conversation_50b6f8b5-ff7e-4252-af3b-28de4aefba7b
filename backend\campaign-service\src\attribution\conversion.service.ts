import {
  Injectable,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Conversion, ConversionType, ConversionStatus } from '../database/entities/conversion.entity';
import { AttributionEngineService } from './attribution-engine.service';

@Injectable()
export class ConversionService {
  private readonly logger = new Logger(ConversionService.name);

  constructor(
    @InjectRepository(Conversion)
    private readonly conversionRepository: Repository<Conversion>,
    private readonly attributionEngineService: AttributionEngineService,
  ) {}

  async createConversion(conversionData: {
    userId: string;
    campaignId?: string;
    sessionId?: string;
    orderId?: string;
    type: ConversionType;
    value: number;
    currency?: string;
    quantity?: number;
    productId?: string;
    productName?: string;
    productCategory?: string;
    productBrand?: string;
    productSku?: string;
    conversionUrl?: string;
    referrer?: string;
    source?: string;
    medium?: string;
    campaign?: string;
    ipAddress?: string;
    userAgent?: string;
    customAttributes?: Record<string, any>;
    productDetails?: Record<string, any>;
    notes?: string;
    processAttribution?: boolean;
  }): Promise<Conversion> {
    if (conversionData.value < 0) {
      throw new BadRequestException('Conversion value cannot be negative');
    }

    const conversion = this.conversionRepository.create(
      Conversion.createFromEvent(conversionData)
    );

    const savedConversion = await this.conversionRepository.save(conversion);
    
    this.logger.log(`Created conversion ${savedConversion.id} for user ${conversionData.userId} with value ${savedConversion.getFormattedValue()}`);

    // Process attribution if requested (default: true)
    if (conversionData.processAttribution !== false) {
      try {
        await this.attributionEngineService.processConversion(savedConversion.id);
      } catch (error) {
        this.logger.error(`Error processing attribution for conversion ${savedConversion.id}:`, error);
        // Don't fail the conversion creation if attribution fails
      }
    }

    return savedConversion;
  }

  async updateConversionStatus(
    conversionId: string,
    status: ConversionStatus,
    notes?: string
  ): Promise<Conversion> {
    const conversion = await this.conversionRepository.findOne({
      where: { id: conversionId },
    });

    if (!conversion) {
      throw new BadRequestException('Conversion not found');
    }

    conversion.status = status;
    if (notes) {
      conversion.notes = notes;
    }

    const updatedConversion = await this.conversionRepository.save(conversion);

    // Reprocess attribution if status changed to completed
    if (status === ConversionStatus.COMPLETED && conversion.status !== ConversionStatus.COMPLETED) {
      try {
        await this.attributionEngineService.processConversion(conversionId);
      } catch (error) {
        this.logger.error(`Error reprocessing attribution for conversion ${conversionId}:`, error);
      }
    }

    this.logger.log(`Updated conversion ${conversionId} status to ${status}`);
    return updatedConversion;
  }

  async getConversions(
    userId?: string,
    campaignId?: string,
    startDate?: Date,
    endDate?: Date,
    type?: ConversionType,
    status?: ConversionStatus,
    limit: number = 100,
    offset: number = 0
  ): Promise<{ conversions: Conversion[]; total: number }> {
    const queryBuilder = this.conversionRepository.createQueryBuilder('conversion')
      .orderBy('conversion.timestamp', 'DESC')
      .limit(limit)
      .offset(offset);

    if (userId) {
      queryBuilder.andWhere('conversion.userId = :userId', { userId });
    }

    if (campaignId) {
      queryBuilder.andWhere('conversion.campaignId = :campaignId', { campaignId });
    }

    if (startDate) {
      queryBuilder.andWhere('conversion.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('conversion.timestamp <= :endDate', { endDate });
    }

    if (type) {
      queryBuilder.andWhere('conversion.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('conversion.status = :status', { status });
    }

    const [conversions, total] = await queryBuilder.getManyAndCount();

    return { conversions, total };
  }

  async getConversionAnalytics(
    campaignId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalConversions: number;
    totalValue: number;
    averageValue: number;
    conversionRate: number;
    byType: Record<ConversionType, { count: number; value: number }>;
    byStatus: Record<ConversionStatus, number>;
    topProducts: Array<{ productId: string; productName: string; count: number; value: number }>;
    dailyTrend: Array<{ date: string; conversions: number; value: number }>;
  }> {
    const queryBuilder = this.conversionRepository.createQueryBuilder('conversion');

    if (campaignId) {
      queryBuilder.where('conversion.campaignId = :campaignId', { campaignId });
    }

    if (startDate) {
      queryBuilder.andWhere('conversion.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('conversion.timestamp <= :endDate', { endDate });
    }

    const conversions = await queryBuilder.getMany();

    const totalConversions = conversions.length;
    const totalValue = conversions.reduce((sum, c) => sum + c.getTotalValue(), 0);
    const averageValue = totalConversions > 0 ? totalValue / totalConversions : 0;

    // Group by type
    const byType = conversions.reduce((acc, c) => {
      if (!acc[c.type]) {
        acc[c.type] = { count: 0, value: 0 };
      }
      acc[c.type].count += 1;
      acc[c.type].value += c.getTotalValue();
      return acc;
    }, {} as Record<ConversionType, { count: number; value: number }>);

    // Group by status
    const byStatus = conversions.reduce((acc, c) => {
      acc[c.status] = (acc[c.status] || 0) + 1;
      return acc;
    }, {} as Record<ConversionStatus, number>);

    // Top products
    const productStats = conversions.reduce((acc, c) => {
      if (c.productId && c.productName) {
        const key = c.productId;
        if (!acc[key]) {
          acc[key] = {
            productId: c.productId,
            productName: c.productName,
            count: 0,
            value: 0,
          };
        }
        acc[key].count += c.quantity;
        acc[key].value += c.getTotalValue();
      }
      return acc;
    }, {} as Record<string, { productId: string; productName: string; count: number; value: number }>);

    const topProducts = Object.values(productStats)
      .sort((a, b) => b.value - a.value)
      .slice(0, 10);

    // Daily trend
    const dailyStats = conversions.reduce((acc, c) => {
      const date = c.timestamp.toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = { conversions: 0, value: 0 };
      }
      acc[date].conversions += 1;
      acc[date].value += c.getTotalValue();
      return acc;
    }, {} as Record<string, { conversions: number; value: number }>);

    const dailyTrend = Object.entries(dailyStats)
      .map(([date, stats]) => ({ date, ...stats }))
      .sort((a, b) => a.date.localeCompare(b.date));

    // Calculate conversion rate (would need touchpoint data for accurate calculation)
    // For now, using a mock calculation
    const conversionRate = 0.025; // 2.5% mock conversion rate

    return {
      totalConversions,
      totalValue,
      averageValue,
      conversionRate,
      byType,
      byStatus,
      topProducts,
      dailyTrend,
    };
  }

  async createBulkConversions(
    conversionsData: Array<{
      userId: string;
      campaignId?: string;
      sessionId?: string;
      orderId?: string;
      type: ConversionType;
      timestamp?: Date;
      value: number;
      currency?: string;
      quantity?: number;
      productId?: string;
      productName?: string;
      productCategory?: string;
      productBrand?: string;
      productSku?: string;
      conversionUrl?: string;
      referrer?: string;
      source?: string;
      medium?: string;
      campaign?: string;
      ipAddress?: string;
      userAgent?: string;
      customAttributes?: Record<string, any>;
      productDetails?: Record<string, any>;
      notes?: string;
    }>
  ): Promise<Conversion[]> {
    if (conversionsData.length === 0) {
      throw new BadRequestException('No conversions data provided');
    }

    if (conversionsData.length > 1000) {
      throw new BadRequestException('Maximum 1000 conversions per batch');
    }

    // Validate all conversions
    for (const data of conversionsData) {
      if (data.value < 0) {
        throw new BadRequestException(`Conversion value cannot be negative: ${data.value}`);
      }
    }

    const conversions = conversionsData.map(data => {
      const conversion = Conversion.createFromEvent(data);
      if (data.timestamp) {
        conversion.timestamp = data.timestamp;
      }
      return this.conversionRepository.create(conversion);
    });

    const savedConversions = await this.conversionRepository.save(conversions);
    
    this.logger.log(`Created ${savedConversions.length} conversions in bulk`);

    // Process attribution for all conversions (in background)
    this.processAttributionBatch(savedConversions.map(c => c.id));

    return savedConversions;
  }

  private async processAttributionBatch(conversionIds: string[]): Promise<void> {
    // Process attributions in background without blocking the response
    setImmediate(async () => {
      for (const conversionId of conversionIds) {
        try {
          await this.attributionEngineService.processConversion(conversionId);
        } catch (error) {
          this.logger.error(`Error processing attribution for conversion ${conversionId}:`, error);
        }
      }
    });
  }

  async deleteConversion(conversionId: string): Promise<void> {
    const result = await this.conversionRepository.delete(conversionId);
    
    if (result.affected === 0) {
      throw new BadRequestException('Conversion not found');
    }

    this.logger.log(`Deleted conversion ${conversionId}`);
  }

  async getConversionById(conversionId: string): Promise<Conversion> {
    const conversion = await this.conversionRepository.findOne({
      where: { id: conversionId },
    });

    if (!conversion) {
      throw new BadRequestException('Conversion not found');
    }

    return conversion;
  }
}
