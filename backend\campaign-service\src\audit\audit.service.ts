import {
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';

import { AuditLog, AuditAction, AuditLevel, AuditCategory } from '../database/entities/audit-log.entity';
import { SecurityEvent } from '../database/entities/security-event.entity';
import { DataAccessLog } from '../database/entities/data-access-log.entity';
import { ComplianceEvent } from '../database/entities/compliance-event.entity';

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    @InjectRepository(SecurityEvent)
    private readonly securityEventRepository: Repository<SecurityEvent>,
    @InjectRepository(DataAccessLog)
    private readonly dataAccessLogRepository: Repository<DataAccessLog>,
    @InjectRepository(ComplianceEvent)
    private readonly complianceEventRepository: Repository<ComplianceEvent>,
  ) {}

  async getAuditLogs(
    userId?: string,
    action?: AuditAction,
    category?: AuditCategory,
    level?: AuditLevel,
    entityType?: string,
    entityId?: string,
    startDate?: Date,
    endDate?: Date,
    limit: number = 100,
    offset: number = 0
  ): Promise<{ logs: AuditLog[]; total: number }> {
    const queryBuilder = this.auditLogRepository.createQueryBuilder('audit')
      .orderBy('audit.timestamp', 'DESC')
      .limit(limit)
      .offset(offset);

    if (userId) {
      queryBuilder.andWhere('audit.userId = :userId', { userId });
    }

    if (action) {
      queryBuilder.andWhere('audit.action = :action', { action });
    }

    if (category) {
      queryBuilder.andWhere('audit.category = :category', { category });
    }

    if (level) {
      queryBuilder.andWhere('audit.level = :level', { level });
    }

    if (entityType) {
      queryBuilder.andWhere('audit.entityType = :entityType', { entityType });
    }

    if (entityId) {
      queryBuilder.andWhere('audit.entityId = :entityId', { entityId });
    }

    if (startDate) {
      queryBuilder.andWhere('audit.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('audit.timestamp <= :endDate', { endDate });
    }

    const [logs, total] = await queryBuilder.getManyAndCount();

    return { logs, total };
  }

  async getAuditSummary(
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalEvents: number;
    byAction: Record<AuditAction, number>;
    byCategory: Record<AuditCategory, number>;
    byLevel: Record<AuditLevel, number>;
    topUsers: Array<{ userId: string; count: number }>;
    topEntities: Array<{ entityType: string; count: number }>;
    errorRate: number;
    dailyTrend: Array<{ date: string; count: number; errors: number }>;
  }> {
    const queryBuilder = this.auditLogRepository.createQueryBuilder('audit');

    if (startDate) {
      queryBuilder.andWhere('audit.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('audit.timestamp <= :endDate', { endDate });
    }

    const logs = await queryBuilder.getMany();

    const totalEvents = logs.length;

    // Group by action
    const byAction = logs.reduce((acc, log) => {
      acc[log.action] = (acc[log.action] || 0) + 1;
      return acc;
    }, {} as Record<AuditAction, number>);

    // Group by category
    const byCategory = logs.reduce((acc, log) => {
      acc[log.category] = (acc[log.category] || 0) + 1;
      return acc;
    }, {} as Record<AuditCategory, number>);

    // Group by level
    const byLevel = logs.reduce((acc, log) => {
      acc[log.level] = (acc[log.level] || 0) + 1;
      return acc;
    }, {} as Record<AuditLevel, number>);

    // Top users
    const userCounts = logs.reduce((acc, log) => {
      if (log.userId) {
        acc[log.userId] = (acc[log.userId] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const topUsers = Object.entries(userCounts)
      .map(([userId, count]) => ({ userId, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Top entities
    const entityCounts = logs.reduce((acc, log) => {
      if (log.entityType) {
        acc[log.entityType] = (acc[log.entityType] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const topEntities = Object.entries(entityCounts)
      .map(([entityType, count]) => ({ entityType, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Error rate
    const errorCount = logs.filter(log => !log.isSuccessful || log.level === AuditLevel.ERROR).length;
    const errorRate = totalEvents > 0 ? (errorCount / totalEvents) * 100 : 0;

    // Daily trend
    const dailyStats = logs.reduce((acc, log) => {
      const date = log.timestamp.toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = { count: 0, errors: 0 };
      }
      acc[date].count += 1;
      if (!log.isSuccessful || log.level === AuditLevel.ERROR) {
        acc[date].errors += 1;
      }
      return acc;
    }, {} as Record<string, { count: number; errors: number }>);

    const dailyTrend = Object.entries(dailyStats)
      .map(([date, stats]) => ({ date, ...stats }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalEvents,
      byAction,
      byCategory,
      byLevel,
      topUsers,
      topEntities,
      errorRate,
      dailyTrend,
    };
  }

  async getSecurityEvents(
    startDate?: Date,
    endDate?: Date,
    severity?: string,
    status?: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<{ events: SecurityEvent[]; total: number }> {
    const queryBuilder = this.securityEventRepository.createQueryBuilder('security')
      .orderBy('security.timestamp', 'DESC')
      .limit(limit)
      .offset(offset);

    if (startDate) {
      queryBuilder.andWhere('security.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('security.timestamp <= :endDate', { endDate });
    }

    if (severity) {
      queryBuilder.andWhere('security.severity = :severity', { severity });
    }

    if (status) {
      queryBuilder.andWhere('security.status = :status', { status });
    }

    const [events, total] = await queryBuilder.getManyAndCount();

    return { events, total };
  }

  async getDataAccessLogs(
    userId?: string,
    dataType?: string,
    accessType?: string,
    sensitivity?: string,
    startDate?: Date,
    endDate?: Date,
    limit: number = 100,
    offset: number = 0
  ): Promise<{ logs: DataAccessLog[]; total: number }> {
    const queryBuilder = this.dataAccessLogRepository.createQueryBuilder('access')
      .orderBy('access.timestamp', 'DESC')
      .limit(limit)
      .offset(offset);

    if (userId) {
      queryBuilder.andWhere('access.userId = :userId', { userId });
    }

    if (dataType) {
      queryBuilder.andWhere('access.dataType = :dataType', { dataType });
    }

    if (accessType) {
      queryBuilder.andWhere('access.accessType = :accessType', { accessType });
    }

    if (sensitivity) {
      queryBuilder.andWhere('access.sensitivity = :sensitivity', { sensitivity });
    }

    if (startDate) {
      queryBuilder.andWhere('access.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('access.timestamp <= :endDate', { endDate });
    }

    const [logs, total] = await queryBuilder.getManyAndCount();

    return { logs, total };
  }

  async getComplianceEvents(
    type?: string,
    eventType?: string,
    status?: string,
    dataSubjectId?: string,
    startDate?: Date,
    endDate?: Date,
    limit: number = 100,
    offset: number = 0
  ): Promise<{ events: ComplianceEvent[]; total: number }> {
    const queryBuilder = this.complianceEventRepository.createQueryBuilder('compliance')
      .orderBy('compliance.timestamp', 'DESC')
      .limit(limit)
      .offset(offset);

    if (type) {
      queryBuilder.andWhere('compliance.type = :type', { type });
    }

    if (eventType) {
      queryBuilder.andWhere('compliance.eventType = :eventType', { eventType });
    }

    if (status) {
      queryBuilder.andWhere('compliance.status = :status', { status });
    }

    if (dataSubjectId) {
      queryBuilder.andWhere('compliance.dataSubjectId = :dataSubjectId', { dataSubjectId });
    }

    if (startDate) {
      queryBuilder.andWhere('compliance.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('compliance.timestamp <= :endDate', { endDate });
    }

    const [events, total] = await queryBuilder.getManyAndCount();

    return { events, total };
  }

  async getComplianceReport(
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalEvents: number;
    complianceRate: number;
    byType: Record<string, { total: number; compliant: number; rate: number }>;
    overdueRequests: number;
    breachEvents: number;
    consentWithdrawals: number;
    dataSubjectRequests: number;
    riskEvents: ComplianceEvent[];
  }> {
    const queryBuilder = this.complianceEventRepository.createQueryBuilder('compliance');

    if (startDate) {
      queryBuilder.andWhere('compliance.timestamp >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('compliance.timestamp <= :endDate', { endDate });
    }

    const events = await queryBuilder.getMany();

    const totalEvents = events.length;
    const compliantEvents = events.filter(e => e.status === 'compliant').length;
    const complianceRate = totalEvents > 0 ? (compliantEvents / totalEvents) * 100 : 100;

    // Group by type
    const byType = events.reduce((acc, event) => {
      const type = event.type;
      if (!acc[type]) {
        acc[type] = { total: 0, compliant: 0, rate: 0 };
      }
      acc[type].total += 1;
      if (event.status === 'compliant') {
        acc[type].compliant += 1;
      }
      return acc;
    }, {} as Record<string, { total: number; compliant: number; rate: number }>);

    // Calculate rates
    Object.keys(byType).forEach(type => {
      const stats = byType[type];
      stats.rate = stats.total > 0 ? (stats.compliant / stats.total) * 100 : 100;
    });

    const overdueRequests = events.filter(e => e.isOverdue()).length;
    const breachEvents = events.filter(e => e.eventType === 'data_breach').length;
    const consentWithdrawals = events.filter(e => e.eventType === 'consent_withdrawn').length;
    const dataSubjectRequests = events.filter(e => e.eventType === 'data_subject_request').length;

    const riskEvents = events
      .filter(e => e.isHighRisk())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10);

    return {
      totalEvents,
      complianceRate,
      byType,
      overdueRequests,
      breachEvents,
      consentWithdrawals,
      dataSubjectRequests,
      riskEvents,
    };
  }

  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async cleanupOldLogs(): Promise<void> {
    const retentionDays = 365; // Keep logs for 1 year
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);

    try {
      // Clean up audit logs
      const auditResult = await this.auditLogRepository
        .createQueryBuilder()
        .delete()
        .where('timestamp < :cutoffDate', { cutoffDate })
        .andWhere('level != :critical', { critical: AuditLevel.CRITICAL })
        .execute();

      // Clean up security events (keep high/critical severity longer)
      const securityResult = await this.securityEventRepository
        .createQueryBuilder()
        .delete()
        .where('timestamp < :cutoffDate', { cutoffDate })
        .andWhere('severity NOT IN (:...severities)', { severities: ['high', 'critical'] })
        .execute();

      // Clean up data access logs (keep sensitive data access longer)
      const accessResult = await this.dataAccessLogRepository
        .createQueryBuilder()
        .delete()
        .where('timestamp < :cutoffDate', { cutoffDate })
        .andWhere('sensitivity NOT IN (:...sensitivities)', { 
          sensitivities: ['restricted', 'pii', 'financial', 'health', 'legal'] 
        })
        .execute();

      this.logger.log(
        `Cleaned up old logs: ${auditResult.affected || 0} audit logs, ` +
        `${securityResult.affected || 0} security events, ` +
        `${accessResult.affected || 0} access logs`
      );
    } catch (error) {
      this.logger.error('Error cleaning up old logs:', error);
    }
  }

  async exportAuditData(
    startDate: Date,
    endDate: Date,
    format: 'json' | 'csv' = 'json'
  ): Promise<{ data: any; filename: string }> {
    const [auditLogs, securityEvents, dataAccessLogs, complianceEvents] = await Promise.all([
      this.auditLogRepository.find({
        where: {
          timestamp: {
            $gte: startDate,
            $lte: endDate,
          } as any,
        },
        order: { timestamp: 'ASC' },
      }),
      this.securityEventRepository.find({
        where: {
          timestamp: {
            $gte: startDate,
            $lte: endDate,
          } as any,
        },
        order: { timestamp: 'ASC' },
      }),
      this.dataAccessLogRepository.find({
        where: {
          timestamp: {
            $gte: startDate,
            $lte: endDate,
          } as any,
        },
        order: { timestamp: 'ASC' },
      }),
      this.complianceEventRepository.find({
        where: {
          timestamp: {
            $gte: startDate,
            $lte: endDate,
          } as any,
        },
        order: { timestamp: 'ASC' },
      }),
    ]);

    const data = {
      exportInfo: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        exportDate: new Date().toISOString(),
        totalRecords: auditLogs.length + securityEvents.length + dataAccessLogs.length + complianceEvents.length,
      },
      auditLogs,
      securityEvents,
      dataAccessLogs,
      complianceEvents,
    };

    const dateStr = startDate.toISOString().split('T')[0];
    const filename = `audit-export-${dateStr}-${endDate.toISOString().split('T')[0]}.${format}`;

    return { data, filename };
  }
}
