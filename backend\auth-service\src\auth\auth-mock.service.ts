import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { EmailService } from '../email/email.service';

@Injectable()
export class AuthMockService {
  private readonly logger = new Logger(AuthMockService.name);
  private readonly otpCodes = new Map<string, { code: string; expiresAt: Date }>();

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Mock OTP request for industries
   */
  async requestOtpForIndustry(email: string): Promise<{ message: string }> {
    // Validate corporate email
    if (!this.isValidCorporateEmail(email)) {
      throw new Error('E-mail deve ser corporativo (não gmail, hotmail, etc.)');
    }

    // Generate 6-digit OTP
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    // Store OTP
    this.otpCodes.set(email, { code, expiresAt });

    // Send email
    const emailSent = await this.emailService.sendOtpEmail(email, code, 5);

    this.logger.log(`OTP generated for ${email}: ${code} | Email sent: ${emailSent}`);

    return {
      message: 'Código OTP enviado para seu e-mail',
    };
  }

  /**
   * Mock authentication for industries
   */
  async authenticateIndustry(email: string, code: string): Promise<{
    user: any;
    accessToken: string;
    refreshToken: string;
  }> {
    // Check if OTP exists and is valid
    const storedOtp = this.otpCodes.get(email);
    if (!storedOtp) {
      throw new Error('Código OTP não encontrado ou expirado');
    }

    if (storedOtp.expiresAt < new Date()) {
      this.otpCodes.delete(email);
      throw new Error('Código OTP expirado');
    }

    if (storedOtp.code !== code) {
      throw new Error('Código OTP inválido');
    }

    // Remove used OTP
    this.otpCodes.delete(email);

    // Create mock user
    const user = {
      id: '1',
      email: email,
      name: email.split('@')[0].replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      role: 'EDITOR',
      status: 'ACTIVE',
      emailVerified: true,
      twoFactorEnabled: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Generate tokens
    const payload = { sub: user.id, email: user.email, role: user.role };
    const accessToken = this.jwtService.sign(payload);
    const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    this.logger.log(`Mock authentication successful for ${email}`);

    return {
      user,
      accessToken,
      refreshToken,
    };
  }

  private isValidCorporateEmail(email: string): boolean {
    const publicDomains = [
      'gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com',
      'live.com', 'msn.com', 'aol.com', 'icloud.com',
      'protonmail.com', 'mail.com', 'yandex.com'
    ];
    
    const domain = email.split('@')[1]?.toLowerCase();
    return domain && !publicDomains.includes(domain);
  }
}
