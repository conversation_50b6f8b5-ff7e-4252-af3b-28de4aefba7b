'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Search,
  Package,
  CheckCircle,
  AlertTriangle,
  Loader2,
  RefreshCw,
  Sparkles,
  TrendingUp,
  Users,
  DollarSign
} from 'lucide-react';
import toast from 'react-hot-toast';
import { ProductSelectorProps } from './ProductSelector/types';
import { useProductData } from './ProductSelector/hooks/useProductData';
import { useProductFilters } from './ProductSelector/hooks/useProductFilters';
import { useAIEstimation } from './ProductSelector/hooks/useAIEstimation';



export default function ProductSelector({
  industryId,
  selectedProducts,
  onProductsChange,
  maxProducts = 100,
  incentiveValue = 0
}: ProductSelectorProps) {
  const { products, isLoading, error, retryLoad } = useProductData();
  const { filters, filteredProducts, categories, brands, updateFilter } = useProductFilters(products);
  const { estimation, isLoading: isLoadingAI } = useAIEstimation(selectedProducts, incentiveValue);







  const toggleProduct = (product: any) => {
    if (product.isInActiveCampaign) {
      toast.error('Produto já está em campanha ativa');
      return;
    }

    const isSelected = selectedProducts.some(p => p.id === product.id);

    if (isSelected) {
      onProductsChange(selectedProducts.filter(p => p.id !== product.id));
    } else {
      if (selectedProducts.length >= maxProducts) {
        toast.error(`Limite de ${maxProducts} produtos por ciclo excedido`);
        return;
      }
      onProductsChange([...selectedProducts, product]);
    }
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Seleção de Produtos
          </CardTitle>
          <CardDescription>
            Selecione os produtos para sua campanha ({selectedProducts.length}/{maxProducts} selecionados)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar produtos, SKU ou marca..."
                value={filters.searchTerm}
                onChange={(e) => updateFilter('searchTerm', e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Category Filter */}
            <select
              value={filters.selectedCategory}
              onChange={(e) => updateFilter('selectedCategory', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Todas as categorias</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>

            {/* Brand Filter */}
            <select
              value={filters.selectedBrand}
              onChange={(e) => updateFilter('selectedBrand', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Todas as marcas</option>
              {brands.map(brand => (
                <option key={brand} value={brand}>{brand}</option>
              ))}
            </select>

            {/* Retry Button */}
            {error && (
              <Button onClick={retryLoad} variant="outline" className="flex items-center">
                <RefreshCw className="h-4 w-4 mr-2" />
                Tentar Novamente
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* AI Estimation */}
      {(estimation || isLoadingAI) && (
        <Card className="border-purple-200 bg-purple-50/30">
          <CardHeader>
            <CardTitle className="flex items-center text-purple-700">
              <Sparkles className="h-5 w-5 mr-2" />
              Estimativa de IA
            </CardTitle>
            <CardDescription>
              Estimativa inteligente de público e custo da campanha
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingAI ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
                <span className="ml-2 text-purple-600">Processando estimativa...</span>
              </div>
            ) : estimation && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-white rounded-lg border">
                    <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-blue-600">
                      {estimation.estimatedAudience.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Consumidores Estimados</div>
                  </div>

                  <div className="text-center p-4 bg-white rounded-lg border">
                    <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-600">
                      R$ {estimation.estimatedCost.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </div>
                    <div className="text-sm text-gray-600">Custo Estimado</div>
                  </div>
                  
                  <div className="text-center p-4 bg-white rounded-lg border">
                    <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-purple-600">
                      {estimation.confidence}%
                    </div>
                    <div className="text-sm text-gray-600">Confiança</div>
                  </div>
                </div>

                {estimation.confidence < 80 && (
                  <div className="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                    <span className="text-yellow-700">
                      Estimativa com baixa confiança - considere adicionar mais produtos ou ajustar o incentivo
                    </span>
                  </div>
                )}

                <div className="text-sm text-gray-600">
                  <strong>Fatores considerados:</strong>
                  <ul className="list-disc list-inside mt-1">
                    {estimation.factors.map((factor, index) => (
                      <li key={index}>{factor}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Products Grid */}
      <Card>
        <CardContent className="p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2">Carregando produtos...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={loadProducts} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Tentar Novamente
              </Button>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">Nenhum produto encontrado</p>
              <p className="text-sm text-gray-500">
                Tente ajustar os filtros ou termo de busca
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredProducts.map((product) => {
                const isSelected = selectedProducts.some(p => p.id === product.id);
                const canSelect = !product.isInActiveCampaign && selectedProducts.length < maxProducts;
                
                return (
                  <div
                    key={product.id}
                    className={`
                      border rounded-lg p-4 cursor-pointer transition-all
                      ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}
                      ${product.isInActiveCampaign ? 'opacity-50 cursor-not-allowed' : ''}
                      ${!canSelect && !isSelected ? 'opacity-50 cursor-not-allowed' : ''}
                    `}
                    onClick={() => (canSelect || isSelected) && toggleProduct(product)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{product.name}</h3>
                        <p className="text-sm text-gray-600">{product.brand}</p>
                        <p className="text-xs text-gray-500">SKU: {product.sku}</p>
                      </div>
                      {isSelected && (
                        <CheckCircle className="h-5 w-5 text-blue-600 flex-shrink-0" />
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-bold text-green-600">
                        R$ {product.price.toFixed(2)}
                      </span>
                      <span className="text-xs text-gray-500">{product.category}</span>
                    </div>
                    
                    {product.isInActiveCampaign && (
                      <div className="mt-2 text-xs text-orange-600 flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Produto já em campanha ativa
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
