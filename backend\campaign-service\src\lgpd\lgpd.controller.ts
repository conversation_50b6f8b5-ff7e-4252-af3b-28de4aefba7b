import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsArray, IsBoolean, IsDateString } from 'class-validator';

import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

import { LgpdService } from './lgpd.service';
import { DataAnonymizationService } from './data-anonymization.service';

import { ConsentPurpose, ConsentMethod } from '../database/entities/lgpd-consent.entity';
import { RequestType } from '../database/entities/data-subject-request.entity';

// DTOs
class CreateConsentDto {
  @IsString()
  dataSubjectId: string;

  @IsEnum(ConsentPurpose)
  purpose: ConsentPurpose;

  @IsEnum(ConsentMethod)
  method: ConsentMethod;

  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  legalBasis?: string;

  @IsOptional()
  @IsArray()
  dataCategories?: string[];

  @IsOptional()
  @IsArray()
  processingPurposes?: string[];

  @IsOptional()
  @IsArray()
  recipients?: string[];

  @IsOptional()
  @IsString()
  retentionPeriod?: string;

  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @IsOptional()
  @IsString()
  source?: string;

  @IsOptional()
  @IsString()
  ipAddress?: string;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsBoolean()
  isMinor?: boolean;

  @IsOptional()
  @IsString()
  guardianId?: string;

  @IsOptional()
  @IsString()
  version?: string;
}

class CreateDataSubjectRequestDto {
  @IsString()
  dataSubjectId: string;

  @IsEnum(RequestType)
  type: RequestType;

  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  subject?: string;

  @IsOptional()
  @IsArray()
  requestedData?: string[];

  @IsOptional()
  @IsArray()
  dataCategories?: string[];

  @IsOptional()
  @IsString()
  ipAddress?: string;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsString()
  source?: string;
}

class ProcessRequestDto {
  @IsString()
  response: string;

  @IsString()
  responseMethod: string;

  @IsString()
  processedBy: string;

  @IsOptional()
  responseData?: Record<string, any>;
}

class WithdrawConsentDto {
  @IsOptional()
  @IsString()
  withdrawnBy?: string;

  @IsOptional()
  @IsString()
  reason?: string;
}

class AnonymizeDataDto {
  @IsString()
  dataType: string;

  @IsString()
  entityId: string;

  data: Record<string, any>;
}

@ApiTags('LGPD Compliance')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('api/v1/lgpd')
export class LgpdController {
  constructor(
    private readonly lgpdService: LgpdService,
    private readonly anonymizationService: DataAnonymizationService,
  ) {}

  // Consent Management
  @Post('consents')
  @ApiOperation({ summary: 'Create consent record' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Consent created successfully' })
  async createConsent(@Body(ValidationPipe) createConsentDto: CreateConsentDto) {
    return this.lgpdService.createConsent({
      ...createConsentDto,
      expiresAt: createConsentDto.expiresAt ? new Date(createConsentDto.expiresAt) : undefined,
    });
  }

  @Get('consents')
  @Roles('admin', 'manager', 'dpo')
  @ApiOperation({ summary: 'Get consent records' })
  @ApiQuery({ name: 'dataSubjectId', required: false })
  @ApiQuery({ name: 'purpose', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'offset', required: false })
  async getConsents(
    @Query('dataSubjectId') dataSubjectId?: string,
    @Query('purpose') purpose?: ConsentPurpose,
    @Query('status') status?: string,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit?: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset?: number,
  ) {
    return this.lgpdService.getConsents(dataSubjectId, purpose, status as any, limit, offset);
  }

  @Put('consents/:id/withdraw')
  @ApiOperation({ summary: 'Withdraw consent' })
  async withdrawConsent(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) withdrawDto: WithdrawConsentDto,
  ) {
    return this.lgpdService.withdrawConsent(id, withdrawDto.withdrawnBy, withdrawDto.reason);
  }

  // Data Subject Rights
  @Post('requests')
  @ApiOperation({ summary: 'Create data subject request' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Request created successfully' })
  async createDataSubjectRequest(@Body(ValidationPipe) createRequestDto: CreateDataSubjectRequestDto) {
    return this.lgpdService.createDataSubjectRequest(createRequestDto);
  }

  @Get('requests')
  @Roles('admin', 'manager', 'dpo')
  @ApiOperation({ summary: 'Get data subject requests' })
  @ApiQuery({ name: 'dataSubjectId', required: false })
  @ApiQuery({ name: 'type', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'offset', required: false })
  async getDataSubjectRequests(
    @Query('dataSubjectId') dataSubjectId?: string,
    @Query('type') type?: RequestType,
    @Query('status') status?: string,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit?: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset?: number,
  ) {
    // This would typically call a method to get requests
    // For now, return mock data
    return {
      requests: [],
      total: 0,
    };
  }

  @Put('requests/:id/process')
  @Roles('admin', 'manager', 'dpo')
  @ApiOperation({ summary: 'Process data subject request' })
  async processDataSubjectRequest(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) processDto: ProcessRequestDto,
  ) {
    return this.lgpdService.processDataSubjectRequest(
      id,
      processDto.response,
      processDto.responseMethod,
      processDto.processedBy,
      processDto.responseData,
    );
  }

  // Data Anonymization
  @Post('anonymize')
  @Roles('admin', 'dpo')
  @ApiOperation({ summary: 'Anonymize data' })
  async anonymizeData(@Body(ValidationPipe) anonymizeDto: AnonymizeDataDto) {
    const rules = await this.anonymizationService.getAnonymizationRules(anonymizeDto.dataType);
    return this.anonymizationService.anonymizeData(anonymizeDto.data, rules);
  }

  @Get('anonymization/rules/:dataType')
  @Roles('admin', 'dpo')
  @ApiOperation({ summary: 'Get anonymization rules for data type' })
  async getAnonymizationRules(@Param('dataType') dataType: string) {
    return this.anonymizationService.getAnonymizationRules(dataType);
  }

  @Get('anonymization/report')
  @Roles('admin', 'dpo')
  @ApiOperation({ summary: 'Get anonymization report' })
  @ApiQuery({ name: 'startDate', required: true })
  @ApiQuery({ name: 'endDate', required: true })
  async getAnonymizationReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.anonymizationService.generateAnonymizationReport(
      new Date(startDate),
      new Date(endDate),
    );
  }

  // Compliance Monitoring
  @Get('compliance/status')
  @Roles('admin', 'manager', 'dpo')
  @ApiOperation({ summary: 'Get LGPD compliance status' })
  async getComplianceStatus() {
    return this.lgpdService.getComplianceStatus();
  }

  @Get('compliance/report')
  @Roles('admin', 'dpo')
  @ApiOperation({ summary: 'Generate LGPD compliance report' })
  @ApiQuery({ name: 'startDate', required: true })
  @ApiQuery({ name: 'endDate', required: true })
  async generateComplianceReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.lgpdService.generateLgpdReport(
      new Date(startDate),
      new Date(endDate),
    );
  }

  // Health Check
  @Get('health')
  @ApiOperation({ summary: 'Check LGPD system health' })
  async checkHealth() {
    try {
      const status = await this.lgpdService.getComplianceStatus();
      
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        complianceScore: status.complianceScore,
        riskLevel: status.riskLevel,
        message: 'LGPD compliance system is operational',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
        message: 'LGPD compliance system has issues',
      };
    }
  }

  // Data Subject Portal (simplified endpoints for data subjects)
  @Get('my-data/:dataSubjectId')
  @ApiOperation({ summary: 'Get data subject information (for data subject portal)' })
  async getMyData(@Param('dataSubjectId') dataSubjectId: string) {
    const { consents } = await this.lgpdService.getConsents(dataSubjectId);
    
    return {
      dataSubjectId,
      consents: consents.map(consent => ({
        id: consent.id,
        purpose: consent.getPurposeLabel(),
        status: consent.getStatusLabel(),
        createdAt: consent.createdAt,
        expiresAt: consent.expiresAt,
        isActive: consent.isActive(),
        canWithdraw: consent.status === 'given',
      })),
      rights: [
        'Acesso aos seus dados pessoais',
        'Correção de dados incorretos',
        'Eliminação de dados desnecessários',
        'Portabilidade de dados',
        'Revogação do consentimento',
        'Informações sobre o tratamento',
      ],
    };
  }

  @Post('my-data/:dataSubjectId/request')
  @ApiOperation({ summary: 'Submit data subject request (for data subject portal)' })
  async submitMyRequest(
    @Param('dataSubjectId') dataSubjectId: string,
    @Body() requestData: { type: RequestType; description: string; requestedData?: string[] },
  ) {
    return this.lgpdService.createDataSubjectRequest({
      dataSubjectId,
      type: requestData.type,
      description: requestData.description,
      requestedData: requestData.requestedData,
      source: 'data_subject_portal',
    });
  }

  @Put('my-data/:dataSubjectId/consents/:consentId/withdraw')
  @ApiOperation({ summary: 'Withdraw consent (for data subject portal)' })
  async withdrawMyConsent(
    @Param('dataSubjectId') dataSubjectId: string,
    @Param('consentId', ParseUUIDPipe) consentId: string,
    @Body() withdrawData: { reason?: string },
  ) {
    return this.lgpdService.withdrawConsent(consentId, dataSubjectId, withdrawData.reason);
  }
}
