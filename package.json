{"name": "retail-media-system", "version": "1.0.0", "description": "Sistema completo de gestão de campanhas de incentivo", "private": true, "scripts": {"install:all": "npm run install:backend && npm run install:frontend", "install:frontend": "cd frontend-nextjs && npm install", "install:backend": "cd backend/auth-service && npm install && cd ../campaign-service && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend-nextjs && npm run dev", "dev:backend": "concurrently \"npm run dev:auth\" \"npm run dev:campaign\"", "dev:auth": "cd backend/auth-service && npm run start:dev", "dev:campaign": "cd backend/campaign-service && npm run start:dev", "build": "npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend-nextjs && npm run build", "build:backend": "npm run build:auth && npm run build:campaign", "build:auth": "cd backend/auth-service && npm run build", "build:campaign": "cd backend/campaign-service && npm run build", "test": "npm run test:backend", "test:backend": "npm run test:auth && npm run test:campaign", "test:auth": "cd backend/auth-service && npm run test", "test:campaign": "cd backend/campaign-service && npm run test", "test:unit": "npm run test:backend -- --testPathPattern=unit", "test:integration": "npm run test:backend -- --testPathPattern=integration", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend-nextjs && npm run lint", "lint:backend": "npm run lint:auth && npm run lint:campaign", "lint:auth": "cd backend/auth-service && npm run lint", "lint:campaign": "cd backend/campaign-service && npm run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:build": "docker-compose build", "health:check": "node infrastructure/monitoring/health-check.js"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/company/retail-media-system.git"}, "keywords": ["retail", "media", "campaigns", "incentives", "<PERSON><PERSON><PERSON>", "nextjs", "typescript", "microservices"], "author": "Retail Media Team", "license": "MIT"}