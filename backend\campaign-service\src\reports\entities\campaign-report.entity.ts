import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ENDED = 'ended',
  REACTIVATED = 'reactivated'
}

export enum IncentiveType {
  PERCENTAGE = 'percentage',
  AMOUNT_BRL = 'amount_brl'
}

@Entity('campaigns')
export class CampaignReport {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'uuid' })
  industryId: string;

  @Column({ type: 'uuid' })
  createdBy: string;

  @Column({
    type: 'enum',
    enum: CampaignStatus,
    default: CampaignStatus.DRAFT,
  })
  status: CampaignStatus;

  @Column({
    type: 'enum',
    enum: IncentiveType,
    default: IncentiveType.PERCENTAGE,
  })
  incentiveType: IncentiveType;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  incentiveValue: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxIncentiveValue: number;

  @Column({ type: 'timestamp with time zone' })
  startDate: Date;

  @Column({ type: 'timestamp with time zone' })
  endDate: Date;

  @Column({ type: 'decimal', precision: 12, scale: 2, default: 0 })
  budget: number;

  @Column({ type: 'decimal', precision: 12, scale: 2, default: 0 })
  totalInvestment: number;

  @Column({ type: 'decimal', precision: 12, scale: 2, default: 0 })
  totalRevenue: number;

  @Column({ type: 'integer', default: 0 })
  impactedConsumers: number;

  @Column({ type: 'integer', default: 0 })
  convertedConsumers: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  conversionRate: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  roi: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  cycleId: string;

  @Column({ type: 'timestamp', nullable: true })
  nextTransitionAt: Date;

  @Column({ type: 'varchar', length: 50, nullable: true })
  nextTransitionAction: string;

  @Column({ type: 'jsonb', nullable: true })
  aiEstimation: any;

  @Column({ type: 'jsonb', nullable: true })
  metadata: any;

  @Column({ type: 'timestamp', nullable: true })
  publishedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  pausedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  endedAt: Date;

  @Column({ type: 'uuid', nullable: true })
  pausedBy: string;

  @Column({ type: 'uuid', nullable: true })
  endedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
