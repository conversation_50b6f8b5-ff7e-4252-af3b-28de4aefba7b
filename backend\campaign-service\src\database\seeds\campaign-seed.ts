import { DataSource } from 'typeorm';

export class CampaignSeed {
  static async run(dataSource: DataSource): Promise<void> {
    // Check if campaigns already exist
    const result = await dataSource.query('SELECT COUNT(*) as count FROM campaigns');
    const existingCampaigns = parseInt(result[0].count);

    if (existingCampaigns > 0) {
      console.log('Campaigns already exist, skipping seed...');
      return;
    }

    console.log('Inserting campaign seed data...');

    // Insert campaigns using raw SQL
    await dataSource.query(`
      INSERT INTO campaigns (
        name, description, "industryId", "createdBy", status, "incentiveType",
        "incentiveValue", "maxIncentiveValue", "startDate", "endDate", budget,
        "totalInvestment", "totalRevenue", "impactedConsumers", "convertedConsumers",
        "conversionRate", roi, metadata
      ) VALUES
      (
        'Campanha Black Friday 2024',
        'Mega promoção de Black Friday com descontos especiais em eletrônicos',
        '123e4567-e89b-12d3-a456-426614174000',
        '123e4567-e89b-12d3-a456-426614174001',
        'active',
        'percentage',
        25.00,
        500.00,
        '2024-11-25 00:00:00+00',
        '2024-11-30 23:59:59+00',
        50000.00,
        35000.00,
        125000.00,
        2500,
        875,
        35.00,
        257.14,
        '{"category":"Eletrônicos","targetAudience":"Geral","priority":"Alta"}'
      ),
      (
        'Promoção Eletrônicos Verão',
        'Campanha especial de eletrônicos para o verão 2024',
        '123e4567-e89b-12d3-a456-426614174000',
        '123e4567-e89b-12d3-a456-426614174001',
        'active',
        'percentage',
        15.00,
        300.00,
        '2024-12-01 00:00:00+00',
        '2025-02-28 23:59:59+00',
        30000.00,
        22000.00,
        85000.00,
        1800,
        540,
        30.00,
        286.36,
        '{"category":"Eletrônicos","targetAudience":"Jovens","priority":"Média"}'
      ),
      (
        'Volta às Aulas 2024',
        'Campanha de volta às aulas com produtos educacionais e tecnológicos',
        '123e4567-e89b-12d3-a456-426614174000',
        '123e4567-e89b-12d3-a456-426614174001',
        'ended',
        'percentage',
        20.00,
        200.00,
        '2024-01-15 00:00:00+00',
        '2024-02-15 23:59:59+00',
        25000.00,
        24500.00,
        78000.00,
        1500,
        420,
        28.00,
        218.37,
        '{"category":"Educação","targetAudience":"Estudantes","priority":"Alta"}'
      ),
      (
        'Campanha Natal 2024',
        'Promoção especial de Natal com presentes e decorações',
        '123e4567-e89b-12d3-a456-426614174000',
        '123e4567-e89b-12d3-a456-426614174001',
        'paused',
        'percentage',
        30.00,
        400.00,
        '2024-12-01 00:00:00+00',
        '2024-12-25 23:59:59+00',
        40000.00,
        15000.00,
        45000.00,
        1200,
        300,
        25.00,
        200.00,
        '{"category":"Presentes","targetAudience":"Famílias","priority":"Alta"}'
      ),
      (
        'Promoção Verão 2025',
        'Campanha de verão com produtos de praia e lazer',
        '123e4567-e89b-12d3-a456-426614174000',
        '123e4567-e89b-12d3-a456-426614174001',
        'draft',
        'percentage',
        18.00,
        250.00,
        '2025-01-01 00:00:00+00',
        '2025-03-31 23:59:59+00',
        35000.00,
        0.00,
        0.00,
        0,
        0,
        0.00,
        0.00,
        '{"category":"Lazer","targetAudience":"Jovens Adultos","priority":"Média"}'
      );
    `);

    console.log('Campaign seed completed successfully!');
  }
}
