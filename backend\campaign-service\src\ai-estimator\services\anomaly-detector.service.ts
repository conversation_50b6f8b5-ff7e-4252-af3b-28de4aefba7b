import { Injectable } from '@nestjs/common';
import { AnomalyDetectionResult } from '../domain/estimation-result';
import { ProductForEstimation } from '../domain/product';

/**
 * Service responsible for detecting anomalies in campaign estimations
 * Follows Single Responsibility Principle
 */
@Injectable()
export class AnomalyDetectorService {
  private readonly ANOMALY_COST_MULTIPLIER = 10;
  private readonly HIGH_REVENUE_THRESHOLD = 100000;
  private readonly NORMAL_COST_PERCENTAGE = 0.1;

  /**
   * Detect anomalies in estimation results
   * @param estimatedCost - The estimated campaign cost
   * @param estimatedRevenue - The estimated revenue
   * @param products - Products being analyzed
   * @returns Anomaly detection result
   */
  detectAnomalies(
    estimatedCost: number,
    estimatedRevenue: number,
    products: ProductForEstimation[]
  ): AnomalyDetectionResult {
    const warnings: string[] = [];
    let isAnomalous = false;

    // Check for cost anomalies
    const costAnomalyResult = this.checkCostAnomalies(estimatedCost, estimatedRevenue, products);
    if (costAnomalyResult.isAnomalous) {
      isAnomalous = true;
    }
    warnings.push(...costAnomalyResult.warnings);

    // Check for revenue anomalies
    const revenueWarnings = this.checkRevenueAnomalies(estimatedRevenue, products);
    warnings.push(...revenueWarnings);

    // Check for profitability issues
    const profitabilityWarnings = this.checkProfitability(estimatedCost, estimatedRevenue);
    warnings.push(...profitabilityWarnings);

    return isAnomalous 
      ? AnomalyDetectionResult.anomalous(warnings)
      : new AnomalyDetectionResult(false, warnings);
  }

  private checkCostAnomalies(
    estimatedCost: number,
    estimatedRevenue: number,
    products: ProductForEstimation[]
  ): AnomalyDetectionResult {
    const averagePrice = this.calculateAveragePrice(products);
    const normalCostPerConversion = averagePrice * this.NORMAL_COST_PERCENTAGE;
    const estimatedConversions = estimatedRevenue / averagePrice;
    const costPerConversion = estimatedCost / estimatedConversions;

    if (costPerConversion > normalCostPerConversion * this.ANOMALY_COST_MULTIPLIER) {
      return AnomalyDetectionResult.anomalous([
        'Custo por conversão excepcionalmente alto detectado'
      ]);
    }

    return AnomalyDetectionResult.normal();
  }

  private checkRevenueAnomalies(
    estimatedRevenue: number,
    products: ProductForEstimation[]
  ): string[] {
    const warnings: string[] = [];
    const averagePrice = this.calculateAveragePrice(products);
    const estimatedUnits = estimatedRevenue / averagePrice;

    if (estimatedRevenue > this.HIGH_REVENUE_THRESHOLD) {
      warnings.push('Receita estimada excepcionalmente alta - verificar parâmetros');
    }

    if (estimatedUnits > 100000) {
      warnings.push('Volume de vendas estimado muito alto para o período');
    }

    return warnings;
  }

  private checkProfitability(estimatedCost: number, estimatedRevenue: number): string[] {
    const warnings: string[] = [];

    if (estimatedCost >= estimatedRevenue) {
      warnings.push('Campanha não rentável - custo maior ou igual à receita');
    }

    const roi = ((estimatedRevenue - estimatedCost) / estimatedCost) * 100;
    if (roi < 10) {
      warnings.push('ROI baixo - considere ajustar parâmetros da campanha');
    }

    return warnings;
  }

  private calculateAveragePrice(products: ProductForEstimation[]): number {
    if (products.length === 0) return 0;
    
    const totalPrice = products.reduce((sum, product) => sum + product.price, 0);
    return totalPrice / products.length;
  }
}
