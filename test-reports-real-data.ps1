$body = @{} | ConvertTo-Json

try {
    Write-Host "Testing reports API with real PostgreSQL data..." -ForegroundColor Cyan
    $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/v1/reports/campaign-performance' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing
    Write-Host "Success! Status: $($response.StatusCode)" -ForegroundColor Green
    
    $data = $response.Content | ConvertFrom-Json
    Write-Host "`nSummary:" -ForegroundColor Yellow
    Write-Host "  Total Campaigns: $($data.summary.totalCampaigns)"
    Write-Host "  Active Campaigns: $($data.summary.activeCampaigns)"
    Write-Host "  Total Revenue: $($data.summary.totalRevenue)"
    Write-Host "  Average ROI: $($data.summary.averageROI)"
    
    Write-Host "`nCampaigns:" -ForegroundColor Yellow
    foreach ($campaign in $data.campaigns) {
        Write-Host "  - $($campaign.name) [$($campaign.status)] - ROI: $($campaign.roi)%"
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
    }
}

