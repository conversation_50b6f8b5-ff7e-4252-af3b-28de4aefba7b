import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../database/entities/product.entity';
import { ProductCategory } from '../database/entities/product-category.entity';
import { ValidationService } from '../validation/validation.service';
import { IncentiveValidityService } from '../incentive-validity/incentive-validity.service';
import { IsString, IsNumber, IsOptional, IsIn, Min, Max } from 'class-validator';

export class IncentiveCalculationInput {
  @IsString()
  productId: string;

  @IsString()
  @IsIn(['percentage', 'fixed_value'])
  incentiveType: 'percentage' | 'fixed_value';

  @IsNumber()
  @Min(0.01)
  @Max(100)
  incentiveValue: number; // Percentage (0-100) or fixed value in currency

  @IsOptional()
  @IsNumber()
  @Min(1)
  quantity?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  validityDays?: number;

  @IsString()
  industryId: string;

  @IsOptional()
  @IsString()
  campaignId?: string;

  @IsOptional()
  @IsString()
  cycleId?: string;
}

export interface IncentiveCalculationResult {
  productId: string;
  productName: string;
  productPrice: number;
  incentiveType: 'percentage' | 'fixed_value';
  incentiveValue: number;
  calculatedIncentive: number;
  finalPrice: number;
  savings: number;
  savingsPercentage: number;
  quantity: number;
  totalIncentive: number;
  totalSavings: number;
  validityPeriod: {
    startDate: Date;
    endDate: Date;
    validityDays: number;
  };
  isValid: boolean;
  errors: string[];
  warnings: string[];
  metadata: {
    calculatedAt: Date;
    maxIncentiveAllowed: number;
    categoryLimits?: {
      maxPercentage?: number;
      maxValue?: number;
    };
    productLimits?: {
      maxPercentage?: number;
      maxValue?: number;
    };
  };
}

export class BulkIncentiveCalculationInput {
  products: IncentiveCalculationInput[];
  globalSettings?: {
    validityDays?: number;
    industryId: string;
    campaignId?: string;
    cycleId?: string;
  };
}

export interface BulkIncentiveCalculationResult {
  results: IncentiveCalculationResult[];
  summary: {
    totalProducts: number;
    validCalculations: number;
    invalidCalculations: number;
    totalIncentiveValue: number;
    totalSavings: number;
    averageIncentivePercentage: number;
    errors: string[];
    warnings: string[];
  };
  performance: {
    calculationTime: number;
    validationTime: number;
    totalTime: number;
  };
}

@Injectable()
export class IncentiveCalculatorService {
  private readonly logger = new Logger(IncentiveCalculatorService.name);

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(ProductCategory)
    private readonly categoryRepository: Repository<ProductCategory>,
    private readonly validationService: ValidationService,
    private readonly incentiveValidityService: IncentiveValidityService,
  ) {}

  async calculateIncentive(input: IncentiveCalculationInput): Promise<IncentiveCalculationResult> {
    const startTime = Date.now();

    try {
      // Get product details
      const product = await this.productRepository.findOne({ 
        where: { id: input.productId } 
      });

      if (!product) {
        throw new BadRequestException('Produto não encontrado');
      }

      // Get category details for limits
      const category = await this.categoryRepository.findOne({
        where: { id: product.categoryId }
      });

      // Validate product eligibility
      const productValidation = product.validateIncentive(
        input.incentiveType === 'percentage' ? input.incentiveValue : undefined,
        input.incentiveType === 'fixed_value' ? input.incentiveValue : undefined
      );

      // Validate category limits if category exists
      let categoryValidation = { isValid: true, errors: [] };
      if (category) {
        categoryValidation = category.validateIncentive(
          input.incentiveType === 'percentage' ? input.incentiveValue : undefined,
          input.incentiveType === 'fixed_value' ? input.incentiveValue : undefined
        );
      }

      // Validate business rules
      const ruleValidation = await this.validationService.validateInRealTime({
        industryId: input.industryId,
        incentivePercentage: input.incentiveType === 'percentage' ? input.incentiveValue : undefined,
        incentiveValue: input.incentiveType === 'fixed_value' ? input.incentiveValue : undefined,
        campaignId: input.campaignId,
        cycleId: input.cycleId,
      });

      // Calculate validity period
      const validityDays = input.validityDays || 30;
      const validityPeriod = await this.incentiveValidityService.calculateValidity(
        new Date(),
        input.industryId,
        product.categoryId
      );

      // Perform calculations
      const quantity = input.quantity || 1;
      let calculatedIncentive: number;
      let finalPrice: number;

      if (input.incentiveType === 'percentage') {
        calculatedIncentive = (product.price * input.incentiveValue) / 100;
        finalPrice = product.price - calculatedIncentive;
      } else {
        calculatedIncentive = input.incentiveValue;
        finalPrice = Math.max(0, product.price - calculatedIncentive);
      }

      const savings = calculatedIncentive;
      const savingsPercentage = (savings / product.price) * 100;
      const totalIncentive = calculatedIncentive * quantity;
      const totalSavings = savings * quantity;

      // Collect all errors and warnings
      const errors: string[] = [
        ...productValidation.errors,
        ...categoryValidation.errors,
        ...ruleValidation.errors.map(e => e.message),
      ];

      const warnings: string[] = [
        ...ruleValidation.warnings.map(w => w.message),
        ...validityPeriod.warnings,
      ];

      // Additional validations
      if (finalPrice < 0) {
        errors.push('Incentivo não pode ser maior que o preço do produto');
      }

      if (calculatedIncentive <= 0) {
        errors.push('Valor do incentivo deve ser maior que zero');
      }

      // Determine max incentive allowed
      const maxIncentiveAllowed = this.getMaxIncentiveAllowed(product, category);

      const result: IncentiveCalculationResult = {
        productId: product.id,
        productName: product.name,
        productPrice: product.price,
        incentiveType: input.incentiveType,
        incentiveValue: input.incentiveValue,
        calculatedIncentive,
        finalPrice,
        savings,
        savingsPercentage,
        quantity,
        totalIncentive,
        totalSavings,
        validityPeriod: {
          startDate: validityPeriod.startDate,
          endDate: validityPeriod.endDate,
          validityDays: validityPeriod.validityDays,
        },
        isValid: errors.length === 0,
        errors,
        warnings,
        metadata: {
          calculatedAt: new Date(),
          maxIncentiveAllowed,
          categoryLimits: category ? {
            maxPercentage: category.defaultMaxIncentivePercentage,
            maxValue: category.defaultMaxIncentiveValue,
          } : undefined,
          productLimits: {
            maxPercentage: product.maxIncentivePercentage,
            maxValue: product.maxIncentiveValue,
          },
        },
      };

      this.logger.log(`Incentive calculated for product ${product.id} in ${Date.now() - startTime}ms`);

      return result;
    } catch (error) {
      this.logger.error(`Error calculating incentive for product ${input.productId}:`, error);
      throw error;
    }
  }

  async calculateBulkIncentives(input: BulkIncentiveCalculationInput): Promise<BulkIncentiveCalculationResult> {
    const startTime = Date.now();
    const results: IncentiveCalculationResult[] = [];
    const globalErrors: string[] = [];
    const globalWarnings: string[] = [];

    let validationTime = 0;
    let calculationTime = 0;

    for (const productInput of input.products) {
      try {
        // Apply global settings if not specified
        const enhancedInput: IncentiveCalculationInput = {
          ...productInput,
          validityDays: productInput.validityDays || input.globalSettings?.validityDays,
          industryId: productInput.industryId || input.globalSettings?.industryId,
          campaignId: productInput.campaignId || input.globalSettings?.campaignId,
          cycleId: productInput.cycleId || input.globalSettings?.cycleId,
        };

        const calcStart = Date.now();
        const result = await this.calculateIncentive(enhancedInput);
        calculationTime += Date.now() - calcStart;

        results.push(result);
      } catch (error) {
        globalErrors.push(`Erro no produto ${productInput.productId}: ${error.message}`);
        
        // Add a failed result
        results.push({
          productId: productInput.productId,
          productName: 'Produto não encontrado',
          productPrice: 0,
          incentiveType: productInput.incentiveType,
          incentiveValue: productInput.incentiveValue,
          calculatedIncentive: 0,
          finalPrice: 0,
          savings: 0,
          savingsPercentage: 0,
          quantity: productInput.quantity || 1,
          totalIncentive: 0,
          totalSavings: 0,
          validityPeriod: {
            startDate: new Date(),
            endDate: new Date(),
            validityDays: 0,
          },
          isValid: false,
          errors: [error.message],
          warnings: [],
          metadata: {
            calculatedAt: new Date(),
            maxIncentiveAllowed: 0,
          },
        });
      }
    }

    // Calculate summary
    const validCalculations = results.filter(r => r.isValid).length;
    const invalidCalculations = results.length - validCalculations;
    const totalIncentiveValue = results.reduce((sum, r) => sum + r.totalIncentive, 0);
    const totalSavings = results.reduce((sum, r) => sum + r.totalSavings, 0);
    const averageIncentivePercentage = validCalculations > 0 
      ? results.filter(r => r.isValid).reduce((sum, r) => sum + r.savingsPercentage, 0) / validCalculations
      : 0;

    const totalTime = Date.now() - startTime;

    return {
      results,
      summary: {
        totalProducts: results.length,
        validCalculations,
        invalidCalculations,
        totalIncentiveValue,
        totalSavings,
        averageIncentivePercentage,
        errors: globalErrors,
        warnings: globalWarnings,
      },
      performance: {
        calculationTime,
        validationTime,
        totalTime,
      },
    };
  }

  async estimateIncentiveImpact(
    productIds: string[],
    incentivePercentage: number,
    industryId: string
  ): Promise<{
    totalProducts: number;
    estimatedTotalIncentive: number;
    estimatedTotalSavings: number;
    averageIncentivePerProduct: number;
    productBreakdown: Array<{
      productId: string;
      productName: string;
      currentPrice: number;
      estimatedIncentive: number;
      estimatedFinalPrice: number;
    }>;
  }> {
    const products = await this.productRepository.findByIds(productIds);
    
    let totalIncentive = 0;
    const productBreakdown = [];

    for (const product of products) {
      const incentive = (product.price * incentivePercentage) / 100;
      totalIncentive += incentive;

      productBreakdown.push({
        productId: product.id,
        productName: product.name,
        currentPrice: product.price,
        estimatedIncentive: incentive,
        estimatedFinalPrice: product.price - incentive,
      });
    }

    return {
      totalProducts: products.length,
      estimatedTotalIncentive: totalIncentive,
      estimatedTotalSavings: totalIncentive,
      averageIncentivePerProduct: products.length > 0 ? totalIncentive / products.length : 0,
      productBreakdown,
    };
  }

  private getMaxIncentiveAllowed(product: Product, category?: ProductCategory): number {
    // Priority: Product limit > Category limit > Default (100%)
    if (product.maxIncentivePercentage) {
      return product.maxIncentivePercentage;
    }
    
    if (category?.defaultMaxIncentivePercentage) {
      return category.defaultMaxIncentivePercentage;
    }
    
    return 100; // Default maximum
  }

  async validateIncentiveCalculation(
    productId: string,
    incentiveType: 'percentage' | 'fixed_value',
    incentiveValue: number,
    industryId: string
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[]; maxAllowed: number }> {
    const product = await this.productRepository.findOne({ where: { id: productId } });
    
    if (!product) {
      return {
        isValid: false,
        errors: ['Produto não encontrado'],
        warnings: [],
        maxAllowed: 0,
      };
    }

    const category = await this.categoryRepository.findOne({
      where: { id: product.categoryId }
    });

    const productValidation = product.validateIncentive(
      incentiveType === 'percentage' ? incentiveValue : undefined,
      incentiveType === 'fixed_value' ? incentiveValue : undefined
    );

    const categoryValidation = category ? category.validateIncentive(
      incentiveType === 'percentage' ? incentiveValue : undefined,
      incentiveType === 'fixed_value' ? incentiveValue : undefined
    ) : { isValid: true, errors: [] };

    const ruleValidation = await this.validationService.validateInRealTime({
      industryId,
      incentivePercentage: incentiveType === 'percentage' ? incentiveValue : undefined,
      incentiveValue: incentiveType === 'fixed_value' ? incentiveValue : undefined,
    });

    const errors = [
      ...productValidation.errors,
      ...categoryValidation.errors,
      ...ruleValidation.errors.map(e => e.message),
    ];

    const warnings = ruleValidation.warnings.map(w => w.message);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      maxAllowed: this.getMaxIncentiveAllowed(product, category),
    };
  }
}
