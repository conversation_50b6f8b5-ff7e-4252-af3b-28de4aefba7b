import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Product } from './product.entity';
import { PriceProvider } from './price-provider.entity';
import { PriceType, PriceChangeType } from './price-types.entity';

@Entity('price_history')
@Index(['productId', 'providerId'])
@Index(['productId', 'recordedAt'])
@Index(['changeType'])
@Index(['recordedAt'])
export class PriceHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  @Index()
  productId: string;

  @Column({ type: 'uuid' })
  @Index()
  providerId: string;

  @Column({ type: 'enum', enum: PriceType, default: PriceType.REGULAR })
  priceType: PriceType;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  previousPrice: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  newPrice: number;

  @Column({ type: 'varchar', length: 3, default: 'BRL' })
  currency: string;

  @Column({ type: 'enum', enum: PriceChangeType })
  changeType: PriceChangeType;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  changePercentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  changeAmount: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  reason: string; // Reason for price change

  @Column({ type: 'varchar', length: 255, nullable: true })
  source: string; // Source of the price change (API, manual, etc.)

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  recordedAt: Date;

  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'productId' })
  product: Product;

  @ManyToOne(() => PriceProvider, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'providerId' })
  provider: PriceProvider;

  // Helper methods
  static createFromPriceChange(
    productId: string,
    providerId: string,
    priceType: PriceType,
    previousPrice: number | null,
    newPrice: number,
    currency: string = 'BRL',
    reason?: string,
    source?: string
  ): Partial<PriceHistory> {
    const changeAmount = previousPrice ? newPrice - previousPrice : null;
    const changePercentage = previousPrice && previousPrice > 0 
      ? ((newPrice - previousPrice) / previousPrice) * 100 
      : null;

    let changeType: PriceChangeType;
    if (!previousPrice) {
      changeType = PriceChangeType.INITIAL;
    } else if (newPrice > previousPrice) {
      changeType = PriceChangeType.INCREASE;
    } else if (newPrice < previousPrice) {
      changeType = PriceChangeType.DECREASE;
    } else {
      changeType = PriceChangeType.CORRECTION;
    }

    return {
      productId,
      providerId,
      priceType,
      previousPrice,
      newPrice,
      currency,
      changeType,
      changeAmount,
      changePercentage,
      reason,
      source,
    };
  }

  getFormattedPreviousPrice(): string | null {
    if (!this.previousPrice) return null;
    
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: this.currency,
    }).format(this.previousPrice);
  }

  getFormattedNewPrice(): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: this.currency,
    }).format(this.newPrice);
  }

  getFormattedChangeAmount(): string | null {
    if (!this.changeAmount) return null;
    
    const sign = this.changeAmount > 0 ? '+' : '';
    return sign + new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: this.currency,
    }).format(this.changeAmount);
  }

  getFormattedChangePercentage(): string | null {
    if (!this.changePercentage) return null;
    
    const sign = this.changePercentage > 0 ? '+' : '';
    return sign + this.changePercentage.toFixed(2) + '%';
  }

  getChangeDescription(): string {
    const changeTypeLabels = {
      [PriceChangeType.INCREASE]: 'Aumento',
      [PriceChangeType.DECREASE]: 'Redução',
      [PriceChangeType.INITIAL]: 'Preço inicial',
      [PriceChangeType.CORRECTION]: 'Correção',
    };

    return changeTypeLabels[this.changeType] || 'Alteração';
  }
}
