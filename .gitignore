# Dependencies
node_modules/
*/node_modules/
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging
.env.prod

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.output/
.vite/

# Coverage reports
coverage/
*.lcov
.nyc_output/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/
.temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Database files
*.sqlite
*.sqlite3
*.db

# Docker
.dockerignore

# Vercel
.vercel/

# Netlify
.netlify/

# AWS
.aws/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
*.kubeconfig

# Monitoring and observability
grafana/data/
prometheus/data/

# Backup files
*.bak
*.backup
*.old

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Test files
test-results/
playwright-report/
test-results.xml

# Security
*.pem
*.key
*.crt
*.p12
*.pfx

# Local development
.local/
local/

# Miscellaneous
*.tgz
*.tar.gz
*.zip
*.rar
*.7z
