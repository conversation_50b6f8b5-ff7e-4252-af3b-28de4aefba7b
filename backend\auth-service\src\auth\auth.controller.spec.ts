import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';

// Mock RabbitMQ service to avoid connection issues in tests
jest.mock('../queue/rabbitmq.service', () => ({
  RabbitMQService: jest.fn().mockImplementation(() => ({
    publishMessage: jest.fn().mockResolvedValue(true),
    onModuleInit: jest.fn(),
    onModuleDestroy: jest.fn(),
    getQueueStats: jest.fn().mockResolvedValue({}),
    healthCheck: jest.fn().mockResolvedValue({ status: 'healthy' }),
  })),
}));

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  const mockAuthService = {
    requestOtp: jest.fn(),
    requestOtpForIndustry: jest.fn(),
    verifyOtp: jest.fn(),
    authenticateIndustry: jest.fn(),
    register: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    changePassword: jest.fn(),
    resetPassword: jest.fn(),
    enableTwoFactor: jest.fn(),
    verifyTwoFactor: jest.fn(),
    getProfile: jest.fn(),
  };

  const mockRequest = {
    ip: '127.0.0.1',
    connection: { remoteAddress: '127.0.0.1' },
    get: (key) => {
        if (key === 'User-Agent') return 'Mozilla/5.0 Test Browser'
    },
    user: {
      sub: '1',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'editor',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).overrideGuard(ThrottlerGuard).useValue({ canActivate: () => true }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('requestOtp', () => {
    const requestOtpDto = { email: '<EMAIL>' };

    it('should request OTP for industry successfully', async () => {
      const expectedResult = { message: 'Código OTP enviado para seu e-mail', expiresIn: 300 };
      mockAuthService.requestOtpForIndustry.mockResolvedValue(expectedResult);

      const result = await controller.requestOtpForIndustry(requestOtpDto, mockRequest);

      expect(result).toEqual(expectedResult);
      expect(mockAuthService.requestOtpForIndustry).toHaveBeenCalledWith(requestOtpDto, mockRequest.ip, 'Mozilla/5.0 Test Browser');
    });

    it('should handle service errors', async () => {
      mockAuthService.requestOtpForIndustry.mockRejectedValue(
        new UnauthorizedException('User not found'),
      );

      await expect(controller.requestOtpForIndustry(requestOtpDto, mockRequest)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('verifyOtp', () => {
    const verifyOtpDto = { email: '<EMAIL>', code: '123456' };

    it('should verify OTP successfully', async () => {
      const expectedResult = {
        user: {
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'editor',
        },
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      };

      mockAuthService.authenticateIndustry.mockResolvedValue(expectedResult);

      const result = await controller.authenticateIndustry(verifyOtpDto, mockRequest);

      expect(result).toEqual(expectedResult);
      expect(mockAuthService.authenticateIndustry).toHaveBeenCalledWith(verifyOtpDto, mockRequest.ip, 'Mozilla/5.0 Test Browser');
    });

    it('should handle invalid OTP', async () => {
      mockAuthService.authenticateIndustry.mockRejectedValue(
        new UnauthorizedException('Invalid OTP code'),
      );

      await expect(controller.authenticateIndustry(verifyOtpDto, mockRequest)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('refreshToken', () => {
    const refreshTokenDto = { refreshToken: 'valid_refresh_token' };

    it('should refresh token successfully', async () => {
      const expectedResult = {
        user: { id: '1' },
        accessToken: 'new_access_token',
        refreshToken: 'new_refresh_token',
      };

      mockAuthService.refreshToken.mockResolvedValue(expectedResult);

      const result = await controller.refreshToken(refreshTokenDto);

      expect(result).toEqual(expectedResult);
      expect(mockAuthService.refreshToken).toHaveBeenCalledWith(refreshTokenDto);
    });

    it('should handle invalid refresh token', async () => {
      mockAuthService.refreshToken.mockRejectedValue(
        new UnauthorizedException('Invalid refresh token'),
      );

      await expect(controller.refreshToken(refreshTokenDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('getProfile', () => {
    it('should return user profile', async () => {
      mockAuthService.getProfile.mockResolvedValue({
        user: mockRequest.user,
      });

      const result = await controller.getProfile(mockRequest);

      expect(result).toEqual({
        user: mockRequest.user,
      });
    });
  });

  describe('enableTwoFactor', () => {
    it('should enable 2FA successfully', async () => {
      const expectedResult = {
        qrCode: 'data:image/png;base64,qrcode',
        secret: 'secret_key',
      };

      mockAuthService.enableTwoFactor.mockResolvedValue(expectedResult);

      const result = await controller.enableTwoFactor(mockRequest);

      expect(result).toEqual(expectedResult);
      expect(mockAuthService.enableTwoFactor).toHaveBeenCalledWith('1');
    });

    it('should handle 2FA already enabled', async () => {
      mockAuthService.enableTwoFactor.mockRejectedValue(
        new BadRequestException('2FA already enabled'),
      );

      await expect(controller.enableTwoFactor(mockRequest)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('verifyTwoFactor', () => {
    const verifyTwoFactorDto = { token: '123456' };

    it('should verify 2FA code successfully', async () => {
      const expectedResult = { message: '2FA ativado com sucesso' };

      mockAuthService.verifyTwoFactor.mockResolvedValue(expectedResult);

      const result = await controller.verifyTwoFactor(verifyTwoFactorDto, mockRequest);

      expect(result).toEqual(expectedResult);
      expect(mockAuthService.verifyTwoFactor).toHaveBeenCalledWith('1', '123456');
    });

    it('should handle invalid 2FA code', async () => {
      mockAuthService.verifyTwoFactor.mockRejectedValue(
        new UnauthorizedException('Invalid 2FA code'),
      );

      await expect(controller.verifyTwoFactor(verifyTwoFactorDto, mockRequest)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
        const expectedResult = { message: 'Logout realizado com sucesso' };
        mockAuthService.logout.mockResolvedValue(expectedResult);
      const result = await controller.logout(mockRequest);

      expect(result).toEqual(expectedResult);
      expect(mockAuthService.logout).toHaveBeenCalledWith('1');
    });
  });
});

