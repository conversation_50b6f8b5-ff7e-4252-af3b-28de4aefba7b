import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum BusinessRuleType {
  INCENTIVE_PERCENTAGE = 'incentive_percentage',
  INCENTIVE_VALUE = 'incentive_value',
  PRODUCTS_PER_CYCLE = 'products_per_cycle',
  CAMPAIGN_DURATION = 'campaign_duration',
  BUDGET_LIMIT = 'budget_limit',
}

export enum BusinessRuleStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
}

@Entity('business_rules')
@Index('IDX_business_rule_type_industry', ['type', 'industryId'], { unique: true })
export class BusinessRule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: BusinessRuleType,
    comment: 'Type of business rule',
  })
  type: BusinessRuleType;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Human-readable name of the rule',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of what this rule does',
  })
  description: string;

  @Column({
    type: 'enum',
    enum: BusinessRuleStatus,
    default: BusinessRuleStatus.ACTIVE,
    comment: 'Current status of the rule',
  })
  @Index()
  status: BusinessRuleStatus;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this rule is currently enabled',
  })
  @Index()
  enabled: boolean;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Industry ID this rule applies to (null = global)',
  })
  industryId: string;

  // Rule configuration values
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: 'Minimum percentage value (for percentage-based rules)',
  })
  minPercentage: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: 'Maximum percentage value (for percentage-based rules)',
  })
  maxPercentage: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Minimum monetary value',
  })
  minValue: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Maximum monetary value',
  })
  maxValue: number;

  @Column({
    type: 'integer',
    nullable: true,
    comment: 'Minimum integer value (for count-based rules)',
  })
  minCount: number;

  @Column({
    type: 'integer',
    nullable: true,
    comment: 'Maximum integer value (for count-based rules)',
  })
  maxCount: number;

  @Column({
    type: 'integer',
    nullable: true,
    comment: 'Duration in days (for time-based rules)',
  })
  durationDays: number;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Additional configuration parameters as JSON',
  })
  configuration: Record<string, any>;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Error message to display when rule is violated',
  })
  errorMessage: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Warning message to display when rule is close to being violated',
  })
  warningMessage: string;

  @Column({
    type: 'integer',
    default: 0,
    comment: 'Priority order for rule evaluation (lower = higher priority)',
  })
  priority: number;

  @Column({
    type: 'uuid',
    comment: 'ID of the user who created this rule',
  })
  createdBy: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'ID of the user who last updated this rule',
  })
  updatedBy: string;

  @CreateDateColumn({
    type: 'timestamp with time zone',
    comment: 'When this rule was created',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp with time zone',
    comment: 'When this rule was last updated',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this rule becomes effective',
  })
  effectiveFrom: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this rule expires',
  })
  effectiveTo: Date;

  // Helper methods
  isActive(): boolean {
    const now = new Date();
    
    if (!this.enabled || this.status !== BusinessRuleStatus.ACTIVE) {
      return false;
    }

    if (this.effectiveFrom && this.effectiveFrom > now) {
      return false;
    }

    if (this.effectiveTo && this.effectiveTo < now) {
      return false;
    }

    return true;
  }

  validateValue(value: number, type: 'percentage' | 'value' | 'count'): boolean {
    switch (type) {
      case 'percentage':
        if (this.minPercentage !== null && value < this.minPercentage) {
          return false;
        }
        if (this.maxPercentage !== null && value > this.maxPercentage) {
          return false;
        }
        break;

      case 'value':
        if (this.minValue !== null && value < this.minValue) {
          return false;
        }
        if (this.maxValue !== null && value > this.maxValue) {
          return false;
        }
        break;

      case 'count':
        if (this.minCount !== null && value < this.minCount) {
          return false;
        }
        if (this.maxCount !== null && value > this.maxCount) {
          return false;
        }
        break;
    }

    return true;
  }

  getViolationMessage(value: number, type: 'percentage' | 'value' | 'count'): string {
    if (this.errorMessage) {
      return this.errorMessage;
    }

    // Generate default error message based on rule type and violated constraint
    switch (type) {
      case 'percentage':
        if (this.minPercentage !== null && value < this.minPercentage) {
          return `Incentivo abaixo do mínimo de ${this.minPercentage}%`;
        }
        if (this.maxPercentage !== null && value > this.maxPercentage) {
          return `Incentivo acima do máximo de ${this.maxPercentage}%`;
        }
        break;

      case 'value':
        if (this.minValue !== null && value < this.minValue) {
          return `Incentivo abaixo do mínimo de R$ ${this.minValue.toFixed(2).replace('.', ',')}`;
        }
        if (this.maxValue !== null && value > this.maxValue) {
          return `Incentivo acima do máximo de R$ ${this.maxValue.toFixed(2).replace('.', ',')}`;
        }
        break;

      case 'count':
        if (this.minCount !== null && value < this.minCount) {
          return `Quantidade abaixo do mínimo de ${this.minCount}`;
        }
        if (this.maxCount !== null && value > this.maxCount) {
          return `Limite de ${this.maxCount} produtos por ciclo excedido`;
        }
        break;
    }

    return 'Regra de negócio violada';
  }

  toJSON() {
    return {
      id: this.id,
      type: this.type,
      name: this.name,
      description: this.description,
      status: this.status,
      enabled: this.enabled,
      industryId: this.industryId,
      minPercentage: this.minPercentage,
      maxPercentage: this.maxPercentage,
      minValue: this.minValue,
      maxValue: this.maxValue,
      minCount: this.minCount,
      maxCount: this.maxCount,
      durationDays: this.durationDays,
      configuration: this.configuration,
      errorMessage: this.errorMessage,
      warningMessage: this.warningMessage,
      priority: this.priority,
      effectiveFrom: this.effectiveFrom,
      effectiveTo: this.effectiveTo,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      isActive: this.isActive(),
    };
  }
}
