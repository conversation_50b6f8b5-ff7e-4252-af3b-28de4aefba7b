import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  Parse<PERSON><PERSON><PERSON>ipe,
  BadRequestException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { PriceIntegrationService } from './price-integration.service';
import { PriceProviderService } from './price-provider.service';
import { PriceCacheService } from './price-cache.service';
import { PriceValidationService } from './price-validation.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { PriceType } from '../database/entities/price-types.entity';

@ApiTags('price-integration')
@Controller('price-integration')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PriceIntegrationController {
  constructor(
    private readonly priceIntegrationService: PriceIntegrationService,
    private readonly priceProviderService: PriceProviderService,
    private readonly priceCacheService: PriceCacheService,
    private readonly priceValidationService: PriceValidationService,
  ) {}

  @Get('products/:productId/price')
  @ApiOperation({ summary: 'Obter preço de um produto' })
  @ApiResponse({ status: 200, description: 'Preço do produto' })
  @ApiResponse({ status: 404, description: 'Produto não encontrado' })
  async getProductPrice(
    @Param('productId', ParseUUIDPipe) productId: string,
    @Query('priceType') priceType?: string,
    @Query('forceRefresh') forceRefresh?: string,
  ) {
    const type = priceType as PriceType || PriceType.REGULAR;
    const refresh = forceRefresh === 'true';

    const price = await this.priceIntegrationService.getProductPrice(productId, type, refresh);
    
    if (!price) {
      return { 
        productId,
        priceType: type,
        price: null,
        message: 'Preço não disponível'
      };
    }

    return {
      productId,
      priceType: type,
      ...price,
    };
  }

  @Post('products/:productId/price')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Atualizar preço de um produto manualmente' })
  @ApiResponse({ status: 200, description: 'Preço atualizado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async updateProductPrice(
    @Param('productId', ParseUUIDPipe) productId: string,
    @Body() updateData: {
      price: number;
      priceType?: PriceType;
      currency?: string;
      reason?: string;
    },
    @Request() req,
  ) {
    const { price, priceType = PriceType.REGULAR, currency = 'BRL', reason } = updateData;

    // Validate price
    const validation = this.priceValidationService.validatePrice(price);
    if (!validation.isValid) {
      throw new BadRequestException({
        message: 'Preço inválido',
        errors: validation.errors,
        warnings: validation.warnings,
      });
    }

    const updatedPrice = await this.priceIntegrationService.updateProductPrice(
      productId,
      priceType,
      price,
      currency,
      reason
    );

    // Invalidate cache
    await this.priceCacheService.invalidatePrice(productId, priceType);

    return {
      message: 'Preço atualizado com sucesso',
      price: {
        id: updatedPrice.id,
        productId: updatedPrice.productId,
        priceType: updatedPrice.priceType,
        price: updatedPrice.price,
        currency: updatedPrice.currency,
        lastUpdated: updatedPrice.lastUpdated,
        formattedPrice: updatedPrice.getFormattedPrice(),
      },
    };
  }

  @Post('products/prices/batch')
  @ApiOperation({ summary: 'Obter preços de múltiplos produtos' })
  @ApiResponse({ status: 200, description: 'Preços dos produtos' })
  async getProductPrices(
    @Body() requestData: {
      productIds: string[];
      priceType?: PriceType;
    },
  ) {
    const { productIds, priceType = PriceType.REGULAR } = requestData;

    if (!productIds || productIds.length === 0) {
      throw new BadRequestException('Lista de produtos não pode estar vazia');
    }

    if (productIds.length > 100) {
      throw new BadRequestException('Máximo de 100 produtos por requisição');
    }

    const prices = await this.priceIntegrationService.getProductPrices(productIds, priceType);

    return {
      priceType,
      count: productIds.length,
      prices,
    };
  }

  @Get('products/:productId/history')
  @ApiOperation({ summary: 'Obter histórico de preços de um produto' })
  @ApiResponse({ status: 200, description: 'Histórico de preços' })
  async getPriceHistory(
    @Param('productId', ParseUUIDPipe) productId: string,
    @Query('priceType') priceType?: string,
    @Query('limit') limit?: string,
  ) {
    const type = priceType as PriceType;
    const limitNum = limit ? parseInt(limit, 10) : 50;

    if (limitNum > 200) {
      throw new BadRequestException('Limite máximo de 200 registros');
    }

    const history = await this.priceIntegrationService.getPriceHistory(productId, type, limitNum);

    return {
      productId,
      priceType: type,
      count: history.length,
      history: history.map(record => ({
        id: record.id,
        previousPrice: record.previousPrice,
        newPrice: record.newPrice,
        currency: record.currency,
        changeType: record.changeType,
        changeAmount: record.changeAmount,
        changePercentage: record.changePercentage,
        reason: record.reason,
        source: record.source,
        recordedAt: record.recordedAt,
        provider: record.provider?.displayName,
        formattedPreviousPrice: record.getFormattedPreviousPrice(),
        formattedNewPrice: record.getFormattedNewPrice(),
        formattedChangeAmount: record.getFormattedChangeAmount(),
        formattedChangePercentage: record.getFormattedChangePercentage(),
        changeDescription: record.getChangeDescription(),
      })),
    };
  }

  @Post('refresh')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Atualizar preços expirados' })
  @ApiResponse({ status: 200, description: 'Atualização concluída' })
  async refreshExpiredPrices() {
    const result = await this.priceIntegrationService.refreshExpiredPrices();

    return {
      message: 'Atualização de preços concluída',
      ...result,
    };
  }

  @Get('providers/stats')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Obter estatísticas dos provedores de preço' })
  @ApiResponse({ status: 200, description: 'Estatísticas dos provedores' })
  async getProviderStats() {
    return this.priceIntegrationService.getProviderStats();
  }

  @Post('providers/:providerId/test')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Testar provedor de preços' })
  @ApiResponse({ status: 200, description: 'Resultado do teste' })
  async testProvider(@Param('providerId', ParseUUIDPipe) providerId: string) {
    // This would need to be implemented to get the provider and test it
    // For now, return a mock response
    return {
      providerId,
      success: true,
      responseTime: 150,
      message: 'Provedor funcionando corretamente',
    };
  }

  @Get('cache/stats')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Obter estatísticas do cache de preços' })
  @ApiResponse({ status: 200, description: 'Estatísticas do cache' })
  async getCacheStats() {
    return this.priceCacheService.getCacheStats();
  }

  @Post('cache/invalidate')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Invalidar cache de preços' })
  @ApiResponse({ status: 200, description: 'Cache invalidado' })
  async invalidateCache(
    @Body() requestData?: {
      productId?: string;
      priceType?: PriceType;
    },
  ) {
    if (requestData?.productId) {
      await this.priceCacheService.invalidatePrice(requestData.productId, requestData.priceType);
      return {
        message: `Cache invalidado para produto ${requestData.productId}`,
      };
    } else {
      await this.priceCacheService.invalidateAll();
      return {
        message: 'Todo o cache foi invalidado',
      };
    }
  }

  @Post('validate/price')
  @ApiOperation({ summary: 'Validar um preço' })
  @ApiResponse({ status: 200, description: 'Resultado da validação' })
  async validatePrice(
    @Body() requestData: {
      price: number;
      context?: {
        productId?: string;
        priceType?: string;
        previousPrice?: number;
        category?: string;
        brand?: string;
      };
    },
  ) {
    const { price, context } = requestData;
    const validation = this.priceValidationService.validatePrice(price, context);

    return {
      price,
      ...validation,
      formattedPrice: this.priceValidationService.formatPrice(price),
    };
  }

  @Post('validate/prices/batch')
  @ApiOperation({ summary: 'Validar múltiplos preços' })
  @ApiResponse({ status: 200, description: 'Resultados das validações' })
  async validatePrices(
    @Body() requestData: {
      prices: Array<{
        price: number;
        context?: {
          productId?: string;
          priceType?: string;
          previousPrice?: number;
          category?: string;
          brand?: string;
        };
      }>;
    },
  ) {
    const { prices } = requestData;

    if (!prices || prices.length === 0) {
      throw new BadRequestException('Lista de preços não pode estar vazia');
    }

    if (prices.length > 100) {
      throw new BadRequestException('Máximo de 100 preços por requisição');
    }

    const validations = this.priceValidationService.validatePriceBatch(prices);

    return {
      count: prices.length,
      validCount: validations.filter(v => v.isValid).length,
      invalidCount: validations.filter(v => !v.isValid).length,
      validations,
    };
  }

  @Get('analytics/anomalies')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Detectar anomalias de preços' })
  @ApiResponse({ status: 200, description: 'Anomalias detectadas' })
  async detectPriceAnomalies(
    @Query('prices') pricesParam?: string,
  ) {
    // In a real implementation, this would analyze prices from the database
    // For now, accept prices as a query parameter for demonstration
    let prices: number[] = [];
    
    if (pricesParam) {
      try {
        prices = pricesParam.split(',').map(p => parseFloat(p.trim())).filter(p => !isNaN(p));
      } catch (error) {
        throw new BadRequestException('Formato de preços inválido');
      }
    } else {
      // Mock data for demonstration
      prices = [10.99, 12.50, 11.75, 13.20, 45.00, 12.80, 11.90, 13.50, 12.10];
    }

    const anomalies = this.priceValidationService.detectPriceAnomalies(prices);

    return {
      totalPrices: prices.length,
      ...anomalies,
      outlierCount: anomalies.outliers.length,
    };
  }
}
