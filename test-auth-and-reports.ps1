Write-Host "Testing Auth and Reports Flow..." -ForegroundColor Cyan

$headers = @{
    "Content-Type" = "application/json"
}

# Step 1: Request OTP
Write-Host "`n=== Step 1: Request OTP ===" -ForegroundColor Yellow
$otpBody = @{
    email = "<EMAIL>"
} | ConvertTo-Json

try {
    $otpResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/auth/industries/request-otp" `
        -Method POST `
        -Headers $headers `
        -Body $otpBody `
        -UseBasicParsing

    Write-Host "OTP Request Status: $($otpResponse.StatusCode)" -ForegroundColor Green
    $otpData = $otpResponse.Content | ConvertFrom-Json
    Write-Host "Response: $($otpData | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Error requesting OTP: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Get OTP from logs (for testing)
Write-Host "`n=== Step 2: Get OTP from logs ===" -ForegroundColor Yellow
try {
    $logs = docker logs retail-media-auth-service --tail 50 2>&1
    $otpLine = $logs | Select-String "OTP <NAME_EMAIL>:" | Select-Object -Last 1
    if ($otpLine) {
        $otp = ($otpLine -split ": ")[1].Trim()
        Write-Host "OTP Code: $otp" -ForegroundColor Green
    } else {
        Write-Host "OTP not found in logs" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Error getting OTP: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Verify OTP and get token
Write-Host "`n=== Step 3: Verify OTP and get token ===" -ForegroundColor Yellow
$verifyBody = @{
    email = "<EMAIL>"
    code = $otp
} | ConvertTo-Json

try {
    $authResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/auth/industries/verify-otp" `
        -Method POST `
        -Headers $headers `
        -Body $verifyBody `
        -UseBasicParsing

    Write-Host "Auth Status: $($authResponse.StatusCode)" -ForegroundColor Green
    $authData = $authResponse.Content | ConvertFrom-Json
    $accessToken = $authData.accessToken
    Write-Host "Access Token: $($accessToken.Substring(0, 50))..." -ForegroundColor Green
} catch {
    Write-Host "Error verifying OTP: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
    exit 1
}

# Step 4: Call Reports API with token
Write-Host "`n=== Step 4: Call Reports API with token ===" -ForegroundColor Yellow
$authHeaders = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $accessToken"
}

$reportBody = @{
    dateRange = @{
        startDate = "2025-01-01"
        endDate = "2025-12-31"
    }
    status = @("active", "paused")
} | ConvertTo-Json

try {
    $reportResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/reports/campaign-performance" `
        -Method POST `
        -Headers $authHeaders `
        -Body $reportBody `
        -UseBasicParsing

    Write-Host "Report Status: $($reportResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Report Data:" -ForegroundColor Green
    $reportResponse.Content | ConvertFrom-Json | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error calling Reports API: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
    exit 1
}

Write-Host "`n=== All tests passed! ===" -ForegroundColor Green

