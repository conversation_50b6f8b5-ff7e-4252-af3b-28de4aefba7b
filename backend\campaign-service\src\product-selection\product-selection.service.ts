import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In, Between } from 'typeorm';
import { IsOptional, IsString, IsBoolean, IsNumber, IsArray, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';
import { Product, ProductStatus } from '../database/entities/product.entity';
import { ProductCategory, CategoryStatus } from '../database/entities/product-category.entity';

export class ProductSearchFilters {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  categoryId?: string;

  @IsOptional()
  @IsString()
  industryId?: string;

  @IsOptional()
  @IsString()
  brandId?: string;

  @IsOptional()
  @IsEnum(ProductStatus)
  status?: ProductStatus;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isEligibleForIncentives?: boolean;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  priceMin?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  priceMax?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  inStock?: boolean;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  limit?: number;

  @IsOptional()
  @IsString()
  sortBy?: 'name' | 'price' | 'createdAt' | 'salesCount' | 'averageRating';

  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC';
}

export interface ProductSearchResult {
  products: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export class CategorySearchFilters {
  search?: string;
  industryId?: string;
  parentId?: string;
  level?: number;
  status?: CategoryStatus;
  isEligibleForIncentives?: boolean;
  page?: number;
  limit?: number;
}

export interface CategorySearchResult {
  categories: ProductCategory[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ProductSelectionSummary {
  selectedProducts: Product[];
  totalProducts: number;
  totalValue: number;
  averagePrice: number;
  categoriesRepresented: string[];
  eligibleForIncentives: number;
  inStockCount: number;
  warnings: string[];
}

@Injectable()
export class ProductSelectionService {
  private readonly logger = new Logger(ProductSelectionService.name);

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(ProductCategory)
    private readonly categoryRepository: Repository<ProductCategory>,
  ) {}

  async searchProducts(filters: ProductSearchFilters): Promise<ProductSearchResult> {
    const page = filters.page || 1;
    const limit = Math.min(filters.limit || 20, 100); // Max 100 items per page
    const skip = (page - 1) * limit;

    const queryBuilder = this.productRepository.createQueryBuilder('product');

    // Apply filters
    if (filters.search) {
      queryBuilder.andWhere(
        '(product.name ILIKE :search OR product.sku ILIKE :search OR product.barcode ILIKE :search OR product.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    if (filters.categoryId) {
      queryBuilder.andWhere('product.categoryId = :categoryId', { categoryId: filters.categoryId });
    }

    if (filters.industryId) {
      queryBuilder.andWhere('product.industryId = :industryId', { industryId: filters.industryId });
    }

    if (filters.brandId) {
      queryBuilder.andWhere('product.brandId = :brandId', { brandId: filters.brandId });
    }

    if (filters.status) {
      queryBuilder.andWhere('product.status = :status', { status: filters.status });
    } else {
      // Default to active products only
      queryBuilder.andWhere('product.status = :status', { status: ProductStatus.ACTIVE });
    }

    if (filters.isEligibleForIncentives !== undefined) {
      queryBuilder.andWhere('product.isEligibleForIncentives = :eligible', { 
        eligible: filters.isEligibleForIncentives 
      });
    }

    if (filters.priceMin !== undefined) {
      queryBuilder.andWhere('product.price >= :priceMin', { priceMin: filters.priceMin });
    }

    if (filters.priceMax !== undefined) {
      queryBuilder.andWhere('product.price <= :priceMax', { priceMax: filters.priceMax });
    }

    if (filters.tags && filters.tags.length > 0) {
      queryBuilder.andWhere('product.tags && :tags', { tags: filters.tags });
    }

    if (filters.inStock) {
      queryBuilder.andWhere('(product.trackStock = false OR product.stockQuantity > 0)');
    }

    // Apply sorting
    const sortBy = filters.sortBy || 'name';
    const sortOrder = filters.sortOrder || 'ASC';
    queryBuilder.orderBy(`product.${sortBy}`, sortOrder);

    // Add secondary sort for consistency
    if (sortBy !== 'createdAt') {
      queryBuilder.addOrderBy('product.createdAt', 'DESC');
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const products = await queryBuilder.getMany();

    const totalPages = Math.ceil(total / limit);

    return {
      products,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async searchCategories(filters: CategorySearchFilters): Promise<CategorySearchResult> {
    const page = filters.page || 1;
    const limit = Math.min(filters.limit || 50, 100);
    const skip = (page - 1) * limit;

    const queryBuilder = this.categoryRepository.createQueryBuilder('category');

    // Apply filters
    if (filters.search) {
      queryBuilder.andWhere(
        '(category.name ILIKE :search OR category.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    if (filters.industryId) {
      queryBuilder.andWhere('category.industryId = :industryId', { industryId: filters.industryId });
    }

    if (filters.parentId) {
      queryBuilder.andWhere('category.parentId = :parentId', { parentId: filters.parentId });
    }

    if (filters.level !== undefined) {
      queryBuilder.andWhere('category.level = :level', { level: filters.level });
    }

    if (filters.status) {
      queryBuilder.andWhere('category.status = :status', { status: filters.status });
    } else {
      queryBuilder.andWhere('category.status = :status', { status: CategoryStatus.ACTIVE });
    }

    if (filters.isEligibleForIncentives !== undefined) {
      queryBuilder.andWhere('category.isEligibleForIncentives = :eligible', { 
        eligible: filters.isEligibleForIncentives 
      });
    }

    // Default sorting
    queryBuilder.orderBy('category.level', 'ASC')
               .addOrderBy('category.sortOrder', 'ASC')
               .addOrderBy('category.name', 'ASC');

    const total = await queryBuilder.getCount();
    queryBuilder.skip(skip).take(limit);

    const categories = await queryBuilder.getMany();
    const totalPages = Math.ceil(total / limit);

    return {
      categories,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async getProductById(id: string): Promise<Product | null> {
    return this.productRepository.findOne({ where: { id } });
  }

  async getProductsBySku(skus: string[]): Promise<Product[]> {
    return this.productRepository.find({
      where: { sku: In(skus) },
    });
  }

  async getProductsByCategory(categoryId: string, limit = 20): Promise<Product[]> {
    return this.productRepository.find({
      where: { 
        categoryId,
        status: ProductStatus.ACTIVE,
        isEligibleForIncentives: true,
      },
      take: limit,
      order: { name: 'ASC' },
    });
  }

  async getCategoryById(id: string): Promise<ProductCategory | null> {
    return this.categoryRepository.findOne({ 
      where: { id },
      relations: ['parent', 'children'],
    });
  }

  async getCategoryTree(industryId: string): Promise<ProductCategory[]> {
    return this.categoryRepository.find({
      where: { 
        industryId,
        status: CategoryStatus.ACTIVE,
        isVisible: true,
      },
      order: { level: 'ASC', sortOrder: 'ASC', name: 'ASC' },
    });
  }

  async getProductSelectionSummary(productIds: string[]): Promise<ProductSelectionSummary> {
    if (productIds.length === 0) {
      return {
        selectedProducts: [],
        totalProducts: 0,
        totalValue: 0,
        averagePrice: 0,
        categoriesRepresented: [],
        eligibleForIncentives: 0,
        inStockCount: 0,
        warnings: [],
      };
    }

    const products = await this.productRepository.find({
      where: { id: In(productIds) },
    });

    const warnings: string[] = [];
    const categoriesSet = new Set<string>();
    let totalValue = 0;
    let eligibleCount = 0;
    let inStockCount = 0;

    for (const product of products) {
      if (product.categoryId) {
        categoriesSet.add(product.categoryId);
      }

      if (product.price) {
        totalValue += product.price;
      }

      if (product.canReceiveIncentive()) {
        eligibleCount++;
      } else {
        warnings.push(`Produto ${product.name} não é elegível para incentivos`);
      }

      if (product.isInStock()) {
        inStockCount++;
      } else {
        warnings.push(`Produto ${product.name} está fora de estoque`);
      }
    }

    const averagePrice = products.length > 0 ? totalValue / products.length : 0;

    return {
      selectedProducts: products,
      totalProducts: products.length,
      totalValue,
      averagePrice,
      categoriesRepresented: Array.from(categoriesSet),
      eligibleForIncentives: eligibleCount,
      inStockCount,
      warnings,
    };
  }

  async getPopularProducts(industryId: string, limit = 10): Promise<Product[]> {
    return this.productRepository.find({
      where: { 
        industryId,
        status: ProductStatus.ACTIVE,
        isEligibleForIncentives: true,
      },
      order: { salesCount: 'DESC', averageRating: 'DESC' },
      take: limit,
    });
  }

  async getFeaturedProducts(industryId: string, limit = 10): Promise<Product[]> {
    return this.productRepository.find({
      where: { 
        industryId,
        status: ProductStatus.ACTIVE,
        isFeatured: true,
        isEligibleForIncentives: true,
      },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async getRecentProducts(industryId: string, limit = 10): Promise<Product[]> {
    return this.productRepository.find({
      where: { 
        industryId,
        status: ProductStatus.ACTIVE,
        isEligibleForIncentives: true,
      },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async validateProductSelection(productIds: string[]): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    validProducts: Product[];
    invalidProducts: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const validProducts: Product[] = [];
    const invalidProducts: string[] = [];

    if (productIds.length === 0) {
      errors.push('Nenhum produto selecionado');
      return { isValid: false, errors, warnings, validProducts, invalidProducts };
    }

    const products = await this.productRepository.find({
      where: { id: In(productIds) },
    });

    // Check for missing products
    const foundIds = products.map(p => p.id);
    const missingIds = productIds.filter(id => !foundIds.includes(id));
    
    if (missingIds.length > 0) {
      errors.push(`Produtos não encontrados: ${missingIds.join(', ')}`);
      invalidProducts.push(...missingIds);
    }

    // Validate each found product
    for (const product of products) {
      if (!product.isAvailable()) {
        warnings.push(`Produto ${product.name} não está disponível`);
        invalidProducts.push(product.id);
      } else if (!product.canReceiveIncentive()) {
        warnings.push(`Produto ${product.name} não é elegível para incentivos`);
        invalidProducts.push(product.id);
      } else {
        validProducts.push(product);
      }
    }

    return {
      isValid: errors.length === 0 && validProducts.length > 0,
      errors,
      warnings,
      validProducts,
      invalidProducts,
    };
  }
}
