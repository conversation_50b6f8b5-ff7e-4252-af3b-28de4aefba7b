import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe, ExecutionContext } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { RolesGuard } from '../../src/common/guards/roles.guard';
import { JwtAuthGuard } from '../../src/common/guards/jwt-auth.guard';

describe('CampaignsController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
    .overrideGuard(JwtAuthGuard)
    .useValue({
      canActivate: (context: ExecutionContext) => {
        const request = context.switchToHttp().getRequest();
        request.user = {
          sub: '550e8400-e29b-41d4-a716-************', // Valid UUID v4 for test user
          role: 'admin',
          email: '<EMAIL>',
          industryId: '123e4567-e89b-12d3-a456-************' // Valid UUID v4 for test industry
        };
        return true;
      },
    })
    .overrideGuard(RolesGuard)
    .useValue({
      canActivate: (context: ExecutionContext) => {
        const request = context.switchToHttp().getRequest();
        request.user = request.user || {
          sub: '550e8400-e29b-41d4-a716-************', // Valid UUID v4 for test user
          role: 'admin',
          email: '<EMAIL>',
          industryId: '123e4567-e89b-12d3-a456-************' // Valid UUID v4 for test industry
        };
        return true;
      },
    })
    .compile();

    app = moduleFixture.createNestApplication();
    app.setGlobalPrefix('api/v1');
    app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/api/v1/campaigns (GET)', () => {
    it('should return list of campaigns', () => {
      return request(app.getHttpServer())
        .get('/api/v1/campaigns')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(res.body).toHaveProperty('meta');
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });

    it('should filter campaigns by status', () => {
      return request(app.getHttpServer())
        .get('/api/v1/campaigns?status=active')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });

    it('should paginate campaigns', () => {
      return request(app.getHttpServer())
        .get('/api/v1/campaigns?page=1&limit=10')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('meta');
          expect(res.body.meta).toHaveProperty('page');
          expect(res.body.meta).toHaveProperty('limit');
        });
    });
  });

  describe('/api/v1/campaigns/:id (GET)', () => {
    it('should return 400 for invalid UUID', () => {
      return request(app.getHttpServer())
        .get('/api/v1/campaigns/invalid-uuid')
        .expect(400);
    });

    it('should return 404 for non-existent campaign', () => {
      return request(app.getHttpServer())
        .get('/api/v1/campaigns/00000000-0000-0000-0000-000000000000')
        .expect(404);
    });
  });

  describe('/api/v1/campaigns (POST)', () => {
    it('should return 400 for invalid campaign data', () => {
      return request(app.getHttpServer())
        .post('/api/v1/campaigns')
        .send({})
        .expect(400);
    });

    it('should create campaign with valid data', async () => {
      const validCampaign = {
        name: 'Test Campaign',
        description: 'Test Description',
        industryId: '123e4567-e89b-12d3-a456-************', // Valid UUID v4
        incentiveType: 'percentage',
        incentivePercentage: 15, // Changed from 10 to 15 to be above minimum
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/campaigns')
        .send(validCampaign);

      if (response.status !== 201) {
        console.log('Error response:', response.body);
      }

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(validCampaign.name);
      expect(response.body.status).toBe('draft');
    });
  });

  describe('/api/v1/campaigns/:id (PATCH)', () => {
    it('should return 400 for invalid UUID', () => {
      return request(app.getHttpServer())
        .patch('/api/v1/campaigns/invalid-uuid')
        .send({ name: 'Updated Name' })
        .expect(400);
    });

    it('should return 404 for non-existent campaign', () => {
      return request(app.getHttpServer())
        .patch('/api/v1/campaigns/00000000-0000-0000-0000-000000000000')
        .send({ name: 'Updated Name' })
        .expect(404);
    });
  });

  describe('/api/v1/campaigns/:id/activate (POST)', () => {
    it('should return 400 for invalid UUID', () => {
      return request(app.getHttpServer())
        .post('/api/v1/campaigns/invalid-uuid/activate')
        .expect(400);
    });

    it('should return 404 for non-existent campaign', () => {
      return request(app.getHttpServer())
        .post('/api/v1/campaigns/00000000-0000-0000-0000-000000000000/activate')
        .expect(404);
    });
  });

  describe('/api/v1/campaigns/:id/pause (POST)', () => {
    it('should return 400 for invalid UUID', () => {
      return request(app.getHttpServer())
        .post('/api/v1/campaigns/invalid-uuid/pause')
        .send({ reason: 'Test pause' })
        .expect(400);
    });

    it('should return 404 for non-existent campaign', () => {
      return request(app.getHttpServer())
        .post('/api/v1/campaigns/00000000-0000-0000-0000-000000000000/pause')
        .send({ reason: 'Test pause' })
        .expect(404);
    });
  });

  describe('/api/v1/campaigns/:id/end (POST)', () => {
    it('should return 400 for invalid UUID', () => {
      return request(app.getHttpServer())
        .post('/api/v1/campaigns/invalid-uuid/end')
        .send({ reason: 'Test end' })
        .expect(400);
    });

    it('should return 404 for non-existent campaign', () => {
      return request(app.getHttpServer())
        .post('/api/v1/campaigns/00000000-0000-0000-0000-000000000000/end')
        .send({ reason: 'Test end' })
        .expect(404);
    });
  });

  describe('/api/v1/campaigns/:id/analytics (GET)', () => {
    it('should return 400 for invalid UUID', () => {
      return request(app.getHttpServer())
        .get('/api/v1/campaigns/invalid-uuid/analytics')
        .expect(400);
    });

    it('should return 404 for non-existent campaign', () => {
      return request(app.getHttpServer())
        .get('/api/v1/campaigns/00000000-0000-0000-0000-000000000000/analytics')
        .expect(404);
    });
  });

  describe('Error handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // This test would require mocking database failures
      // For now, we just verify the endpoint exists
      const response = await request(app.getHttpServer())
        .get('/api/v1/campaigns')
        .expect(200);
      
      expect(response.body).toBeDefined();
    });

    it('should validate date ranges', () => {
      const invalidCampaign = {
        name: 'Test Campaign',
        description: 'Test Description',
        industryId: 'test-industry-id',
        incentiveType: 'percentage',
        incentiveValue: 10,
        startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date().toISOString(), // End before start
        budget: 10000,
      };

      return request(app.getHttpServer())
        .post('/api/v1/campaigns')
        .send(invalidCampaign)
        .expect(400);
    });

    it('should validate incentive values', () => {
      const invalidCampaign = {
        name: 'Test Campaign',
        description: 'Test Description',
        industryId: 'test-industry-id',
        incentiveType: 'percentage',
        incentiveValue: 150, // Invalid percentage > 100
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        budget: 10000,
      };

      return request(app.getHttpServer())
        .post('/api/v1/campaigns')
        .send(invalidCampaign)
        .expect(400);
    });
  });

  describe('Performance', () => {
    it('should respond within acceptable time', async () => {
      const startTime = Date.now();
      
      await request(app.getHttpServer())
        .get('/api/v1/campaigns')
        .expect(200);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(responseTime).toBeLessThan(2000); // Should respond within 2 seconds
    });
  });
});

