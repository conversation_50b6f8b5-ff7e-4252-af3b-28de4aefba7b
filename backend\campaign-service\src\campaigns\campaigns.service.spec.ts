import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CampaignsService } from './campaigns.service';
import { Campaign } from '../database/entities/campaign.entity';
import { TransitionService } from './transition.service';
import { AIEstimationService } from './ai-estimation.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { CampaignProduct } from '../database/entities/campaign-product.entity';
import { CampaignTransition } from '../database/entities/campaign-transition.entity';

describe('CampaignsService', () => {
  let service: CampaignsService;
  let repository: Repository<Campaign>;

  const mockCampaign = {
    id: '1',
    name: 'Test Campaign',
    startDate: new Date(),
    endDate: new Date(),
    status: 'draft',
    budget: 1000,
  };

  const mockCampaignRepository = {
    createQueryBuilder: jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn().mockResolvedValue([[mockCampaign], 1]),
      getOne: jest.fn().mockResolvedValue(mockCampaign),
    })),
    find: jest.fn().mockResolvedValue([mockCampaign]),
    findOne: jest.fn().mockResolvedValue(mockCampaign),
    create: jest.fn().mockReturnValue(mockCampaign),
    save: jest.fn().mockResolvedValue(mockCampaign),
    remove: jest.fn().mockResolvedValue(mockCampaign),
  };

  const mockTransitionService = {
    createTransition: jest.fn(),
  };

  const mockAiEstimationService = {
    estimateCampaignPerformance: jest.fn(),
  };

  const mockCacheManager = {
    get: jest.fn(),
    set: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CampaignsService,
        {
          provide: getRepositoryToken(Campaign),
          useValue: mockCampaignRepository,
        },
        {
            provide: getRepositoryToken(CampaignProduct),
            useValue: {},
        },
        {
            provide: getRepositoryToken(CampaignTransition),
            useValue: {},
        },
        {
          provide: TransitionService,
          useValue: mockTransitionService,
        },
        {
          provide: AIEstimationService,
          useValue: mockAiEstimationService,
        },
        {
          provide: CACHE_MANAGER,
          useValue: mockCacheManager,
        },
      ],
    }).compile();

    service = module.get<CampaignsService>(CampaignsService);
    repository = module.get<Repository<Campaign>>(getRepositoryToken(Campaign));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return an array of campaigns', async () => {
    const result = await service.findAll({}, '1', 'admin');
    expect(result.data).toEqual([mockCampaign]);
    expect(result.meta).toBeDefined();
  });

  it('should return a single campaign', async () => {
    const campaign = await service.findOne('1', '1', 'admin');
    expect(campaign).toEqual(mockCampaign);
  });

  it('should create a campaign', async () => {
    mockAiEstimationService.estimateCampaignPerformance.mockResolvedValue({ estimatedConsumers: 100, estimatedCost: 500 });
    const campaign = await service.create(mockCampaign as any, '1');
    expect(campaign).toEqual(mockCampaign);
  });

  it('should update a campaign', async () => {
    const campaign = await service.update('1', mockCampaign as any, '1');
    expect(campaign).toEqual(mockCampaign);
  });
});

