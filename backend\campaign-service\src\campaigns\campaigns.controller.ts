import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

import { CampaignsService } from './campaigns.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto, ScheduleTransitionDto, PauseCampaignDto, EndCampaignDto } from './dto/update-campaign.dto';
import { CampaignFiltersDto } from './dto/campaign-filters.dto';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

@ApiTags('campaigns')
@Controller('campaigns')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CampaignsController {
  constructor(private readonly campaignsService: CampaignsService) {}

  @Get()
  @ApiOperation({ summary: 'Listar campanhas' })
  @ApiQuery({ name: 'page', required: false, description: 'Página (padrão: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Itens por página (padrão: 20)' })
  @ApiQuery({ name: 'status', required: false, description: 'Filtrar por status' })
  @ApiQuery({ name: 'industryId', required: false, description: 'Filtrar por indústria' })
  @ApiQuery({ name: 'search', required: false, description: 'Buscar por nome ou descrição' })
  @ApiResponse({ status: 200, description: 'Lista de campanhas' })
  async findAll(@Query() filters: CampaignFiltersDto, @Request() req) {
    return this.campaignsService.findAll(filters, req.user.sub, req.user.role);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obter campanha por ID' })
  @ApiResponse({ status: 200, description: 'Dados da campanha' })
  @ApiResponse({ status: 404, description: 'Campanha não encontrada' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.campaignsService.findOne(id, req.user.sub, req.user.role);
  }

  @Post()
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Criar nova campanha' })
  @ApiResponse({ status: 201, description: 'Campanha criada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  async create(@Body() createCampaignDto: CreateCampaignDto, @Request() req) {
    return this.campaignsService.create(createCampaignDto, req.user.sub);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Atualizar campanha' })
  @ApiResponse({ status: 200, description: 'Campanha atualizada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos ou campanha não pode ser editada' })
  @ApiResponse({ status: 404, description: 'Campanha não encontrada' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCampaignDto: UpdateCampaignDto,
    @Request() req,
  ) {
    return this.campaignsService.update(id, updateCampaignDto, req.user.sub);
  }

  @Post(':id/activate')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Ativar campanha' })
  @ApiResponse({ status: 200, description: 'Campanha ativada com sucesso' })
  @ApiResponse({ status: 400, description: 'Campanha não pode ser ativada' })
  async activate(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.campaignsService.activate(id, req.user.sub);
  }

  @Post(':id/pause')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Pausar campanha' })
  @ApiResponse({ status: 200, description: 'Campanha pausada com sucesso' })
  @ApiResponse({ status: 400, description: 'Campanha não pode ser pausada' })
  async pause(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() pauseCampaignDto: PauseCampaignDto,
    @Request() req,
  ) {
    return this.campaignsService.pause(id, req.user.sub, pauseCampaignDto.reason);
  }

  @Post(':id/resume')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Retomar campanha pausada' })
  @ApiResponse({ status: 200, description: 'Campanha retomada com sucesso' })
  @ApiResponse({ status: 400, description: 'Campanha não pode ser retomada' })
  async resume(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.campaignsService.resume(id, req.user.sub);
  }

  @Post(':id/end')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Encerrar campanha' })
  @ApiResponse({ status: 200, description: 'Campanha encerrada com sucesso' })
  @ApiResponse({ status: 400, description: 'Campanha não pode ser encerrada' })
  async end(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() endCampaignDto: EndCampaignDto,
    @Request() req,
  ) {
    return this.campaignsService.end(id, req.user.sub, endCampaignDto.reason);
  }

  @Post(':id/schedule')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Agendar transição de campanha' })
  @ApiResponse({ status: 200, description: 'Transição agendada com sucesso' })
  @ApiResponse({ status: 400, description: 'Ação não pode ser agendada' })
  async schedule(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() scheduleDto: ScheduleTransitionDto,
    @Request() req,
  ) {
    return this.campaignsService.schedule(
      id,
      scheduleDto.action,
      scheduleDto.executeAt,
      req.user.sub,
    );
  }

  @Get(':id/metrics')
  @ApiOperation({ summary: 'Obter métricas da campanha' })
  @ApiResponse({ status: 200, description: 'Métricas da campanha' })
  async getMetrics(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.campaignsService.getMetrics(id, req.user.sub);
  }

  @Get(':id/analytics')
  @ApiOperation({ summary: 'Obter analytics da campanha' })
  @ApiResponse({ status: 200, description: 'Analytics da campanha' })
  @ApiResponse({ status: 404, description: 'Campanha não encontrada' })
  async getAnalytics(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.campaignsService.getMetrics(id, req.user.sub);
  }

  @Get(':id/transitions')
  @ApiOperation({ summary: 'Obter histórico de transições da campanha' })
  @ApiResponse({ status: 200, description: 'Histórico de transições' })
  async getTransitions(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    // This would be implemented in TransitionService
    return { message: 'Transition history endpoint' };
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Remover campanha (apenas admins)' })
  @ApiResponse({ status: 200, description: 'Campanha removida com sucesso' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    // Soft delete implementation
    return { message: 'Campaign deletion endpoint' };
  }

  // Product management endpoints
  @Post(':id/products')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Adicionar produtos à campanha' })
  @ApiResponse({ status: 201, description: 'Produtos adicionados com sucesso' })
  async addProducts(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() products: any[],
    @Request() req,
  ) {
    // This would be implemented in a separate ProductsService
    return { message: 'Add products endpoint' };
  }

  @Delete(':id/products/:productId')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Remover produto da campanha' })
  @ApiResponse({ status: 200, description: 'Produto removido com sucesso' })
  async removeProduct(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('productId', ParseUUIDPipe) productId: string,
    @Request() req,
  ) {
    return { message: 'Remove product endpoint' };
  }

  @Patch(':id/products/:productId')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Atualizar produto da campanha' })
  @ApiResponse({ status: 200, description: 'Produto atualizado com sucesso' })
  async updateProduct(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('productId', ParseUUIDPipe) productId: string,
    @Body() updateData: any,
    @Request() req,
  ) {
    return { message: 'Update product endpoint' };
  }

  // AI and estimation endpoints
  @Post(':id/estimate')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Gerar nova estimativa de IA para a campanha' })
  @ApiResponse({ status: 200, description: 'Estimativa gerada com sucesso' })
  async generateEstimation(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    // This would trigger AI estimation regeneration
    return { message: 'AI estimation endpoint' };
  }

  @Get(':id/recommendations')
  @ApiOperation({ summary: 'Obter recomendações de IA para a campanha' })
  @ApiResponse({ status: 200, description: 'Recomendações de IA' })
  async getRecommendations(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return { message: 'AI recommendations endpoint' };
  }
}
