'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeft, Download, Filter, Calendar, BarChart3, TrendingUp, Eye, MousePointer, RefreshCw } from 'lucide-react';
import CampaignFilters, { CampaignFilter } from '@/components/reports/CampaignFilters';
import PeriodSelector, { PeriodFilter } from '@/components/reports/PeriodSelector';
import ExportModal, { ExportOptions } from '@/components/reports/ExportModal';
import { useReports } from '@/hooks/useReports';

export default function ReportsPage() {
  console.log('🔍 ReportsPage renderizando...');

  const { isAuthenticated, isLoading } = useAuth();
  console.log('🔍 Auth state:', { isAuthenticated, isLoading });

  const router = useRouter();

  // Re-enable useReports to test
  const { reportData, isLoading: isLoadingReports, error, loadReportData, exportReport, refreshData } = useReports();

  // Modal states
  const [showFilters, setShowFilters] = useState(false);
  const [showPeriodSelector, setShowPeriodSelector] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);

  // Filter states
  const [campaignFilters, setCampaignFilters] = useState<CampaignFilter>({
    campaignIds: [],
    status: [],
    search: ''
  });

  const [periodFilter, setPeriodFilter] = useState<PeriodFilter>({
    type: 'preset',
    preset: 'all'
  });

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  const handleBack = () => {
    router.push('/dashboard');
  };

  const handleFiltersChange = (filters: CampaignFilter) => {
    console.log('🎯 handleFiltersChange chamado com:', filters);
    setCampaignFilters(filters);
    console.log('📞 Chamando loadReportData...');
    loadReportData(filters, periodFilter);
  };

  const handlePeriodChange = (period: PeriodFilter) => {
    console.log('📅 handlePeriodChange chamado com:', period);
    setPeriodFilter(period);
    console.log('📞 Chamando loadReportData com período...');
    loadReportData(campaignFilters, period);
  };

  const handleExport = async (options: ExportOptions) => {
    await exportReport(campaignFilters, periodFilter, options);
  };

  const getFilterSummary = () => {
    const parts = [];

    if (campaignFilters.campaignIds.length > 0) {
      parts.push(`${campaignFilters.campaignIds.length} campanhas`);
    }

    if (campaignFilters.status.length > 0) {
      const statusLabels = {
        'active': 'Ativas',
        'completed': 'Concluídas',
        'paused': 'Pausadas',
        'draft': 'Rascunho'
      };
      const translatedStatus = campaignFilters.status.map(status =>
        statusLabels[status as keyof typeof statusLabels] || status
      );
      parts.push(translatedStatus.join(', '));
    }

    return parts.length > 0 ? parts.join(' | ') : '';
  };

  const getPeriodSummary = () => {
    if (periodFilter.type === 'preset') {
      const presetLabels = {
        'all': 'Todos',
        '1d': '1 dia',
        '7d': '7 dias',
        '30d': '30 dias',
        '60d': '60 dias',
        '90d': '90 dias'
      };
      return presetLabels[periodFilter.preset as keyof typeof presetLabels] || 'Todos';
    } else if (periodFilter.startDate && periodFilter.endDate) {
      return `${new Date(periodFilter.startDate).toLocaleDateString()} - ${new Date(periodFilter.endDate).toLocaleDateString()}`;
    }
    return 'Todos';
  };



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={handleBack}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <h1 className="text-xl font-bold text-gray-900">
                <span className="text-red-600">Retail Media</span> Platform
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowFilters(true)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
                {getFilterSummary() && (
                  <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                    {getFilterSummary()}
                  </span>
                )}
              </button>
              <button
                onClick={() => setShowPeriodSelector(true)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Calendar className="h-4 w-4 mr-2" />
                {getPeriodSummary()}
              </button>
              <button
                onClick={() => setShowExportModal(true)}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md !text-white bg-blue-600 hover:bg-blue-700"
              >
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </button>
              <button
                onClick={refreshData}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                disabled={isLoadingReports}
              >
                <RefreshCw className={`h-4 w-4 ${isLoadingReports ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Relatórios</h2>
          <p className="text-gray-700">
            Análise detalhada do desempenho das suas campanhas de retail media.
          </p>
        </div>

        {/* Quick Stats */}
        {isLoadingReports ? (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="animate-pulse">
                  <div className="flex items-center">
                    <div className="p-2 bg-gray-200 rounded-lg w-10 h-10"></div>
                    <div className="ml-4 flex-1">
                      <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-16 mb-1"></div>
                      <div className="h-3 bg-gray-200 rounded w-20"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div className="text-center">
              <p className="text-red-600 mb-2">Erro ao carregar dados</p>
              <p className="text-sm text-red-500">{error}</p>
              <button
                onClick={refreshData}
                className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Tentar Novamente
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-700">Receita Total</p>
                  <p className="text-2xl font-bold text-gray-900">
                    R$ {typeof reportData?.summary?.totalRevenue === 'number' ? reportData.summary.totalRevenue.toLocaleString('pt-BR', { minimumFractionDigits: 2 }) : '0,00'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {reportData?.summary.totalCampaigns || 0} campanhas
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Eye className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-700">Alcance Total</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {typeof reportData?.summary?.totalReach === 'number' ? reportData.summary.totalReach.toLocaleString('pt-BR') : '0'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {reportData?.summary.activeCampaigns || 0} ativas
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <MousePointer className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-700">Conversões</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {typeof reportData?.summary?.totalConversions === 'number' ? reportData.summary.totalConversions.toLocaleString('pt-BR') : '0'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {typeof reportData?.summary?.totalEngagement === 'number' ? reportData.summary.totalEngagement.toLocaleString('pt-BR') : '0'} engajamentos
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-700">ROI Médio</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {typeof reportData?.summary?.averageROI === 'number' ? reportData.summary.averageROI.toFixed(1) : '0.0'}x
                  </p>
                  <p className="text-xs text-gray-500">
                    {typeof reportData?.summary?.averageIncentive === 'number' ? reportData.summary.averageIncentive.toFixed(1) : '0.0'}% incentivo médio
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Reports Grid */}
        {reportData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Campaign Performance */}
            <div className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <BarChart3 className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">Performance de Campanhas</h3>
                    <p className="text-sm text-gray-700">Análise detalhada do desempenho das campanhas</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center py-1">
                  <span className="text-sm text-gray-800">Campanhas Ativas:</span>
                  <span className="text-sm font-medium text-gray-900">{reportData?.summary?.activeCampaigns || 0}</span>
                </div>
                <div className="flex justify-between items-center py-1">
                  <span className="text-sm text-gray-800">Total de Campanhas:</span>
                  <span className="text-sm font-medium text-gray-900">{reportData?.summary?.totalCampaigns || 0}</span>
                </div>
                <div className="flex justify-between items-center py-1">
                  <span className="text-sm text-gray-800">ROI Médio:</span>
                  <span className="text-sm font-medium text-gray-900">{typeof reportData?.summary?.averageROI === 'number' ? reportData.summary.averageROI.toFixed(1) : '0.0'}x</span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <button
                  onClick={() => setShowExportModal(true)}
                  className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Baixar Relatório
                </button>
              </div>
            </div>

            {/* Top Performers */}
            <div className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">Top Performers</h3>
                    <p className="text-sm text-gray-700">Campanhas e produtos com melhor desempenho</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Melhores Campanhas:</h4>
                  {(reportData?.topPerformers?.campaigns || []).slice(0, 3).map((campaign) => (
                    <div key={campaign.id} className="flex justify-between items-center py-1">
                      <span className="text-sm text-gray-800 truncate">{campaign.name}</span>
                      <span className="text-sm font-medium text-green-600">{typeof campaign.roi === 'number' ? campaign.roi.toFixed(1) : '0.0'}x</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <button
                  onClick={() => setShowExportModal(true)}
                  className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Baixar Relatório
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Chart Placeholder */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance ao Longo do Tempo</h3>
          <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-gray-500 mx-auto mb-2" />
              <p className="text-gray-700">Gráfico de performance será exibido aqui</p>
              <p className="text-sm text-gray-600">Integração com biblioteca de gráficos em desenvolvimento</p>
            </div>
          </div>
        </div>
      </main>

      {/* Modals */}
      <CampaignFilters
        filters={campaignFilters}
        onFiltersChange={handleFiltersChange}
        isOpen={showFilters}
        onClose={() => setShowFilters(false)}
      />

      <PeriodSelector
        period={periodFilter}
        onPeriodChange={handlePeriodChange}
        isOpen={showPeriodSelector}
        onClose={() => setShowPeriodSelector(false)}
      />

      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        onExport={handleExport}
        filters={campaignFilters}
        period={periodFilter}
      />
    </div>
  );
}
