import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Campaign } from './campaign.entity';

export enum TouchpointType {
  IMPRESSION = 'impression',
  CLICK = 'click',
  VIEW = 'view',
  INTERACTION = 'interaction',
  EMAIL_OPEN = 'email_open',
  EMAIL_CLICK = 'email_click',
  SOCIAL_SHARE = 'social_share',
  VIDEO_VIEW = 'video_view',
}

export enum TouchpointChannel {
  DISPLAY = 'display',
  SEARCH = 'search',
  SOCIAL = 'social',
  EMAIL = 'email',
  VIDEO = 'video',
  MOBILE = 'mobile',
  NATIVE = 'native',
  AUDIO = 'audio',
  OUTDOOR = 'outdoor',
  DIRECT = 'direct',
  REFERRAL = 'referral',
  ORGANIC = 'organic',
}

export enum DeviceType {
  DESKTOP = 'desktop',
  MOBILE = 'mobile',
  TABLET = 'tablet',
  TV = 'tv',
  UNKNOWN = 'unknown',
}

@Entity('touchpoints')
@Index(['userId', 'timestamp'])
@Index(['campaignId', 'timestamp'])
@Index(['type', 'timestamp'])
@Index(['channel', 'timestamp'])
@Index(['timestamp'])
@Index(['sessionId'])
export class Touchpoint {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  userId: string;

  @Column({ type: 'uuid', nullable: true })
  @Index()
  campaignId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  sessionId: string;

  @Column({ type: 'enum', enum: TouchpointType })
  type: TouchpointType;

  @Column({ type: 'enum', enum: TouchpointChannel })
  channel: TouchpointChannel;

  @Column({ type: 'timestamp' })
  @Index()
  timestamp: Date;

  @Column({ type: 'varchar', length: 500, nullable: true })
  url: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  referrer: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  source: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  medium: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  campaign: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  content: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  term: string;

  @Column({ type: 'enum', enum: DeviceType, default: DeviceType.UNKNOWN })
  deviceType: DeviceType;

  @Column({ type: 'varchar', length: 255, nullable: true })
  userAgent: string;

  @Column({ type: 'varchar', length: 45, nullable: true })
  ipAddress: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  countryCode: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  city: string;

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true })
  latitude: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true })
  longitude: number;

  @Column({ type: 'json', nullable: true })
  customAttributes: Record<string, any>;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  value: number; // Associated value (e.g., cost, revenue)

  @Column({ type: 'varchar', length: 3, default: 'BRL' })
  currency: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => Campaign, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'campaignId' })
  campaignEntity: Campaign;

  // Helper methods
  isWithinWindow(windowDays: number, referenceDate: Date = new Date()): boolean {
    const windowMs = windowDays * 24 * 60 * 60 * 1000;
    const timeDiff = referenceDate.getTime() - this.timestamp.getTime();
    return timeDiff >= 0 && timeDiff <= windowMs;
  }

  getTimeSinceTouch(referenceDate: Date = new Date()): number {
    return referenceDate.getTime() - this.timestamp.getTime();
  }

  getFormattedValue(): string | null {
    if (!this.value) return null;
    
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: this.currency,
    }).format(this.value);
  }

  getChannelGroup(): string {
    const channelGroups = {
      [TouchpointChannel.DISPLAY]: 'Paid Media',
      [TouchpointChannel.SEARCH]: 'Paid Search',
      [TouchpointChannel.SOCIAL]: 'Social Media',
      [TouchpointChannel.EMAIL]: 'Email Marketing',
      [TouchpointChannel.VIDEO]: 'Video Advertising',
      [TouchpointChannel.MOBILE]: 'Mobile Advertising',
      [TouchpointChannel.NATIVE]: 'Native Advertising',
      [TouchpointChannel.AUDIO]: 'Audio Advertising',
      [TouchpointChannel.OUTDOOR]: 'Outdoor Advertising',
      [TouchpointChannel.DIRECT]: 'Direct',
      [TouchpointChannel.REFERRAL]: 'Referral',
      [TouchpointChannel.ORGANIC]: 'Organic',
    };

    return channelGroups[this.channel] || 'Other';
  }

  getInteractionWeight(): number {
    // Weight different touchpoint types for attribution
    const weights = {
      [TouchpointType.CLICK]: 1.0,
      [TouchpointType.INTERACTION]: 0.8,
      [TouchpointType.VIEW]: 0.6,
      [TouchpointType.IMPRESSION]: 0.4,
      [TouchpointType.EMAIL_CLICK]: 0.9,
      [TouchpointType.EMAIL_OPEN]: 0.3,
      [TouchpointType.SOCIAL_SHARE]: 0.7,
      [TouchpointType.VIDEO_VIEW]: 0.5,
    };

    return weights[this.type] || 0.2;
  }

  static createFromEvent(eventData: {
    userId: string;
    campaignId?: string;
    sessionId?: string;
    type: TouchpointType;
    channel: TouchpointChannel;
    url?: string;
    referrer?: string;
    source?: string;
    medium?: string;
    campaign?: string;
    content?: string;
    term?: string;
    deviceType?: DeviceType;
    userAgent?: string;
    ipAddress?: string;
    customAttributes?: Record<string, any>;
    value?: number;
    currency?: string;
  }): Partial<Touchpoint> {
    return {
      userId: eventData.userId,
      campaignId: eventData.campaignId,
      sessionId: eventData.sessionId,
      type: eventData.type,
      channel: eventData.channel,
      timestamp: new Date(),
      url: eventData.url,
      referrer: eventData.referrer,
      source: eventData.source,
      medium: eventData.medium,
      campaign: eventData.campaign,
      content: eventData.content,
      term: eventData.term,
      deviceType: eventData.deviceType || DeviceType.UNKNOWN,
      userAgent: eventData.userAgent,
      ipAddress: eventData.ipAddress,
      customAttributes: eventData.customAttributes,
      value: eventData.value,
      currency: eventData.currency || 'BRL',
    };
  }
}
