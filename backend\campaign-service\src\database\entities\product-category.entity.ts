import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  Tree,
  TreeParent,
  TreeChildren,
} from 'typeorm';

export enum CategoryStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
}

@Entity('product_categories')
@Tree('nested-set')
export class ProductCategory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  slug: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 255 })
  industryId: string;

  @TreeParent()
  parent: ProductCategory;

  @TreeChildren()
  children: ProductCategory[];

  @Column({ type: 'varchar', nullable: true })
  parentId: string;

  @Column({ type: 'int', default: 0 })
  level: number;

  @Column({ type: 'int', default: 0 })
  sortOrder: number;

  @Column({
    type: 'enum',
    enum: CategoryStatus,
    default: CategoryStatus.ACTIVE,
  })
  status: CategoryStatus;

  @Column({ type: 'varchar', length: 255, nullable: true })
  imageUrl: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  iconUrl: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  color: string;

  @Column({ type: 'json', nullable: true })
  attributes: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'boolean', default: true })
  isEligibleForIncentives: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  defaultMaxIncentivePercentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  defaultMaxIncentiveValue: number;

  @Column({ type: 'boolean', default: true })
  allowSubcategoryIncentives: boolean;

  @Column({ type: 'int', default: 0 })
  productCount: number;

  @Column({ type: 'boolean', default: true })
  isVisible: boolean;

  @Column({ type: 'boolean', default: false })
  isFeatured: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  seoTitle: string;

  @Column({ type: 'text', nullable: true })
  seoDescription: string;

  @Column({ type: 'json', nullable: true })
  seoKeywords: string[];

  @Column({ type: 'varchar', length: 255, nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  updatedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  getFullPath(): string {
    // This would need to be implemented with a recursive query
    // For now, return just the name
    return this.name;
  }

  isRoot(): boolean {
    return !this.parentId;
  }

  hasChildren(): boolean {
    return this.children && this.children.length > 0;
  }

  isActive(): boolean {
    return this.status === CategoryStatus.ACTIVE && this.isVisible;
  }

  canReceiveIncentives(): boolean {
    return this.isEligibleForIncentives && this.isActive();
  }

  getMaxIncentive(type: 'percentage' | 'value'): number | null {
    if (type === 'percentage') {
      return this.defaultMaxIncentivePercentage;
    }
    return this.defaultMaxIncentiveValue;
  }

  validateIncentive(percentage?: number, value?: number): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.canReceiveIncentives()) {
      errors.push('Categoria não elegível para incentivos');
    }

    if (percentage !== undefined) {
      if (this.defaultMaxIncentivePercentage && percentage > this.defaultMaxIncentivePercentage) {
        errors.push(`Percentual máximo para categoria: ${this.defaultMaxIncentivePercentage}%`);
      }
    }

    if (value !== undefined) {
      if (this.defaultMaxIncentiveValue && value > this.defaultMaxIncentiveValue) {
        errors.push(`Valor máximo para categoria: R$ ${this.defaultMaxIncentiveValue.toFixed(2)}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  getBreadcrumb(): string[] {
    // This would need to be implemented with parent traversal
    // For now, return just the current category
    return [this.name];
  }

  getIncentiveRules(): {
    maxPercentage?: number;
    maxValue?: number;
    allowSubcategories: boolean;
  } {
    return {
      maxPercentage: this.defaultMaxIncentivePercentage,
      maxValue: this.defaultMaxIncentiveValue,
      allowSubcategories: this.allowSubcategoryIncentives,
    };
  }
}
