import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class FixProductCategoryColumns1703000000005 implements MigrationInterface {
  name = 'FixProductCategoryColumns1703000000005';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add slug column if not exists
    const slugColumnExists = await queryRunner.hasColumn('product_categories', 'slug');
    if (!slugColumnExists) {
      await queryRunner.addColumn(
        'product_categories',
        new TableColumn({
          name: 'slug',
          type: 'varchar',
          length: '255',
          isNullable: true,
        }),
      );
    }

    // Add missing columns with correct camelCase names
    const columnsToAdd = [
      { name: 'sortOrder', type: 'int', default: 0 },
      { name: 'imageUrl', type: 'varchar', length: '255', isNullable: true },
      { name: 'iconUrl', type: 'varchar', length: '255', isNullable: true },
      { name: 'isEligibleForIncentives', type: 'boolean', default: true },
      { name: 'defaultMaxIncentivePercentage', type: 'decimal', precision: 5, scale: 2, isNullable: true },
      { name: 'defaultMaxIncentiveValue', type: 'decimal', precision: 10, scale: 2, isNullable: true },
      { name: 'allowSubcategoryIncentives', type: 'boolean', default: true },
      { name: 'productCount', type: 'int', default: 0 },
      { name: 'isVisible', type: 'boolean', default: true },
      { name: 'isFeatured', type: 'boolean', default: false },
      { name: 'seoTitle', type: 'varchar', length: '255', isNullable: true },
      { name: 'seoDescription', type: 'text', isNullable: true },
      { name: 'seoKeywords', type: 'json', isNullable: true },
      { name: 'createdBy', type: 'varchar', length: '255', isNullable: true },
      { name: 'updatedBy', type: 'varchar', length: '255', isNullable: true },
    ];

    for (const col of columnsToAdd) {
      const columnExists = await queryRunner.hasColumn('product_categories', col.name);
      if (!columnExists) {
        const column = new TableColumn({
          name: col.name,
          type: col.type,
          ...(col.length && { length: col.length }),
          ...(col.precision && { precision: col.precision }),
          ...(col.scale && { scale: col.scale }),
          isNullable: col.isNullable !== undefined ? col.isNullable : false,
          ...(col.default !== undefined && { default: col.default }),
        });
        await queryRunner.addColumn('product_categories', column);
      }
    }

    // Update existing rows with slug if null
    await queryRunner.query(`
      UPDATE product_categories 
      SET slug = LOWER(REPLACE(REPLACE(name, ' ', '-'), 'ã', 'a'))
      WHERE slug IS NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop columns in reverse order
    const columnsToDrop = [
      'updatedBy',
      'createdBy',
      'seoKeywords',
      'seoDescription',
      'seoTitle',
      'isFeatured',
      'isVisible',
      'productCount',
      'allowSubcategoryIncentives',
      'defaultMaxIncentiveValue',
      'defaultMaxIncentivePercentage',
      'isEligibleForIncentives',
      'iconUrl',
      'imageUrl',
      'sortOrder',
      'slug',
    ];

    for (const columnName of columnsToDrop) {
      const columnExists = await queryRunner.hasColumn('product_categories', columnName);
      if (columnExists) {
        await queryRunner.dropColumn('product_categories', columnName);
      }
    }
  }
}

