import { isValidCorporateEmail, formatCPF, validateCPF } from './validation.utils';

describe('Validation Utils', () => {
  describe('isValidCorporateEmail', () => {
    it('should accept valid corporate emails', () => {
      expect(isValidCorporateEmail('<EMAIL>')).toBe(true);
      expect(isValidCorporateEmail('<EMAIL>')).toBe(true);
      expect(isValidCorporateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject public email domains', () => {
      expect(isValidCorporateEmail('<EMAIL>')).toBe(false);
      expect(isValidCorporateEmail('<EMAIL>')).toBe(false);
      expect(isValidCorporateEmail('<EMAIL>')).toBe(false);
      expect(isValidCorporateEmail('<EMAIL>')).toBe(false);
      expect(isValidCorporateEmail('<EMAIL>')).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(isValidCorporateEmail('')).toBe(false);
      expect(isValidCorporateEmail('invalid-email')).toBe(false);
      expect(isValidCorporateEmail('@domain.com')).toBe(false);
      expect(isValidCorporateEmail('user@')).toBe(false);
    });
  });

  describe('formatCPF', () => {
    it('should format CPF correctly', () => {
      expect(formatCPF('12345678901')).toBe('123.456.789-01');
      expect(formatCPF('00000000000')).toBe('000.000.000-00');
    });

    it('should handle partial CPF', () => {
      expect(formatCPF('123')).toBe('123');
      expect(formatCPF('12345')).toBe('123.45');
      expect(formatCPF('12345678')).toBe('123.456.78');
    });

    it('should remove non-numeric characters', () => {
      expect(formatCPF('123.456.789-01')).toBe('123.456.789-01');
      expect(formatCPF('123abc456def789ghi01')).toBe('123.456.789-01');
    });

    it('should handle empty input', () => {
      expect(formatCPF('')).toBe('');
      expect(formatCPF('abc')).toBe('');
    });
  });

  describe('validateCPF', () => {
    it('should validate correct CPF format', () => {
      expect(validateCPF('123.456.789-01')).toBe(true);
      expect(validateCPF('000.000.000-00')).toBe(true);
    });

    it('should reject incorrect CPF format', () => {
      expect(validateCPF('12345678901')).toBe(false);
      expect(validateCPF('123.456.789')).toBe(false);
      expect(validateCPF('123.456.789-1')).toBe(false);
      expect(validateCPF('123.456.789-012')).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(validateCPF('')).toBe(false);
      expect(validateCPF('abc.def.ghi-jk')).toBe(false);
      expect(validateCPF('123.456.789-ab')).toBe(false);
    });
  });
});
