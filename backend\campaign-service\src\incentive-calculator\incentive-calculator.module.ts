import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IncentiveCalculatorService } from './incentive-calculator.service';
import { IncentiveCalculatorController } from './incentive-calculator.controller';
import { Product } from '../database/entities/product.entity';
import { ProductCategory } from '../database/entities/product-category.entity';
import { ValidationModule } from '../validation/validation.module';
import { IncentiveValidityModule } from '../incentive-validity/incentive-validity.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Product, ProductCategory]),
    ValidationModule,
    IncentiveValidityModule,
  ],
  controllers: [IncentiveCalculatorController],
  providers: [IncentiveCalculatorService],
  exports: [IncentiveCalculatorService],
})
export class IncentiveCalculatorModule {}
