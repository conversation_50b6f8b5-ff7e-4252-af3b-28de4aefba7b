import { IsEmail, IsString, IsOptional, IsBoolean, Matches, Length } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateIndustryProfileDto {
  @ApiProperty({
    description: 'E-mail corporativo da indústria (não aceita domínios públicos)',
    example: '<EMAIL>',
    pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
  })
  @IsEmail({}, { message: 'E-mail deve ter formato válido' })
  @Matches(/^[a-zA-Z0-9._%+-]+@(?!gmail\.com|hotmail\.com|yahoo\.com|outlook\.com|live\.com)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, {
    message: 'Use e-mail corporativo. <PERSON><PERSON><PERSON> públicos (gmail, hotmail, yahoo, outlook, live) não são permitidos.'
  })
  email: string;

  @ApiProperty({
    description: 'Nome da empresa/indústria',
    example: 'Indústria ABC Ltda',
    minLength: 2,
    maxLength: 100
  })
  @IsString({ message: 'Nome deve ser uma string' })
  @Length(2, 100, { message: 'Nome deve ter entre 2 e 100 caracteres' })
  name: string;

  @ApiProperty({
    description: 'Nome da empresa',
    example: 'Indústria ABC Ltda',
    minLength: 2,
    maxLength: 100
  })
  @IsString({ message: 'Empresa deve ser uma string' })
  @Length(2, 100, { message: 'Empresa deve ter entre 2 e 100 caracteres' })
  company: string;

  @ApiPropertyOptional({
    description: 'CPF do responsável (formato: 000.000.000-00)',
    example: '123.456.789-01',
    pattern: '^\\d{3}\\.\\d{3}\\.\\d{3}-\\d{2}$'
  })
  @IsOptional()
  @IsString({ message: 'CPF deve ser uma string' })
  @Matches(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, {
    message: 'CPF deve ter formato válido (000.000.000-00)'
  })
  cpf?: string;

  @ApiProperty({
    description: 'Aceite dos termos de uso (obrigatório)',
    example: true
  })
  @IsBoolean({ message: 'Aceite dos termos deve ser um boolean' })
  termsAccepted: boolean;

  @ApiProperty({
    description: 'Versão dos termos aceitos',
    example: '1.0.0'
  })
  @IsString({ message: 'Versão dos termos deve ser uma string' })
  termsVersion: string;

  @ApiPropertyOptional({
    description: 'Metadados adicionais da indústria',
    example: { segment: 'alimenticio', size: 'large' }
  })
  @IsOptional()
  metadata?: Record<string, any>;
}
