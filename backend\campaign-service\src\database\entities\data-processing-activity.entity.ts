import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('data_processing_activities')
export class DataProcessingActivity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ name: 'legal_basis' })
  legalBasis: string;

  @Column({ name: 'processing_purpose' })
  processingPurpose: string;

  @Column({ name: 'data_categories', type: 'jsonb' })
  dataCategories: string[];

  @Column({ name: 'data_subjects', type: 'jsonb' })
  dataSubjects: string[];

  @Column({ type: 'jsonb', nullable: true })
  recipients: string[];

  @Column({ name: 'retention_period', nullable: true })
  retentionPeriod: string;

  @Column({ name: 'security_measures', type: 'jsonb', nullable: true })
  securityMeasures: string[];

  @Column({ name: 'international_transfers', default: false })
  internationalTransfers: boolean;

  @Column({ name: 'transfer_safeguards', type: 'text', nullable: true })
  transferSafeguards: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'controller_name', nullable: true })
  controllerName: string;

  @Column({ name: 'controller_contact', nullable: true })
  controllerContact: string;

  @Column({ name: 'dpo_contact', nullable: true })
  dpoContact: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
