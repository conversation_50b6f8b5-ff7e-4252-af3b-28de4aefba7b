import { Injectable, Logger } from '@nestjs/common';
import { PriceType } from '../database/entities/price-types.entity';

interface CachedPrice {
  price: number;
  currency: string;
  lastUpdated: Date;
  provider: string;
  expiresAt: Date;
}

@Injectable()
export class PriceCacheService {
  private readonly logger = new Logger(PriceCacheService.name);
  private readonly cache = new Map<string, CachedPrice>();
  private readonly defaultTtlMinutes = 30;

  constructor() {
    // Clean up expired cache entries every 5 minutes
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, 5 * 60 * 1000);
  }

  async getPrice(
    productId: string,
    priceType: PriceType
  ): Promise<{ price: number; currency: string; lastUpdated: Date; provider: string } | null> {
    const key = this.getCacheKey(productId, priceType);
    const cached = this.cache.get(key);

    if (!cached) {
      return null;
    }

    // Check if expired
    if (new Date() > cached.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    this.logger.debug(`Cache hit for ${key}`);
    return {
      price: cached.price,
      currency: cached.currency,
      lastUpdated: cached.lastUpdated,
      provider: cached.provider,
    };
  }

  async setPrice(
    productId: string,
    priceType: PriceType,
    priceData: { price: number; currency: string; lastUpdated: Date; provider: string },
    ttlMinutes?: number
  ): Promise<void> {
    const key = this.getCacheKey(productId, priceType);
    const expiresAt = new Date(Date.now() + (ttlMinutes || this.defaultTtlMinutes) * 60 * 1000);

    this.cache.set(key, {
      ...priceData,
      expiresAt,
    });

    this.logger.debug(`Cached price for ${key}, expires at ${expiresAt.toISOString()}`);
  }

  async invalidatePrice(productId: string, priceType?: PriceType): Promise<void> {
    if (priceType) {
      const key = this.getCacheKey(productId, priceType);
      this.cache.delete(key);
      this.logger.debug(`Invalidated cache for ${key}`);
    } else {
      // Invalidate all price types for this product
      const keysToDelete = Array.from(this.cache.keys()).filter(key => 
        key.startsWith(`${productId}:`)
      );
      
      keysToDelete.forEach(key => this.cache.delete(key));
      this.logger.debug(`Invalidated all cached prices for product ${productId}`);
    }
  }

  async invalidateAll(): Promise<void> {
    this.cache.clear();
    this.logger.log('Cleared all cached prices');
  }

  async getPrices(
    productIds: string[],
    priceType: PriceType
  ): Promise<Record<string, { price: number; currency: string; lastUpdated: Date; provider: string } | null>> {
    const results: Record<string, any> = {};

    for (const productId of productIds) {
      results[productId] = await this.getPrice(productId, priceType);
    }

    return results;
  }

  async setPrices(
    prices: Array<{
      productId: string;
      priceType: PriceType;
      priceData: { price: number; currency: string; lastUpdated: Date; provider: string };
      ttlMinutes?: number;
    }>
  ): Promise<void> {
    for (const item of prices) {
      await this.setPrice(item.productId, item.priceType, item.priceData, item.ttlMinutes);
    }
  }

  getCacheStats(): {
    totalEntries: number;
    expiredEntries: number;
    hitRate: number;
    memoryUsage: string;
  } {
    const totalEntries = this.cache.size;
    const now = new Date();
    
    let expiredEntries = 0;
    for (const cached of this.cache.values()) {
      if (now > cached.expiresAt) {
        expiredEntries++;
      }
    }

    // Estimate memory usage (rough calculation)
    const avgEntrySize = 200; // bytes per entry (rough estimate)
    const memoryBytes = totalEntries * avgEntrySize;
    const memoryUsage = this.formatBytes(memoryBytes);

    return {
      totalEntries,
      expiredEntries,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      memoryUsage,
    };
  }

  private getCacheKey(productId: string, priceType: PriceType): string {
    return `${productId}:${priceType}`;
  }

  private cleanupExpiredEntries(): void {
    const now = new Date();
    let cleanedCount = 0;

    for (const [key, cached] of this.cache.entries()) {
      if (now > cached.expiresAt) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} expired cache entries`);
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Preload prices for a list of products
  async preloadPrices(
    productIds: string[],
    priceType: PriceType,
    priceProvider: (productId: string, priceType: PriceType) => Promise<{ price: number; currency: string; lastUpdated: Date; provider: string } | null>
  ): Promise<void> {
    this.logger.log(`Preloading prices for ${productIds.length} products`);

    const batchSize = 10;
    for (let i = 0; i < productIds.length; i += batchSize) {
      const batch = productIds.slice(i, i + batchSize);
      
      const promises = batch.map(async (productId) => {
        try {
          // Check if already cached and not expired
          const cached = await this.getPrice(productId, priceType);
          if (cached) {
            return; // Already cached
          }

          // Fetch and cache
          const priceData = await priceProvider(productId, priceType);
          if (priceData) {
            await this.setPrice(productId, priceType, priceData);
          }
        } catch (error) {
          this.logger.error(`Failed to preload price for product ${productId}:`, error);
        }
      });

      await Promise.all(promises);
    }

    this.logger.log('Price preloading completed');
  }

  // Warm up cache with frequently accessed products
  async warmupCache(
    frequentProductIds: string[],
    priceTypes: PriceType[] = [PriceType.REGULAR],
    priceProvider: (productId: string, priceType: PriceType) => Promise<{ price: number; currency: string; lastUpdated: Date; provider: string } | null>
  ): Promise<void> {
    this.logger.log(`Warming up cache for ${frequentProductIds.length} frequent products`);

    for (const priceType of priceTypes) {
      await this.preloadPrices(frequentProductIds, priceType, priceProvider);
    }

    this.logger.log('Cache warmup completed');
  }
}
