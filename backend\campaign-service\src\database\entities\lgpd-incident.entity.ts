import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum IncidentType {
  DATA_BREACH = 'data_breach',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  DATA_LOSS = 'data_loss',
  SYSTEM_COMPROMISE = 'system_compromise',
  PRIVACY_VIOLATION = 'privacy_violation',
  CONSENT_VIOLATION = 'consent_violation',
  RETENTION_VIOLATION = 'retention_violation',
  TRANSFER_VIOLATION = 'transfer_violation',
}

export enum IncidentSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum IncidentStatus {
  REPORTED = 'reported',
  INVESTIGATING = 'investigating',
  CONTAINED = 'contained',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

@Entity('lgpd_incidents')
export class LgpdIncident {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'enum', enum: IncidentType })
  type: IncidentType;

  @Column({ type: 'enum', enum: IncidentSeverity })
  severity: IncidentSeverity;

  @Column({ type: 'enum', enum: IncidentStatus, default: IncidentStatus.REPORTED })
  status: IncidentStatus;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ name: 'affected_data_subjects', type: 'integer', default: 0 })
  affectedDataSubjects: number;

  @Column({ name: 'data_categories_affected', type: 'jsonb', nullable: true })
  dataCategoriesAffected: string[];

  @Column({ name: 'potential_impact', type: 'text', nullable: true })
  potentialImpact: string;

  @Column({ name: 'root_cause', type: 'text', nullable: true })
  rootCause: string;

  @Column({ name: 'containment_actions', type: 'jsonb', nullable: true })
  containmentActions: string[];

  @Column({ name: 'remediation_actions', type: 'jsonb', nullable: true })
  remediationActions: string[];

  @Column({ name: 'notification_required', default: false })
  notificationRequired: boolean;

  @Column({ name: 'authority_notified', default: false })
  authorityNotified: boolean;

  @Column({ name: 'authority_notification_date', type: 'timestamp', nullable: true })
  authorityNotificationDate: Date;

  @Column({ name: 'data_subjects_notified', default: false })
  dataSubjectsNotified: boolean;

  @Column({ name: 'data_subjects_notification_date', type: 'timestamp', nullable: true })
  dataSubjectsNotificationDate: Date;

  @Column({ name: 'reported_by', nullable: true })
  reportedBy: string;

  @Column({ name: 'assigned_to', nullable: true })
  assignedTo: string;

  @Column({ name: 'incident_date', type: 'timestamp' })
  incidentDate: Date;

  @Column({ name: 'discovery_date', type: 'timestamp' })
  discoveryDate: Date;

  @Column({ name: 'resolution_date', type: 'timestamp', nullable: true })
  resolutionDate: Date;

  @Column({ name: 'lessons_learned', type: 'text', nullable: true })
  lessonsLearned: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Helper methods
  isHighRisk(): boolean {
    return this.severity === IncidentSeverity.HIGH || this.severity === IncidentSeverity.CRITICAL;
  }

  requiresAuthorityNotification(): boolean {
    return this.isHighRisk() || this.affectedDataSubjects > 100;
  }

  requiresDataSubjectNotification(): boolean {
    return this.isHighRisk() || this.affectedDataSubjects > 0;
  }

  getDaysToNotifyAuthority(): number {
    if (!this.requiresAuthorityNotification()) return 0;
    
    const discoveryDate = new Date(this.discoveryDate);
    const deadline = new Date(discoveryDate);
    deadline.setHours(deadline.getHours() + 72); // 72 hours = 3 days
    
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  }

  isOverdueForAuthorityNotification(): boolean {
    return this.requiresAuthorityNotification() && 
           !this.authorityNotified && 
           this.getDaysToNotifyAuthority() <= 0;
  }

  getStatusLabel(): string {
    const labels = {
      [IncidentStatus.REPORTED]: 'Reportado',
      [IncidentStatus.INVESTIGATING]: 'Investigando',
      [IncidentStatus.CONTAINED]: 'Contido',
      [IncidentStatus.RESOLVED]: 'Resolvido',
      [IncidentStatus.CLOSED]: 'Fechado',
    };
    return labels[this.status] || this.status;
  }

  getSeverityLabel(): string {
    const labels = {
      [IncidentSeverity.LOW]: 'Baixa',
      [IncidentSeverity.MEDIUM]: 'Média',
      [IncidentSeverity.HIGH]: 'Alta',
      [IncidentSeverity.CRITICAL]: 'Crítica',
    };
    return labels[this.severity] || this.severity;
  }

  getTypeLabel(): string {
    const labels = {
      [IncidentType.DATA_BREACH]: 'Vazamento de Dados',
      [IncidentType.UNAUTHORIZED_ACCESS]: 'Acesso Não Autorizado',
      [IncidentType.DATA_LOSS]: 'Perda de Dados',
      [IncidentType.SYSTEM_COMPROMISE]: 'Comprometimento do Sistema',
      [IncidentType.PRIVACY_VIOLATION]: 'Violação de Privacidade',
      [IncidentType.CONSENT_VIOLATION]: 'Violação de Consentimento',
      [IncidentType.RETENTION_VIOLATION]: 'Violação de Retenção',
      [IncidentType.TRANSFER_VIOLATION]: 'Violação de Transferência',
    };
    return labels[this.type] || this.type;
  }
}
