import { useState, useMemo } from 'react';
import { Product, FilterState } from '../types';

export function useProductFilters(products: Product[]) {
  const [filters, setFilters] = useState<FilterState>({
    searchTerm: '',
    selectedCategory: 'all',
    selectedBrand: 'all'
  });

  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      const matchesSearch = filters.searchTerm === '' || 
        product.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(filters.searchTerm.toLowerCase());

      const matchesCategory = filters.selectedCategory === 'all' || 
        product.category === filters.selectedCategory;

      const matchesBrand = filters.selectedBrand === 'all' || 
        product.brand === filters.selectedBrand;

      return matchesSearch && matchesCategory && matchesBrand;
    });
  }, [products, filters]);

  const categories = useMemo(() => 
    Array.from(new Set(products.map(p => p.category))), 
    [products]
  );

  const brands = useMemo(() => 
    Array.from(new Set(products.map(p => p.brand))), 
    [products]
  );

  const updateFilter = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  return {
    filters,
    filteredProducts,
    categories,
    brands,
    updateFilter
  };
}
