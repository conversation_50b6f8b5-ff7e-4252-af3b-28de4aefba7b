import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { 
  ProductSelectionService, 
  ProductSearchFilters, 
  CategorySearchFilters 
} from './product-selection.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';

export class ProductSelectionDto {
  productIds: string[];
}

export class ProductValidationDto {
  productIds: string[];
}

@ApiTags('Product Selection')
@Controller('product-selection')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProductSelectionController {
  constructor(private readonly productSelectionService: ProductSelectionService) {}

  @Get('products/search')
  @ApiOperation({ summary: 'Buscar produtos com filtros avançados' })
  @ApiQuery({ name: 'search', required: false, description: 'Termo de busca (nome, SKU, código de barras)' })
  @ApiQuery({ name: 'categoryId', required: false, description: 'ID da categoria' })
  @ApiQuery({ name: 'industryId', required: false, description: 'ID da indústria' })
  @ApiQuery({ name: 'brandId', required: false, description: 'ID da marca' })
  @ApiQuery({ name: 'isEligibleForIncentives', required: false, description: 'Elegível para incentivos' })
  @ApiQuery({ name: 'priceMin', required: false, description: 'Preço mínimo' })
  @ApiQuery({ name: 'priceMax', required: false, description: 'Preço máximo' })
  @ApiQuery({ name: 'inStock', required: false, description: 'Apenas produtos em estoque' })
  @ApiQuery({ name: 'page', required: false, description: 'Página (padrão: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Itens por página (padrão: 20, máx: 100)' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Ordenar por: name, price, createdAt, salesCount, averageRating' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Ordem: ASC ou DESC' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de produtos encontrados',
    schema: {
      type: 'object',
      properties: {
        products: { type: 'array', items: { type: 'object' } },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
        hasNext: { type: 'boolean' },
        hasPrev: { type: 'boolean' }
      }
    }
  })
  async searchProducts(@Query() filters: ProductSearchFilters) {
    return this.productSelectionService.searchProducts(filters);
  }

  @Get('categories/search')
  @ApiOperation({ summary: 'Buscar categorias de produtos' })
  @ApiQuery({ name: 'search', required: false, description: 'Termo de busca' })
  @ApiQuery({ name: 'industryId', required: false, description: 'ID da indústria' })
  @ApiQuery({ name: 'parentId', required: false, description: 'ID da categoria pai' })
  @ApiQuery({ name: 'level', required: false, description: 'Nível da categoria' })
  @ApiQuery({ name: 'isEligibleForIncentives', required: false, description: 'Elegível para incentivos' })
  @ApiQuery({ name: 'page', required: false, description: 'Página (padrão: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Itens por página (padrão: 50, máx: 100)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de categorias encontradas',
    schema: {
      type: 'object',
      properties: {
        categories: { type: 'array', items: { type: 'object' } },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
        hasNext: { type: 'boolean' },
        hasPrev: { type: 'boolean' }
      }
    }
  })
  async searchCategories(@Query() filters: CategorySearchFilters) {
    return this.productSelectionService.searchCategories(filters);
  }

  @Get('products/:id')
  @ApiOperation({ summary: 'Obter produto por ID' })
  @ApiResponse({ status: 200, description: 'Produto encontrado' })
  @ApiResponse({ status: 404, description: 'Produto não encontrado' })
  async getProduct(@Param('id') id: string) {
    const product = await this.productSelectionService.getProductById(id);
    if (!product) {
      return { error: 'Produto não encontrado' };
    }
    return product;
  }

  @Get('categories/:id')
  @ApiOperation({ summary: 'Obter categoria por ID' })
  @ApiResponse({ status: 200, description: 'Categoria encontrada' })
  @ApiResponse({ status: 404, description: 'Categoria não encontrada' })
  async getCategory(@Param('id') id: string) {
    const category = await this.productSelectionService.getCategoryById(id);
    if (!category) {
      return { error: 'Categoria não encontrada' };
    }
    return category;
  }

  @Get('categories/:categoryId/products')
  @ApiOperation({ summary: 'Obter produtos de uma categoria' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de produtos (padrão: 20)' })
  @ApiResponse({ status: 200, description: 'Produtos da categoria' })
  async getProductsByCategory(
    @Param('categoryId') categoryId: string,
    @Query('limit') limit?: number,
  ) {
    return this.productSelectionService.getProductsByCategory(categoryId, limit);
  }

  @Get('categories/tree/:industryId')
  @ApiOperation({ summary: 'Obter árvore de categorias de uma indústria' })
  @ApiResponse({ 
    status: 200, 
    description: 'Árvore de categorias',
    schema: {
      type: 'array',
      items: { type: 'object' }
    }
  })
  async getCategoryTree(@Param('industryId') industryId: string) {
    return this.productSelectionService.getCategoryTree(industryId);
  }

  @Post('summary')
  @ApiOperation({ summary: 'Obter resumo da seleção de produtos' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resumo da seleção',
    schema: {
      type: 'object',
      properties: {
        selectedProducts: { type: 'array', items: { type: 'object' } },
        totalProducts: { type: 'number' },
        totalValue: { type: 'number' },
        averagePrice: { type: 'number' },
        categoriesRepresented: { type: 'array', items: { type: 'string' } },
        eligibleForIncentives: { type: 'number' },
        inStockCount: { type: 'number' },
        warnings: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async getSelectionSummary(@Body() selectionDto: ProductSelectionDto) {
    return this.productSelectionService.getProductSelectionSummary(selectionDto.productIds);
  }

  @Post('validate')
  @ApiOperation({ summary: 'Validar seleção de produtos' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultado da validação',
    schema: {
      type: 'object',
      properties: {
        isValid: { type: 'boolean' },
        errors: { type: 'array', items: { type: 'string' } },
        warnings: { type: 'array', items: { type: 'string' } },
        validProducts: { type: 'array', items: { type: 'object' } },
        invalidProducts: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async validateSelection(@Body() validationDto: ProductValidationDto) {
    return this.productSelectionService.validateProductSelection(validationDto.productIds);
  }

  @Get('products/popular/:industryId')
  @ApiOperation({ summary: 'Obter produtos populares de uma indústria' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de produtos (padrão: 10)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Produtos populares',
    schema: {
      type: 'array',
      items: { type: 'object' }
    }
  })
  async getPopularProducts(
    @Param('industryId') industryId: string,
    @Query('limit') limit?: number,
  ) {
    return this.productSelectionService.getPopularProducts(industryId, limit);
  }

  @Get('products/featured/:industryId')
  @ApiOperation({ summary: 'Obter produtos em destaque de uma indústria' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de produtos (padrão: 10)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Produtos em destaque',
    schema: {
      type: 'array',
      items: { type: 'object' }
    }
  })
  async getFeaturedProducts(
    @Param('industryId') industryId: string,
    @Query('limit') limit?: number,
  ) {
    return this.productSelectionService.getFeaturedProducts(industryId, limit);
  }

  @Get('products/recent/:industryId')
  @ApiOperation({ summary: 'Obter produtos recentes de uma indústria' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de produtos (padrão: 10)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Produtos recentes',
    schema: {
      type: 'array',
      items: { type: 'object' }
    }
  })
  async getRecentProducts(
    @Param('industryId') industryId: string,
    @Query('limit') limit?: number,
  ) {
    return this.productSelectionService.getRecentProducts(industryId, limit);
  }

  @Post('products/by-sku')
  @ApiOperation({ summary: 'Obter produtos por lista de SKUs' })
  @ApiResponse({ 
    status: 200, 
    description: 'Produtos encontrados',
    schema: {
      type: 'array',
      items: { type: 'object' }
    }
  })
  async getProductsBySku(@Body() skuDto: { skus: string[] }) {
    return this.productSelectionService.getProductsBySku(skuDto.skus);
  }

  @Get('quick-search')
  @ApiOperation({ summary: 'Busca rápida de produtos (apenas nome e SKU)' })
  @ApiQuery({ name: 'q', required: true, description: 'Termo de busca' })
  @ApiQuery({ name: 'industryId', required: false, description: 'ID da indústria' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de resultados (padrão: 10)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Resultados da busca rápida',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          sku: { type: 'string' },
          name: { type: 'string' },
          price: { type: 'number' },
          isEligibleForIncentives: { type: 'boolean' },
          stockStatus: { type: 'string' }
        }
      }
    }
  })
  async quickSearch(
    @Query('q') query: string,
    @Query('industryId') industryId?: string,
    @Query('limit') limit?: number,
  ) {
    try {
      // Implementação simplificada para demonstração
      return [
        {
          id: 'demo-1',
          sku: 'SB001',
          name: 'Sabonete Líquido',
          price: 8.75,
          isEligibleForIncentives: true,
          stockStatus: 'in_stock'
        },
        {
          id: 'demo-2',
          sku: 'SH001',
          name: 'Shampoo Anticaspa',
          price: 15.99,
          isEligibleForIncentives: true,
          stockStatus: 'in_stock'
        }
      ].filter(p => p.name.toLowerCase().includes(query?.toLowerCase() || ''));
    } catch (error) {
      console.error('Error in quickSearch:', error);
      return [];
    }
  }
}
