'use client';

import { useState, useEffect } from 'react';
import { DatePicker } from '@/components/ui/date-picker';
import { Calendar, X, Check } from 'lucide-react';

export interface PeriodFilter {
  type: 'preset' | 'custom';
  preset?: 'all' | '1d' | '7d' | '30d' | '60d' | '90d';
  startDate?: string;
  endDate?: string;
}

interface PeriodSelectorProps {
  period: PeriodFilter;
  onPeriodChange: (period: PeriodFilter) => void;
  isOpen: boolean;
  onClose: () => void;
}

export default function PeriodSelector({ 
  period, 
  onPeriodChange, 
  isOpen, 
  onClose 
}: PeriodSelectorProps) {
  const [selectedType, setSelectedType] = useState<'preset' | 'custom'>(period.type);
  const [selectedPreset, setSelectedPreset] = useState<string>(period.preset || 'all');
  const [startDate, setStartDate] = useState(period.startDate || '');
  const [endDate, setEndDate] = useState(period.endDate || '');

  // Sync local state with props when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedType(period.type);
      setSelectedPreset(period.preset || 'all');
      setStartDate(period.startDate || '');
      setEndDate(period.endDate || '');
    }
  }, [isOpen, period]);

  const presetOptions = [
    { value: 'all', label: 'Todos os períodos', description: 'Todos os dados disponíveis' },
    { value: '1d', label: 'Último dia', description: 'Últimas 24 horas' },
    { value: '7d', label: 'Últimos 7 dias', description: 'Uma semana' },
    { value: '30d', label: 'Últimos 30 dias', description: 'Um mês' },
    { value: '60d', label: 'Últimos 60 dias', description: 'Dois meses' },
    { value: '90d', label: 'Últimos 90 dias', description: 'Três meses' }
  ];

  const handleApply = () => {
    if (selectedType === 'preset') {
      const newPeriod = {
        type: 'preset',
        preset: selectedPreset as any
      };
      console.log('📅 Aplicando período preset:', newPeriod);
      onPeriodChange(newPeriod);
    } else {
      if (!startDate || !endDate) {
        alert('Por favor, selecione as datas de início e fim');
        return;
      }

      if (new Date(startDate) > new Date(endDate)) {
        alert('A data de início deve ser anterior à data de fim');
        return;
      }

      const newPeriod = {
        type: 'custom',
        startDate,
        endDate
      };
      console.log('📅 Aplicando período customizado:', newPeriod);
      onPeriodChange(newPeriod);
    }
    onClose();
  };

  const handlePresetSelect = (preset: string) => {
    setSelectedPreset(preset);
    setSelectedType('preset');
  };

  const handleCustomDateChange = () => {
    setSelectedType('custom');
  };

  const formatDateForInput = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  const getPresetDateRange = (preset: string) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (preset) {
      case '1d':
        return {
          start: new Date(today.getTime() - 24 * 60 * 60 * 1000),
          end: now
        };
      case '7d':
        return {
          start: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000),
          end: now
        };
      case '30d':
        return {
          start: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000),
          end: now
        };
      case '60d':
        return {
          start: new Date(today.getTime() - 60 * 24 * 60 * 60 * 1000),
          end: now
        };
      case '90d':
        return {
          start: new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000),
          end: now
        };
      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-lg mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex flex-row items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Selecionar Período
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          >
            <X className="h-4 w-4 text-gray-500" />
          </button>
        </div>

        <div className="p-4 space-y-4 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Preset Options */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-900">Períodos Predefinidos</label>
            <div className="space-y-1">
              {presetOptions.map(option => {
                const dateRange = getPresetDateRange(option.value);
                return (
                  <div
                    key={option.value}
                    className={`
                      flex items-center justify-between p-2 rounded-md border cursor-pointer transition-colors
                      ${selectedType === 'preset' && selectedPreset === option.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }
                    `}
                    onClick={() => handlePresetSelect(option.value)}
                  >
                    <div className="flex-1">
                      <div className="font-medium text-sm">{option.label}</div>
                      <div className="text-xs text-gray-500">
                        {option.description}
                        {dateRange && option.value !== 'all' && (
                          <span className="block">
                            {dateRange.start.toLocaleDateString()} - {dateRange.end.toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                    {selectedType === 'preset' && selectedPreset === option.value && (
                      <Check className="h-4 w-4 text-blue-600" />
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Custom Date Range */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-900">Período Personalizado</label>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <label htmlFor="start-date" className="text-xs text-gray-700">Data de Início</label>
                <DatePicker
                  id="start-date"
                  value={startDate}
                  onChange={(value) => {
                    setStartDate(value);
                    handleCustomDateChange();
                  }}
                  placeholder="Data de início"
                  className="text-sm"
                />
              </div>
              <div className="space-y-1">
                <label htmlFor="end-date" className="text-xs text-gray-700">Data de Fim</label>
                <DatePicker
                  id="end-date"
                  value={endDate}
                  onChange={(value) => {
                    setEndDate(value);
                    handleCustomDateChange();
                  }}
                  placeholder="Data de fim"
                  className="text-sm"
                />
              </div>
            </div>
          </div>

          {/* Current Selection Summary */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="text-sm font-medium text-gray-700 mb-1">Período Selecionado:</div>
            <div className="text-sm text-gray-600">
              {selectedType === 'preset' ? (
                presetOptions.find(opt => opt.value === selectedPreset)?.label || 'Nenhum'
              ) : (
                startDate && endDate ? (
                  `${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`
                ) : (
                  'Selecione as datas'
                )
              )}
            </div>
          </div>

        </div>

        {/* Actions - Fixed at bottom */}
        <div className="flex justify-end space-x-2 p-4 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancelar
          </button>
          <button
            onClick={handleApply}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Aplicar Período
          </button>
        </div>
      </div>
    </div>
  );
}
