import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMissingProductColumns1703000000002 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add missing columns to products table
    await queryRunner.query(`
      ALTER TABLE "products" 
      ADD COLUMN IF NOT EXISTS "currency" character varying(10),
      ADD COLUMN IF NOT EXISTS "weightUnit" character varying(10),
      ADD COLUMN IF NOT EXISTS "supplier" character varying(255),
      ADD COLUMN IF NOT EXISTS "supplierSku" character varying(100),
      ADD COLUMN IF NOT EXISTS "launchDate" TIMESTAMP,
      ADD COLUMN IF NOT EXISTS "discontinueDate" TIMESTAMP,
      ADD COLUMN IF NOT EXISTS "isFeatured" boolean NOT NULL DEFAULT false,
      ADD COLUMN IF NOT EXISTS "isPromotional" boolean NOT NULL DEFAULT false,
      ADD COLUMN IF NOT EXISTS "createdBy" character varying(255),
      ADD COLUMN IF NOT EXISTS "updatedBy" character varying(255)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the added columns
    await queryRunner.query(`
      ALTER TABLE "products" 
      DROP COLUMN IF EXISTS "currency",
      DROP COLUMN IF EXISTS "weightUnit",
      DROP COLUMN IF EXISTS "supplier",
      DROP COLUMN IF EXISTS "supplierSku",
      DROP COLUMN IF EXISTS "launchDate",
      DROP COLUMN IF EXISTS "discontinueDate",
      DROP COLUMN IF EXISTS "isFeatured",
      DROP COLUMN IF EXISTS "isPromotional",
      DROP COLUMN IF EXISTS "createdBy",
      DROP COLUMN IF EXISTS "updatedBy"
    `);
  }
}
