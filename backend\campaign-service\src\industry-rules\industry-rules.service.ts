import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IndustryRule, RuleType, RuleStatus } from '../database/entities/industry-rules.entity';
import { CreateIndustryRuleDto, UpdateIndustryRuleDto } from './dto/industry-rules.dto';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

@Injectable()
export class IndustryRulesService {
  constructor(
    @InjectRepository(IndustryRule)
    private readonly industryRuleRepository: Repository<IndustryRule>,
  ) {}

  async create(createDto: CreateIndustryRuleDto, createdBy: string): Promise<IndustryRule> {
    // Check if rule already exists for this industry and type
    const existingRule = await this.industryRuleRepository.findOne({
      where: {
        industryId: createDto.industryId,
        ruleType: createDto.ruleType,
      },
    });

    if (existingRule) {
      throw new BadRequestException(
        `Regra do tipo ${createDto.ruleType} já existe para esta indústria`
      );
    }

    // Validate rule values
    this.validateRuleValues(createDto);

    const rule = this.industryRuleRepository.create({
      ...createDto,
      createdBy,
    });

    return this.industryRuleRepository.save(rule);
  }

  async findAll(industryId?: string): Promise<IndustryRule[]> {
    const where = industryId ? { industryId } : {};
    return this.industryRuleRepository.find({
      where,
      order: { ruleType: 'ASC', createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<IndustryRule> {
    const rule = await this.industryRuleRepository.findOne({ where: { id } });
    if (!rule) {
      throw new NotFoundException('Regra não encontrada');
    }
    return rule;
  }

  async findByIndustryAndType(industryId: string, ruleType: RuleType): Promise<IndustryRule | null> {
    return this.industryRuleRepository.findOne({
      where: { industryId, ruleType },
    });
  }

  async update(id: string, updateDto: UpdateIndustryRuleDto, updatedBy: string): Promise<IndustryRule> {
    const rule = await this.findOne(id);

    // Validate rule values if provided
    if (updateDto.minValue !== undefined || updateDto.maxValue !== undefined ||
        updateDto.minPercentage !== undefined || updateDto.maxPercentage !== undefined ||
        updateDto.integerValue !== undefined) {
      this.validateRuleValues({ ...rule, ...updateDto });
    }

    Object.assign(rule, updateDto, { updatedBy });
    return this.industryRuleRepository.save(rule);
  }

  async delete(id: string): Promise<void> {
    const rule = await this.findOne(id);
    await this.industryRuleRepository.remove(rule);
  }

  async validateCampaignIncentive(
    industryId: string,
    incentivePercentage: number,
    incentiveValue: number,
    productCount: number,
    validityDays: number
  ): Promise<ValidationResult> {
    const rules = await this.findAll(industryId);
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const rule of rules) {
      if (!rule.enabled || rule.status !== RuleStatus.ACTIVE) {
        continue;
      }

      switch (rule.ruleType) {
        case RuleType.MINIMUM_PERCENTAGE:
          if (!rule.validateMinimumPercentage(incentivePercentage)) {
            errors.push(rule.getValidationErrorMessage(incentivePercentage, 'percentage'));
          }
          break;

        case RuleType.MINIMUM_VALUE:
          if (!rule.validateMinimumValue(incentiveValue)) {
            errors.push(rule.getValidationErrorMessage(incentiveValue, 'value'));
          }
          break;

        case RuleType.MAXIMUM_PERCENTAGE:
          if (!rule.validateMaximumPercentage(incentivePercentage)) {
            errors.push(rule.getValidationErrorMessage(incentivePercentage, 'percentage'));
          }
          break;

        case RuleType.MAXIMUM_VALUE:
          if (!rule.validateMaximumValue(incentiveValue)) {
            errors.push(rule.getValidationErrorMessage(incentiveValue, 'value'));
          }
          break;

        case RuleType.PRODUCT_LIMIT_PER_CYCLE:
          if (!rule.validateProductLimit(productCount)) {
            errors.push(rule.getValidationErrorMessage(productCount, 'count'));
          }
          break;

        case RuleType.INCENTIVE_VALIDITY_DAYS:
          if (!rule.validateIncentiveValidity(validityDays)) {
            errors.push(rule.getValidationErrorMessage(validityDays, 'days'));
          }
          break;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  async getDefaultIncentiveValidity(industryId: string): Promise<number> {
    const rule = await this.findByIndustryAndType(industryId, RuleType.INCENTIVE_VALIDITY_DAYS);
    return rule?.enabled && rule.status === RuleStatus.ACTIVE ? rule.integerValue : 30; // Default 30 days
  }

  async getIndustryLimits(industryId: string) {
    const rules = await this.findAll(industryId);
    const limits = {
      minPercentage: null,
      maxPercentage: null,
      minValue: null,
      maxValue: null,
      productLimitPerCycle: null,
      incentiveValidityDays: 30,
    };

    for (const rule of rules) {
      if (!rule.enabled || rule.status !== RuleStatus.ACTIVE) {
        continue;
      }

      switch (rule.ruleType) {
        case RuleType.MINIMUM_PERCENTAGE:
          limits.minPercentage = rule.minPercentage;
          break;
        case RuleType.MAXIMUM_PERCENTAGE:
          limits.maxPercentage = rule.maxPercentage;
          break;
        case RuleType.MINIMUM_VALUE:
          limits.minValue = rule.minValue;
          break;
        case RuleType.MAXIMUM_VALUE:
          limits.maxValue = rule.maxValue;
          break;
        case RuleType.PRODUCT_LIMIT_PER_CYCLE:
          limits.productLimitPerCycle = rule.integerValue;
          break;
        case RuleType.INCENTIVE_VALIDITY_DAYS:
          limits.incentiveValidityDays = rule.integerValue;
          break;
      }
    }

    return limits;
  }

  private validateRuleValues(ruleData: any): void {
    // Validate percentage ranges
    if (ruleData.minPercentage !== undefined && ruleData.minPercentage !== null) {
      if (ruleData.minPercentage < 0.1 || ruleData.minPercentage > 100) {
        throw new BadRequestException('Percentual mínimo deve ser entre 0,1% e 100%');
      }
    }

    if (ruleData.maxPercentage !== undefined && ruleData.maxPercentage !== null) {
      if (ruleData.maxPercentage < 0.1 || ruleData.maxPercentage > 100) {
        throw new BadRequestException('Percentual máximo deve ser entre 0,1% e 100%');
      }
    }

    // Validate value ranges
    if (ruleData.minValue !== undefined && ruleData.minValue !== null) {
      if (ruleData.minValue < 0) {
        throw new BadRequestException('Valor mínimo deve ser positivo');
      }
    }

    if (ruleData.maxValue !== undefined && ruleData.maxValue !== null) {
      if (ruleData.maxValue < 0) {
        throw new BadRequestException('Valor máximo deve ser positivo');
      }
    }

    // Validate integer values
    if (ruleData.integerValue !== undefined && ruleData.integerValue !== null) {
      if (ruleData.integerValue < 1) {
        throw new BadRequestException('Valor deve ser maior que zero');
      }

      if (ruleData.ruleType === RuleType.INCENTIVE_VALIDITY_DAYS && ruleData.integerValue > 365) {
        throw new BadRequestException('Validade deve ser entre 1 e 365 dias');
      }
    }

    // Validate min/max relationships
    if (ruleData.minPercentage !== undefined && ruleData.maxPercentage !== undefined &&
        ruleData.minPercentage !== null && ruleData.maxPercentage !== null) {
      if (ruleData.minPercentage > ruleData.maxPercentage) {
        throw new BadRequestException('Percentual mínimo não pode ser maior que o máximo');
      }
    }

    if (ruleData.minValue !== undefined && ruleData.maxValue !== undefined &&
        ruleData.minValue !== null && ruleData.maxValue !== null) {
      if (ruleData.minValue > ruleData.maxValue) {
        throw new BadRequestException('Valor mínimo não pode ser maior que o máximo');
      }
    }
  }
}
