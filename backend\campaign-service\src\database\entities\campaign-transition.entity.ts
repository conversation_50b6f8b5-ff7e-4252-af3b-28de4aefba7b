import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Campaign, CampaignStatus } from './campaign.entity';

export enum TransitionType {
  MANUAL = 'manual',
  SCHEDULED = 'scheduled',
  AUTOMATIC = 'automatic',
  SYSTEM = 'system',
}

export enum TransitionAction {
  ACTIVATE = 'activate',
  PAUSE = 'pause',
  RESUME = 'resume',
  END = 'end',
  REACTIVATE = 'reactivate',
  SCHEDULE = 'schedule',
}

export enum TransitionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

@Entity('campaign_transitions')
@Index(['campaignId'])
@Index(['status'])
@Index(['executeAt'])
@Index(['type'])
@Index(['action'])
export class CampaignTransition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  campaignId: string;

  @Column({
    type: 'varchar',
    length: 50,
  })
  action: TransitionAction;

  @Column({
    type: 'varchar',
    length: 50,
    default: TransitionType.MANUAL,
  })
  type: TransitionType;

  @Column({
    type: 'varchar',
    length: 50,
    default: TransitionStatus.PENDING,
  })
  status: TransitionStatus;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  fromStatus: CampaignStatus | null;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  toStatus: CampaignStatus | null;

  @Column({ type: 'timestamp', nullable: true })
  executeAt: Date | null;

  @Column({ type: 'timestamp', nullable: true })
  executedAt: Date | null;

  @Column({ type: 'uuid', nullable: true })
  triggeredBy: string | null;

  @Column({ type: 'varchar', length: 500, nullable: true })
  reason: string | null;

  @Column({ type: 'text', nullable: true })
  errorMessage: string | null;

  @Column({ type: 'integer', default: 0 })
  retryCount: number;

  @Column({ type: 'integer', default: 3 })
  maxRetries: number;

  @Column({ type: 'timestamp', nullable: true })
  nextRetryAt: Date | null;

  @Column({ type: 'jsonb', nullable: true })
  parameters: Record<string, any> | null;

  @Column({ type: 'jsonb', nullable: true })
  result: Record<string, any> | null;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  @Column({ type: 'varchar', length: 255, nullable: true })
  jobId: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true })
  correlationId: string | null;

  @Column({ type: 'boolean', default: false })
  isIdempotent: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  idempotencyKey: string | null;

  @CreateDateColumn()
  createdAt: Date;

  // Relations
  @ManyToOne(() => Campaign, (campaign) => campaign.transitions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'campaignId' })
  campaign: Campaign;

  // Computed properties
  get isPending(): boolean {
    return this.status === TransitionStatus.PENDING;
  }

  get isProcessing(): boolean {
    return this.status === TransitionStatus.PROCESSING;
  }

  get isCompleted(): boolean {
    return this.status === TransitionStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === TransitionStatus.FAILED;
  }

  get isCancelled(): boolean {
    return this.status === TransitionStatus.CANCELLED;
  }

  get isScheduled(): boolean {
    return !!this.executeAt && this.executeAt > new Date();
  }

  get isOverdue(): boolean {
    return !!this.executeAt && this.executeAt <= new Date() && this.isPending;
  }

  get canRetry(): boolean {
    return this.isFailed && this.retryCount < this.maxRetries;
  }

  get shouldRetry(): boolean {
    return (
      this.canRetry &&
      (!this.nextRetryAt || (this.nextRetryAt instanceof Date && this.nextRetryAt <= new Date()))
    );
  }

  // Business methods
  markAsProcessing(): void {
    this.status = TransitionStatus.PROCESSING;
    this.executedAt = new Date();
  }

  markAsCompleted(result?: Record<string, any>): void {
    this.status = TransitionStatus.COMPLETED;
    this.executedAt = new Date();
    if (result) {
      this.result = result;
    }
  }

  markAsFailed(errorMessage: string, shouldRetry: boolean = true): void {
    this.status = TransitionStatus.FAILED;
    this.errorMessage = errorMessage;
    this.executedAt = new Date();

    if (shouldRetry && this.canRetry) {
      this.retryCount += 1;
      // Exponential backoff: 2^retryCount minutes
      const delayMinutes = Math.pow(2, this.retryCount);
      this.nextRetryAt = new Date(Date.now() + delayMinutes * 60 * 1000);
    }
  }

  markAsCancelled(reason?: string): void {
    this.status = TransitionStatus.CANCELLED;
    if (reason) {
      this.reason = reason;
    }
  }

  schedule(executeAt: Date): void {
    this.executeAt = executeAt;
    this.status = TransitionStatus.PENDING;
  }

  reschedule(newExecuteAt: Date): void {
    if (this.isCompleted) {
      throw new Error('Cannot reschedule completed transition');
    }
    this.executeAt = newExecuteAt;
    this.status = TransitionStatus.PENDING;
    this.errorMessage = null;
    this.retryCount = 0;
    this.nextRetryAt = null;
  }

  cancel(reason?: string): void {
    if (this.isCompleted) {
      throw new Error('Cannot cancel completed transition');
    }
    this.markAsCancelled(reason);
  }

  setIdempotencyKey(key: string): void {
    this.isIdempotent = true;
    this.idempotencyKey = key;
  }

  updateMetadata(metadata: Record<string, any>): void {
    this.metadata = { ...((this.metadata as Record<string, any>) || {}), ...metadata };
  }

  static createTransition(data: {
    campaignId: string;
    action: TransitionAction;
    type?: TransitionType;
    executeAt?: Date;
    triggeredBy?: string;
    reason?: string;
    parameters?: Record<string, any>;
    fromStatus?: CampaignStatus;
    toStatus?: CampaignStatus;
  }): CampaignTransition {
    const transition = new CampaignTransition();
    
    transition.campaignId = data.campaignId;
    transition.action = data.action;
    transition.type = data.type || TransitionType.MANUAL;
    transition.executeAt = data.executeAt;
    transition.triggeredBy = data.triggeredBy;
    transition.reason = data.reason;
    transition.parameters = data.parameters;
    transition.fromStatus = data.fromStatus;
    transition.toStatus = data.toStatus;

    // Generate correlation ID for tracking
    transition.correlationId = `${data.campaignId}-${data.action}-${Date.now()}`;

    return transition;
  }

  static createScheduledTransition(
    campaignId: string,
    action: TransitionAction,
    executeAt: Date,
    triggeredBy?: string,
    parameters?: Record<string, any>,
  ): CampaignTransition {
    return this.createTransition({
      campaignId,
      action,
      type: TransitionType.SCHEDULED,
      executeAt,
      triggeredBy,
      parameters,
    });
  }

  static createAutomaticTransition(
    campaignId: string,
    action: TransitionAction,
    reason: string,
    parameters?: Record<string, any>,
  ): CampaignTransition {
    return this.createTransition({
      campaignId,
      action,
      type: TransitionType.AUTOMATIC,
      reason,
      parameters,
      executeAt: new Date(), // Execute immediately
    });
  }
}






