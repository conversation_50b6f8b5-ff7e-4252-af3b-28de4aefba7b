import { Injectable } from '@nestjs/common';
import { RateLimitInfo } from '../domain/estimation-result';

/**
 * Service responsible for rate limiting estimation requests
 * Follows Single Responsibility Principle
 */
@Injectable()
export class RateLimiterService {
  private readonly rateLimitMap = new Map<string, { count: number; resetTime: number }>();
  private readonly REQUESTS_PER_HOUR = 100;
  private readonly HOUR_IN_MS = 60 * 60 * 1000;

  /**
   * Check if the industry has exceeded rate limits
   * @param industryId - The industry identifier
   * @returns Rate limit information
   */
  checkRateLimit(industryId: string): RateLimitInfo {
    const now = Date.now();
    const existingData = this.rateLimitMap.get(industryId);

    if (!existingData || now > existingData.resetTime) {
      const newData = { count: 1, resetTime: now + this.HOUR_IN_MS };
      this.rateLimitMap.set(industryId, newData);
      return RateLimitInfo.create(1, newData.resetTime, this.REQUESTS_PER_HOUR);
    }

    existingData.count++;
    return RateLimitInfo.create(
      existingData.count, 
      existingData.resetTime, 
      this.REQUESTS_PER_HOUR
    );
  }

  /**
   * Get remaining time until rate limit reset
   * @param industryId - The industry identifier
   * @returns Minutes until reset
   */
  getResetTimeInMinutes(industryId: string): number {
    const data = this.rateLimitMap.get(industryId);
    if (!data) return 0;
    
    const now = Date.now();
    const remainingMs = Math.max(0, data.resetTime - now);
    return Math.ceil(remainingMs / (60 * 1000));
  }

  /**
   * Clear rate limit data for testing purposes
   * @param industryId - The industry identifier
   */
  clearRateLimit(industryId: string): void {
    this.rateLimitMap.delete(industryId);
  }
}
