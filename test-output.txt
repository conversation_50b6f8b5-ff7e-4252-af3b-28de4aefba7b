﻿
> campaign-service@1.0.0 test:e2e
> jest --config ./test/jest-e2e.json --verbose

  console.log
    query: SELECT version()

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT version()

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT * FROM current_schema()

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT * FROM current_schema()

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: CREATE EXTENSION IF NOT EXISTS "uuid-ossp"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT COUNT(1) AS "cnt" FROM "attribution_models" "AttributionModelConfig"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)
          at async Promise.all (index 0)

  console.log
    query: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id") "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 20 OFFSET 0

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id") "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 20 OFFSET 0

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

docker : [31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation 
"campaign_products" does not exist[39m
At line:1 char:1
+ docker exec retail-media-campaign-service npm run test:e2e -- --verbo ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: ([31m[Nest] 977... not exist[39m:String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.getRawMany (/app/src/query-builder/SelectQueryBuilder.ts:1627:29)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3530:26)
    at SelectQueryBuilder.getManyAndCount (/app/src/query-builder/SelectQueryBuilder.ts:1874:36)
    at CampaignsService.findAll (/app/src/campaigns/campaigns.service.ts:99:32)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."status" = $1) "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 20 OFFSET 0 -- PARAMETERS: ["active"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."status" = $1) "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 20 OFFSET 0 -- PARAMETERS: ["active"]

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation "campaign_products" 
does not exist[39m
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.getRawMany (/app/src/query-builder/SelectQueryBuilder.ts:1627:29)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3530:26)
    at SelectQueryBuilder.getManyAndCount (/app/src/query-builder/SelectQueryBuilder.ts:1874:36)
    at CampaignsService.findAll (/app/src/campaigns/campaigns.service.ts:99:32)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id") "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 10 OFFSET 0

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id") "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 10 OFFSET 0

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation "campaign_products" 
does not exist[39m
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.getRawMany (/app/src/query-builder/SelectQueryBuilder.ts:1627:29)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3530:26)
    at SelectQueryBuilder.getManyAndCount (/app/src/query-builder/SelectQueryBuilder.ts:1874:36)
    at CampaignsService.findAll (/app/src/campaigns/campaigns.service.ts:99:32)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation "campaign_products" 
does not exist[39m
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3614:26)
    at SelectQueryBuilder.getRawAndEntities (/app/src/query-builder/SelectQueryBuilder.ts:1671:29)
    at SelectQueryBuilder.getOne (/app/src/query-builder/SelectQueryBuilder.ts:1698:25)
    at CampaignsService.findOne (/app/src/campaigns/campaigns.service.ts:124:22)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: CREATE EXTENSION IF NOT EXISTS "uuid-ossp"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation "campaign_products" 
does not exist[39m
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3614:26)
    at SelectQueryBuilder.getRawAndEntities (/app/src/query-builder/SelectQueryBuilder.ts:1671:29)
    at SelectQueryBuilder.getOne (/app/src/query-builder/SelectQueryBuilder.ts:1698:25)
    at CampaignsService.findOne (/app/src/campaigns/campaigns.service.ts:124:22)
    at CampaignsService.update (/app/src/campaigns/campaigns.service.ts:189:22)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation "campaign_products" 
does not exist[39m
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3614:26)
    at SelectQueryBuilder.getRawAndEntities (/app/src/query-builder/SelectQueryBuilder.ts:1671:29)
    at SelectQueryBuilder.getOne (/app/src/query-builder/SelectQueryBuilder.ts:1698:25)
    at CampaignsService.findOne (/app/src/campaigns/campaigns.service.ts:124:22)
    at CampaignsService.activate (/app/src/campaigns/campaigns.service.ts:233:22)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation "campaign_products" 
does not exist[39m
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3614:26)
    at SelectQueryBuilder.getRawAndEntities (/app/src/query-builder/SelectQueryBuilder.ts:1671:29)
    at SelectQueryBuilder.getOne (/app/src/query-builder/SelectQueryBuilder.ts:1698:25)
    at CampaignsService.findOne (/app/src/campaigns/campaigns.service.ts:124:22)
    at CampaignsService.pause (/app/src/campaigns/campaigns.service.ts:260:22)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id" WHERE "campaign"."id" = $1 -- PARAMETERS: ["00000000-0000-0000-0000-000000000000"]

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation "campaign_products" 
does not exist[39m
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3614:26)
    at SelectQueryBuilder.getRawAndEntities (/app/src/query-builder/SelectQueryBuilder.ts:1671:29)
    at SelectQueryBuilder.getOne (/app/src/query-builder/SelectQueryBuilder.ts:1698:25)
    at CampaignsService.findOne (/app/src/campaigns/campaigns.service.ts:124:22)
    at CampaignsService.end (/app/src/campaigns/campaigns.service.ts:307:22)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id") "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 20 OFFSET 0

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id") "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 20 OFFSET 0

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation "campaign_products" 
does not exist[39m
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.getRawMany (/app/src/query-builder/SelectQueryBuilder.ts:1627:29)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3530:26)
    at SelectQueryBuilder.getManyAndCount (/app/src/query-builder/SelectQueryBuilder.ts:1874:36)
    at CampaignsService.findAll (/app/src/campaigns/campaigns.service.ts:99:32)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id") "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 20 OFFSET 0

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT DISTINCT "distinctAlias"."campaign_id" AS "ids_campaign_id", "distinctAlias"."campaign_createdAt" FROM (SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."incentivePercentage" AS "campaign_incentivePercentage", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."quantityLimit" AS "campaign_quantityLimit", "campaign"."quantityUsed" AS "campaign_quantityUsed", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalDiscounts" AS "campaign_totalDiscounts", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."businessRules" AS "campaign_businessRules", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt", "products"."id" AS "products_id", "products"."campaignId" AS "products_campaignId", "products"."productId" AS "products_productId", "products"."productName" AS "products_productName", "products"."ean" AS "products_ean", "products"."brand" AS "products_brand", "products"."category" AS "products_category", "products"."originalPrice" AS "products_originalPrice", "products"."currentPrice" AS "products_currentPrice", "products"."incentiveAmount" AS "products_incentiveAmount", "products"."incentivePercentage" AS "products_incentivePercentage", "products"."finalPrice" AS "products_finalPrice", "products"."quantityLimit" AS "products_quantityLimit", "products"."quantityUsed" AS "products_quantityUsed", "products"."salesCount" AS "products_salesCount", "products"."totalRevenue" AS "products_totalRevenue", "products"."totalIncentives" AS "products_totalIncentives", "products"."isActive" AS "products_isActive", "products"."lastSaleAt" AS "products_lastSaleAt", "products"."priceUpdatedAt" AS "products_priceUpdatedAt", "products"."priceHistory" AS "products_priceHistory", "products"."metadata" AS "products_metadata", "products"."createdAt" AS "products_createdAt", "products"."updatedAt" AS "products_updatedAt", "transitions"."id" AS "transitions_id", "transitions"."campaignId" AS "transitions_campaignId", "transitions"."action" AS "transitions_action", "transitions"."type" AS "transitions_type", "transitions"."status" AS "transitions_status", "transitions"."fromStatus" AS "transitions_fromStatus", "transitions"."toStatus" AS "transitions_toStatus", "transitions"."executeAt" AS "transitions_executeAt", "transitions"."executedAt" AS "transitions_executedAt", "transitions"."triggeredBy" AS "transitions_triggeredBy", "transitions"."reason" AS "transitions_reason", "transitions"."errorMessage" AS "transitions_errorMessage", "transitions"."retryCount" AS "transitions_retryCount", "transitions"."maxRetries" AS "transitions_maxRetries", "transitions"."nextRetryAt" AS "transitions_nextRetryAt", "transitions"."parameters" AS "transitions_parameters", "transitions"."result" AS "transitions_result", "transitions"."metadata" AS "transitions_metadata", "transitions"."jobId" AS "transitions_jobId", "transitions"."correlationId" AS "transitions_correlationId", "transitions"."isIdempotent" AS "transitions_isIdempotent", "transitions"."idempotencyKey" AS "transitions_idempotencyKey", "transitions"."createdAt" AS "transitions_createdAt" FROM "campaigns" "campaign" LEFT JOIN "campaign_products" "products" ON "products"."campaignId"="campaign"."id"  LEFT JOIN "campaign_transitions" "transitions" ON "transitions"."campaignId"="campaign"."id") "distinctAlias" ORDER BY "distinctAlias"."campaign_createdAt" DESC, "campaign_id" ASC LIMIT 20 OFFSET 0

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: relation "campaign_products" does not exist

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 977  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mrelation "campaign_products" 
does not exist[39m
QueryFailedError: relation "campaign_products" does not exist
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.getRawMany (/app/src/query-builder/SelectQueryBuilder.ts:1627:29)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3530:26)
    at SelectQueryBuilder.getManyAndCount (/app/src/query-builder/SelectQueryBuilder.ts:1874:36)
    at CampaignsService.findAll (/app/src/campaigns/campaigns.service.ts:99:32)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT COUNT(1) AS "cnt" FROM "attribution_models" "AttributionModelConfig"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)
          at async Promise.all (index 0)

  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."maxIncentiveValue" AS "campaign_maxIncentiveValue", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."budget" AS "campaign_budget", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt" FROM "campaigns" "campaign" WHERE "campaign"."status" IN ($1, $2) AND "campaign"."startDate" <= $3 AND "campaign"."endDate" >= $4 -- PARAMETERS: ["active","paused","2025-12-31","2025-01-01"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."maxIncentiveValue" AS "campaign_maxIncentiveValue", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."budget" AS "campaign_budget", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt" FROM "campaigns" "campaign" WHERE "campaign"."status" IN ($1) AND "campaign"."startDate" <= $2 AND "campaign"."endDate" >= $3 -- PARAMETERS: ["ended","2020-12-31","2020-01-01"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."maxIncentiveValue" AS "campaign_maxIncentiveValue", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."budget" AS "campaign_budget", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt" FROM "campaigns" "campaign"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."maxIncentiveValue" AS "campaign_maxIncentiveValue", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."budget" AS "campaign_budget", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt" FROM "campaigns" "campaign" WHERE "campaign"."startDate" <= $1 AND "campaign"."endDate" >= $2 -- PARAMETERS: ["2025-12-31","2025-01-01"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

FAIL test/e2e/campaigns.e2e-spec.ts (26.452 s)
  CampaignsController (e2e)
    /api/v1/campaigns (GET)
      Ô£ò should return list of campaigns (81 ms)
      Ô£ò should filter campaigns by status (13 ms)
      Ô£ò should paginate campaigns (11 ms)
    /api/v1/campaigns/:id (GET)
      Ô£ô should return 400 for invalid UUID (5 ms)
      Ô£ò should return 404 for non-existent campaign (17 ms)
    /api/v1/campaigns (POST)
      Ô£ô should return 400 for invalid campaign data (16 ms)
      Ô£ò should create campaign with valid data (8 ms)
    /api/v1/campaigns/:id (PATCH)
      Ô£ô should return 400 for invalid UUID (5 ms)
      Ô£ò should return 404 for non-existent campaign (14 ms)
    /api/v1/campaigns/:id/activate (POST)
      Ô£ô should return 400 for invalid UUID (4 ms)
      Ô£ò should return 404 for non-existent campaign (16 ms)
    /api/v1/campaigns/:id/pause (POST)
      Ô£ô should return 400 for invalid UUID (4 ms)
      Ô£ò should return 404 for non-existent campaign (25 ms)
    /api/v1/campaigns/:id/end (POST)
      Ô£ô should return 400 for invalid UUID (11 ms)
      Ô£ò should return 404 for non-existent campaign (10 ms)
    /api/v1/campaigns/:id/analytics (GET)
      Ô£ò should return 400 for invalid UUID (9 ms)
      Ô£ô should return 404 for non-existent campaign (6 ms)
    Error handling
      Ô£ò should handle database connection errors gracefully (8 ms)
      Ô£ô should validate date ranges (3 ms)
      Ô£ô should validate incentive values (3 ms)
    Performance
      Ô£ò should respond within acceptable time (9 ms)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns (GET) ÔÇ║ should return list of campaigns

    expected 200 "OK", got 500 "Internal Server Error"

      55 |       return request(app.getHttpServer())
      56 |         .get('/api/v1/campaigns')
    > 57 |         .expect(200)
         |          ^
      58 |         .expect((res) => {
      59 |           expect(res.body).toHaveProperty('data');
      60 |           expect(res.body).toHaveProperty('meta');

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:57:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns (GET) ÔÇ║ should filter campaigns by status

    expected 200 "OK", got 500 "Internal Server Error"

      66 |       return request(app.getHttpServer())
      67 |         .get('/api/v1/campaigns?status=active')
    > 68 |         .expect(200)
         |          ^
      69 |         .expect((res) => {
      70 |           expect(res.body).toHaveProperty('data');
      71 |           expect(Array.isArray(res.body.data)).toBe(true);

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:68:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns (GET) ÔÇ║ should paginate campaigns

    expected 200 "OK", got 500 "Internal Server Error"

      76 |       return request(app.getHttpServer())
      77 |         .get('/api/v1/campaigns?page=1&limit=10')
    > 78 |         .expect(200)
         |          ^
      79 |         .expect((res) => {
      80 |           expect(res.body).toHaveProperty('meta');
      81 |           expect(res.body.meta).toHaveProperty('page');

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:78:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns/:id (GET) ÔÇ║ should return 404 for non-existent campaign

    expected 404 "Not Found", got 500 "Internal Server Error"

       95 |       return request(app.getHttpServer())
       96 |         .get('/api/v1/campaigns/00000000-0000-0000-0000-000000000000')
    >  97 |         .expect(404);
          |          ^
       98 |     });
       99 |   });
      100 |

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:97:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns (POST) ÔÇ║ should create campaign with valid data

    expected 201 "Created", got 400 "Bad Request"

      122 |         .post('/api/v1/campaigns')
      123 |         .send(validCampaign)
    > 124 |         .expect(201)
          |          ^
      125 |         .expect((res) => {
      126 |           expect(res.body).toHaveProperty('id');
      127 |           expect(res.body.name).toBe(validCampaign.name);

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:124:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns/:id (PATCH) ÔÇ║ should return 404 for non-existent campaign

    expected 404 "Not Found", got 500 "Internal Server Error"

      143 |         .patch('/api/v1/campaigns/00000000-0000-0000-0000-000000000000')
      144 |         .send({ name: 'Updated Name' })
    > 145 |         .expect(404);
          |          ^
      146 |     });
      147 |   });
      148 |

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:145:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns/:id/activate (POST) ÔÇ║ should return 404 for non-existent campaign

    expected 404 "Not Found", got 500 "Internal Server Error"

      157 |       return request(app.getHttpServer())
      158 |         .post('/api/v1/campaigns/00000000-0000-0000-0000-000000000000/activate')
    > 159 |         .expect(404);
          |          ^
      160 |     });
      161 |   });
      162 |

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:159:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns/:id/pause (POST) ÔÇ║ should return 404 for non-existent campaign

    expected 404 "Not Found", got 500 "Internal Server Error"

      173 |         .post('/api/v1/campaigns/00000000-0000-0000-0000-000000000000/pause')
      174 |         .send({ reason: 'Test pause' })
    > 175 |         .expect(404);
          |          ^
      176 |     });
      177 |   });
      178 |

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:175:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns/:id/end (POST) ÔÇ║ should return 404 for non-existent campaign

    expected 404 "Not Found", got 500 "Internal Server Error"

      189 |         .post('/api/v1/campaigns/00000000-0000-0000-0000-000000000000/end')
      190 |         .send({ reason: 'Test end' })
    > 191 |         .expect(404);
          |          ^
      192 |     });
      193 |   });
      194 |

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:191:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ /api/v1/campaigns/:id/analytics (GET) ÔÇ║ should return 400 for invalid UUID

    expected 400 "Bad Request", got 404 "Not Found"

      197 |       return request(app.getHttpServer())
      198 |         .get('/api/v1/campaigns/invalid-uuid/analytics')
    > 199 |         .expect(400);
          |          ^
      200 |     });
      201 |
      202 |     it('should return 404 for non-existent campaign', () => {

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:199:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ Error handling ÔÇ║ should handle database connection errors gracefully

    expected 200 "OK", got 500 "Internal Server Error"

      213 |       const response = await request(app.getHttpServer())
      214 |         .get('/api/v1/campaigns')
    > 215 |         .expect(200);
          |          ^
      216 |       
      217 |       expect(response.body).toBeDefined();
      218 |     });

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:215:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  ÔùÅ CampaignsController (e2e) ÔÇ║ Performance ÔÇ║ should respond within acceptable time

    expected 200 "OK", got 500 "Internal Server Error"

      261 |       await request(app.getHttpServer())
      262 |         .get('/api/v1/campaigns')
    > 263 |         .expect(200);
          |          ^
      264 |       
      265 |       const endTime = Date.now();
      266 |       const responseTime = endTime - startTime;

      at Object.<anonymous> (e2e/campaigns.e2e-spec.ts:263:10)
      ----
      at Test._assertStatus (../node_modules/supertest/lib/test.js:252:14)
      at ../node_modules/supertest/lib/test.js:308:13
      at Test._assertFunction (../node_modules/supertest/lib/test.js:285:13)
      at Test.assert (../node_modules/supertest/lib/test.js:164:23)
      at Server.localAssert (../node_modules/supertest/lib/test.js:120:14)

  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."maxIncentiveValue" AS "campaign_maxIncentiveValue", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."budget" AS "campaign_budget", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt" FROM "campaigns" "campaign"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

[31m[Nest] 978  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ReportsService] [39m[31mError exporting invalid 
report:[39m
[31m[Nest] 978  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ReportsService] [39m[31mError: Invalid report type[39m
[31m[Nest] 978  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ReportsService] [39m[31mError exporting invalid 
report:[39m
[31m[Nest] 978  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ReportsService] [39m[31mBadRequestException: Invalid 
report type[39m
  console.log
    query: SELECT "product"."id" AS "product_id", "product"."sku" AS "product_sku", "product"."name" AS "product_name", "product"."description" AS "product_description", "product"."barcode" AS "product_barcode", "product"."categoryId" AS "product_categoryId", "product"."subcategoryId" AS "product_subcategoryId", "product"."industryId" AS "product_industryId", "product"."brandId" AS "product_brandId", "product"."manufacturerId" AS "product_manufacturerId", "product"."type" AS "product_type", "product"."status" AS "product_status", "product"."price" AS "product_price", "product"."costPrice" AS "product_costPrice", "product"."currency" AS "product_currency", "product"."weight" AS "product_weight", "product"."weightUnit" AS "product_weightUnit", "product"."dimensions" AS "product_dimensions", "product"."images" AS "product_images", "product"."attributes" AS "product_attributes", "product"."tags" AS "product_tags", "product"."isEligibleForIncentives" AS "product_isEligibleForIncentives", "product"."maxIncentivePercentage" AS "product_maxIncentivePercentage", "product"."maxIncentiveValue" AS "product_maxIncentiveValue", "product"."stockQuantity" AS "product_stockQuantity", "product"."minStockLevel" AS "product_minStockLevel", "product"."trackStock" AS "product_trackStock", "product"."supplier" AS "product_supplier", "product"."supplierSku" AS "product_supplierSku", "product"."launchDate" AS "product_launchDate", "product"."discontinueDate" AS "product_discontinueDate", "product"."isFeatured" AS "product_isFeatured", "product"."isPromotional" AS "product_isPromotional", "product"."salesCount" AS "product_salesCount", "product"."averageRating" AS "product_averageRating", "product"."reviewCount" AS "product_reviewCount", "product"."seoTitle" AS "product_seoTitle", "product"."seoDescription" AS "product_seoDescription", "product"."seoKeywords" AS "product_seoKeywords", "product"."createdBy" AS "product_createdBy", "product"."updatedBy" AS "product_updatedBy", "product"."createdAt" AS "product_createdAt", "product"."updatedAt" AS "product_updatedAt", "category"."id" AS "category_id", "category"."name" AS "category_name", "category"."slug" AS "category_slug", "category"."description" AS "category_description", "category"."industryId" AS "category_industryId", "category"."parentId" AS "category_parentId", "category"."level" AS "category_level", "category"."sortOrder" AS "category_sortOrder", "category"."status" AS "category_status", "category"."imageUrl" AS "category_imageUrl", "category"."iconUrl" AS "category_iconUrl", "category"."color" AS "category_color", "category"."attributes" AS "category_attributes", "category"."tags" AS "category_tags", "category"."isEligibleForIncentives" AS "category_isEligibleForIncentives", "category"."defaultMaxIncentivePercentage" AS "category_defaultMaxIncentivePercentage", "category"."defaultMaxIncentiveValue" AS "category_defaultMaxIncentiveValue", "category"."allowSubcategoryIncentives" AS "category_allowSubcategoryIncentives", "category"."productCount" AS "category_productCount", "category"."isVisible" AS "category_isVisible", "category"."isFeatured" AS "category_isFeatured", "category"."seoTitle" AS "category_seoTitle", "category"."seoDescription" AS "category_seoDescription", "category"."seoKeywords" AS "category_seoKeywords", "category"."createdBy" AS "category_createdBy", "category"."updatedBy" AS "category_updatedBy", "category"."createdAt" AS "category_createdAt", "category"."updatedAt" AS "category_updatedAt", "category"."nsleft" AS "category_nsleft", "category"."nsright" AS "category_nsright" FROM "products" "product" LEFT JOIN "product_categories" "category" ON "category"."id"="product"."categoryId"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT "ProductCategory"."id" AS "ProductCategory_id", "ProductCategory"."name" AS "ProductCategory_name", "ProductCategory"."slug" AS "ProductCategory_slug", "ProductCategory"."description" AS "ProductCategory_description", "ProductCategory"."industryId" AS "ProductCategory_industryId", "ProductCategory"."parentId" AS "ProductCategory_parentId", "ProductCategory"."level" AS "ProductCategory_level", "ProductCategory"."sortOrder" AS "ProductCategory_sortOrder", "ProductCategory"."status" AS "ProductCategory_status", "ProductCategory"."imageUrl" AS "ProductCategory_imageUrl", "ProductCategory"."iconUrl" AS "ProductCategory_iconUrl", "ProductCategory"."color" AS "ProductCategory_color", "ProductCategory"."attributes" AS "ProductCategory_attributes", "ProductCategory"."tags" AS "ProductCategory_tags", "ProductCategory"."isEligibleForIncentives" AS "ProductCategory_isEligibleForIncentives", "ProductCategory"."defaultMaxIncentivePercentage" AS "ProductCategory_defaultMaxIncentivePercentage", "ProductCategory"."defaultMaxIncentiveValue" AS "ProductCategory_defaultMaxIncentiveValue", "ProductCategory"."allowSubcategoryIncentives" AS "ProductCategory_allowSubcategoryIncentives", "ProductCategory"."productCount" AS "ProductCategory_productCount", "ProductCategory"."isVisible" AS "ProductCategory_isVisible", "ProductCategory"."isFeatured" AS "ProductCategory_isFeatured", "ProductCategory"."seoTitle" AS "ProductCategory_seoTitle", "ProductCategory"."seoDescription" AS "ProductCategory_seoDescription", "ProductCategory"."seoKeywords" AS "ProductCategory_seoKeywords", "ProductCategory"."createdBy" AS "ProductCategory_createdBy", "ProductCategory"."updatedBy" AS "ProductCategory_updatedBy", "ProductCategory"."createdAt" AS "ProductCategory_createdAt", "ProductCategory"."updatedAt" AS "ProductCategory_updatedAt", "ProductCategory"."nsleft" AS "ProductCategory_nsleft", "ProductCategory"."nsright" AS "ProductCategory_nsright" FROM "product_categories" "ProductCategory"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT "product"."id" AS "product_id", "product"."sku" AS "product_sku", "product"."name" AS "product_name", "product"."description" AS "product_description", "product"."barcode" AS "product_barcode", "product"."categoryId" AS "product_categoryId", "product"."subcategoryId" AS "product_subcategoryId", "product"."industryId" AS "product_industryId", "product"."brandId" AS "product_brandId", "product"."manufacturerId" AS "product_manufacturerId", "product"."type" AS "product_type", "product"."status" AS "product_status", "product"."price" AS "product_price", "product"."costPrice" AS "product_costPrice", "product"."currency" AS "product_currency", "product"."weight" AS "product_weight", "product"."weightUnit" AS "product_weightUnit", "product"."dimensions" AS "product_dimensions", "product"."images" AS "product_images", "product"."attributes" AS "product_attributes", "product"."tags" AS "product_tags", "product"."isEligibleForIncentives" AS "product_isEligibleForIncentives", "product"."maxIncentivePercentage" AS "product_maxIncentivePercentage", "product"."maxIncentiveValue" AS "product_maxIncentiveValue", "product"."stockQuantity" AS "product_stockQuantity", "product"."minStockLevel" AS "product_minStockLevel", "product"."trackStock" AS "product_trackStock", "product"."supplier" AS "product_supplier", "product"."supplierSku" AS "product_supplierSku", "product"."launchDate" AS "product_launchDate", "product"."discontinueDate" AS "product_discontinueDate", "product"."isFeatured" AS "product_isFeatured", "product"."isPromotional" AS "product_isPromotional", "product"."salesCount" AS "product_salesCount", "product"."averageRating" AS "product_averageRating", "product"."reviewCount" AS "product_reviewCount", "product"."seoTitle" AS "product_seoTitle", "product"."seoDescription" AS "product_seoDescription", "product"."seoKeywords" AS "product_seoKeywords", "product"."createdBy" AS "product_createdBy", "product"."updatedBy" AS "product_updatedBy", "product"."createdAt" AS "product_createdAt", "product"."updatedAt" AS "product_updatedAt", "category"."id" AS "category_id", "category"."name" AS "category_name", "category"."slug" AS "category_slug", "category"."description" AS "category_description", "category"."industryId" AS "category_industryId", "category"."parentId" AS "category_parentId", "category"."level" AS "category_level", "category"."sortOrder" AS "category_sortOrder", "category"."status" AS "category_status", "category"."imageUrl" AS "category_imageUrl", "category"."iconUrl" AS "category_iconUrl", "category"."color" AS "category_color", "category"."attributes" AS "category_attributes", "category"."tags" AS "category_tags", "category"."isEligibleForIncentives" AS "category_isEligibleForIncentives", "category"."defaultMaxIncentivePercentage" AS "category_defaultMaxIncentivePercentage", "category"."defaultMaxIncentiveValue" AS "category_defaultMaxIncentiveValue", "category"."allowSubcategoryIncentives" AS "category_allowSubcategoryIncentives", "category"."productCount" AS "category_productCount", "category"."isVisible" AS "category_isVisible", "category"."isFeatured" AS "category_isFeatured", "category"."seoTitle" AS "category_seoTitle", "category"."seoDescription" AS "category_seoDescription", "category"."seoKeywords" AS "category_seoKeywords", "category"."createdBy" AS "category_createdBy", "category"."updatedBy" AS "category_updatedBy", "category"."createdAt" AS "category_createdAt", "category"."updatedAt" AS "category_updatedAt", "category"."nsleft" AS "category_nsleft", "category"."nsright" AS "category_nsright" FROM "products" "product" LEFT JOIN "product_categories" "category" ON "category"."id"="product"."categoryId"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."maxIncentiveValue" AS "campaign_maxIncentiveValue", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."budget" AS "campaign_budget", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt" FROM "campaigns" "campaign" WHERE "campaign"."startDate" <= $1 AND "campaign"."endDate" >= $2 -- PARAMETERS: ["2025-12-31","invalid-date"]

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

  console.log
    query failed: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."maxIncentiveValue" AS "campaign_maxIncentiveValue", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."budget" AS "campaign_budget", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt" FROM "campaigns" "campaign" WHERE "campaign"."startDate" <= $1 AND "campaign"."endDate" >= $2 -- PARAMETERS: ["2025-12-31","invalid-date"]

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

  console.log
    error: error: invalid input syntax for type timestamp with time zone: "invalid-date"

      at Function.logError (../src/platform/PlatformTools.ts:252:17)

[31m[Nest] 978  - [39m09/30/2025, 5:51:56 PM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31minvalid input syntax for type 
timestamp with time zone: "invalid-date"[39m
QueryFailedError: invalid input syntax for type timestamp with time zone: "invalid-date"
    at PostgresQueryRunner.query (/app/src/driver/postgres/PostgresQueryRunner.ts:325:19)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at SelectQueryBuilder.loadRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3868:25)
    at SelectQueryBuilder.executeEntitiesAndRawResults (/app/src/query-builder/SelectQueryBuilder.ts:3614:26)
    at SelectQueryBuilder.getRawAndEntities (/app/src/query-builder/SelectQueryBuilder.ts:1671:29)
    at SelectQueryBuilder.getMany (/app/src/query-builder/SelectQueryBuilder.ts:1761:25)
    at ReportsService.generateCampaignPerformanceReport (/app/src/reports/reports.service.ts:214:23)
    at /app/node_modules/@nestjs/core/router/router-execution-context.js:46:28
    at /app/node_modules/@nestjs/core/router/router-proxy.js:9:17
  console.log
    query: SELECT "campaign"."id" AS "campaign_id", "campaign"."name" AS "campaign_name", "campaign"."description" AS "campaign_description", "campaign"."industryId" AS "campaign_industryId", "campaign"."createdBy" AS "campaign_createdBy", "campaign"."status" AS "campaign_status", "campaign"."incentiveType" AS "campaign_incentiveType", "campaign"."incentiveValue" AS "campaign_incentiveValue", "campaign"."maxIncentiveValue" AS "campaign_maxIncentiveValue", "campaign"."startDate" AS "campaign_startDate", "campaign"."endDate" AS "campaign_endDate", "campaign"."budget" AS "campaign_budget", "campaign"."totalInvestment" AS "campaign_totalInvestment", "campaign"."totalRevenue" AS "campaign_totalRevenue", "campaign"."impactedConsumers" AS "campaign_impactedConsumers", "campaign"."convertedConsumers" AS "campaign_convertedConsumers", "campaign"."conversionRate" AS "campaign_conversionRate", "campaign"."roi" AS "campaign_roi", "campaign"."cycleId" AS "campaign_cycleId", "campaign"."nextTransitionAt" AS "campaign_nextTransitionAt", "campaign"."nextTransitionAction" AS "campaign_nextTransitionAction", "campaign"."aiEstimation" AS "campaign_aiEstimation", "campaign"."metadata" AS "campaign_metadata", "campaign"."publishedAt" AS "campaign_publishedAt", "campaign"."pausedAt" AS "campaign_pausedAt", "campaign"."endedAt" AS "campaign_endedAt", "campaign"."pausedBy" AS "campaign_pausedBy", "campaign"."endedBy" AS "campaign_endedBy", "campaign"."createdAt" AS "campaign_createdAt", "campaign"."updatedAt" AS "campaign_updatedAt" FROM "campaigns" "campaign"

      at Function.logInfo (../src/platform/PlatformTools.ts:248:17)

PASS test/e2e/reports.e2e-spec.ts (26.69 s)
  ReportsController (e2e)
    /api/v1/reports/campaign-performance (POST)
      Ô£ô should return campaign performance report (46 ms)
      Ô£ô should return empty report when no campaigns match filters (10 ms)
      Ô£ô should handle request without filters (8 ms)
    /api/v1/reports/export (POST)
      Ô£ô should export campaign report as XLSX (45 ms)
      Ô£ô should export campaign report as PDF (39 ms)
      Ô£ô should return 400 for invalid report type (4 ms)
    /api/v1/reports/product-performance (POST)
      Ô£ô should return product performance report (23 ms)
    /api/v1/reports/incentive-analysis (POST)
      Ô£ô should return incentive analysis report (7 ms)
    /api/v1/reports/user-activity (POST)
      Ô£ô should return user activity report (12 ms)
    Error handling
      Ô£ô should handle database connection errors gracefully (71 ms)
    Performance
      Ô£ô should respond within acceptable time (6 ms)

A worker process has failed to exit gracefully and has been force exited. This is likely caused by tests leaking due to improper 
teardown. Try running with --detectOpenHandles to find leaks. Active timers can also cause this, ensure that .unref() was called on 
them.
Test Suites: 1 failed, 1 passed, 2 total
Tests:       12 failed, 20 passed, 32 total
Snapshots:   0 total
Time:        28.033 s
Ran all test suites.
