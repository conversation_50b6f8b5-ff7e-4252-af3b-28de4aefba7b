import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';

import { LgpdController } from './lgpd.controller';
import { LgpdService } from './lgpd.service';
import { DataAnonymizationService } from './data-anonymization.service';

import { LgpdConsent } from '../database/entities/lgpd-consent.entity';
import { DataSubjectRequest } from '../database/entities/data-subject-request.entity';
import { PersonalDataInventory } from '../database/entities/personal-data-inventory.entity';
import { DataProcessingActivity } from '../database/entities/data-processing-activity.entity';
import { LgpdIncident } from '../database/entities/lgpd-incident.entity';

// Import audit module for logging
import { AuditModule } from '../audit/audit.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LgpdConsent,
      DataSubjectRequest,
      PersonalDataInventory,
      DataProcessingActivity,
      LgpdIncident,
    ]),
    ConfigModule,
    AuditModule,
  ],
  controllers: [LgpdController],
  providers: [
    LgpdService,
    DataAnonymizationService,
  ],
  exports: [
    LgpdService,
    DataAnonymizationService,
  ],
})
export class LgpdModule {}
