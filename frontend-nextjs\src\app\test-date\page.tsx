'use client';

import { useState } from 'react';
import { DatePicker } from '@/components/ui/date-picker';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';

export default function TestDatePage() {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Teste do DatePicker em Português</h1>
          <p className="text-gray-700">Teste da funcionalidade de seleção de datas em português brasileiro</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Seleção de Datas</CardTitle>
            <CardDescription>
              Os campos abaixo usam o novo componente DatePicker com localização em português
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Data de Início</Label>
                <DatePicker
                  id="startDate"
                  value={startDate}
                  onChange={setStartDate}
                  placeholder="Selecione a data de início"
                />
              </div>
              
              <div>
                <Label htmlFor="endDate">Data de Fim</Label>
                <DatePicker
                  id="endDate"
                  value={endDate}
                  onChange={setEndDate}
                  placeholder="Selecione a data de fim"
                />
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-lg font-medium text-blue-900 mb-2">Valores Selecionados:</h3>
              <div className="space-y-2 text-sm">
                <p><strong>Data de Início:</strong> {startDate || 'Nenhuma data selecionada'}</p>
                <p><strong>Data de Fim:</strong> {endDate || 'Nenhuma data selecionada'}</p>
              </div>
            </div>

            <div className="mt-6 p-4 bg-green-50 rounded-lg">
              <h3 className="text-lg font-medium text-green-900 mb-2">Funcionalidades:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-green-800">
                <li>Calendário em português brasileiro</li>
                <li>Nomes dos meses em português</li>
                <li>Dias da semana abreviados em português</li>
                <li>Formato de exibição: "dd de MMMM de yyyy"</li>
                <li>Navegação entre meses</li>
                <li>Destaque do dia atual</li>
                <li>Seleção visual da data escolhida</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
