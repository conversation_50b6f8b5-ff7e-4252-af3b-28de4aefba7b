import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('personal_data_inventory')
export class PersonalDataInventory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'entity_id' })
  entityId: string;

  @Column({ name: 'data_type' })
  dataType: string;

  @Column({ name: 'data_category' })
  dataCategory: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'legal_basis', nullable: true })
  legalBasis: string;

  @Column({ name: 'processing_purpose', nullable: true })
  processingPurpose: string;

  @Column({ name: 'retention_period_days', type: 'integer', default: 1825 })
  retentionPeriodDays: number;

  @Column({ name: 'retention_deadline', type: 'timestamp', nullable: true })
  retentionDeadline: Date;

  @Column({ name: 'is_anonymized', default: false })
  isAnonymized: boolean;

  @Column({ name: 'anonymized_at', type: 'timestamp', nullable: true })
  anonymizedAt: Date;

  @Column({ name: 'data_subject_id', nullable: true })
  dataSubjectId: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
