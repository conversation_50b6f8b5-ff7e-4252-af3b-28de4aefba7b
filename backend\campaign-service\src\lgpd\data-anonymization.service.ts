import {
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as crypto from 'crypto';

import { PersonalDataInventory } from '../database/entities/personal-data-inventory.entity';
import { AuditLoggerService } from '../audit/audit-logger.service';
import { ComplianceType, ComplianceEventType } from '../database/entities/compliance-event.entity';

export interface AnonymizationRule {
  field: string;
  method: AnonymizationMethod;
  retentionDays?: number;
  conditions?: Record<string, any>;
}

export enum AnonymizationMethod {
  HASH = 'hash',
  MASK = 'mask',
  GENERALIZE = 'generalize',
  SUPPRESS = 'suppress',
  PSEUDONYMIZE = 'pseudonymize',
  RANDOMIZE = 'randomize',
  DATE_SHIFT = 'date_shift',
  AGGREGATE = 'aggregate',
}

export interface AnonymizationResult {
  originalValue: any;
  anonymizedValue: any;
  method: AnonymizationMethod;
  timestamp: Date;
  irreversible: boolean;
}

@Injectable()
export class DataAnonymizationService {
  private readonly logger = new Logger(DataAnonymizationService.name);
  private readonly saltRounds = 12;
  private readonly encryptionKey = process.env.ANONYMIZATION_KEY || 'default-key-change-in-production';

  constructor(
    @InjectRepository(PersonalDataInventory)
    private readonly dataInventoryRepository: Repository<PersonalDataInventory>,
    private readonly auditLogger: AuditLoggerService,
  ) {}

  async anonymizeData(
    data: Record<string, any>,
    rules: AnonymizationRule[]
  ): Promise<{ anonymizedData: Record<string, any>; results: AnonymizationResult[] }> {
    const anonymizedData = { ...data };
    const results: AnonymizationResult[] = [];

    for (const rule of rules) {
      if (data[rule.field] !== undefined && data[rule.field] !== null) {
        const originalValue = data[rule.field];
        const anonymizedValue = await this.applyAnonymizationMethod(originalValue, rule.method);
        
        anonymizedData[rule.field] = anonymizedValue;
        
        results.push({
          originalValue,
          anonymizedValue,
          method: rule.method,
          timestamp: new Date(),
          irreversible: this.isIrreversibleMethod(rule.method),
        });

        this.logger.log(`Anonymized field '${rule.field}' using method '${rule.method}'`);
      }
    }

    // Log anonymization event
    await this.auditLogger.logComplianceEvent({
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.DATA_ANONYMIZATION,
      description: `Data anonymized using ${rules.length} rules`,
      dataFields: rules.map(r => r.field),
      metadata: {
        rulesApplied: rules.length,
        fieldsProcessed: results.length,
        methods: [...new Set(rules.map(r => r.method))],
      },
    });

    return { anonymizedData, results };
  }

  private async applyAnonymizationMethod(value: any, method: AnonymizationMethod): Promise<any> {
    switch (method) {
      case AnonymizationMethod.HASH:
        return this.hashValue(value);
      
      case AnonymizationMethod.MASK:
        return this.maskValue(value);
      
      case AnonymizationMethod.GENERALIZE:
        return this.generalizeValue(value);
      
      case AnonymizationMethod.SUPPRESS:
        return null;
      
      case AnonymizationMethod.PSEUDONYMIZE:
        return this.pseudonymizeValue(value);
      
      case AnonymizationMethod.RANDOMIZE:
        return this.randomizeValue(value);
      
      case AnonymizationMethod.DATE_SHIFT:
        return this.shiftDate(value);
      
      case AnonymizationMethod.AGGREGATE:
        return this.aggregateValue(value);
      
      default:
        this.logger.warn(`Unknown anonymization method: ${method}`);
        return value;
    }
  }

  private hashValue(value: any): string {
    const stringValue = String(value);
    return crypto.createHash('sha256').update(stringValue + this.encryptionKey).digest('hex');
  }

  private maskValue(value: any): string {
    const stringValue = String(value);
    
    // Email masking
    if (stringValue.includes('@')) {
      const [local, domain] = stringValue.split('@');
      const maskedLocal = local.length > 2 
        ? local.substring(0, 2) + '*'.repeat(local.length - 2)
        : '*'.repeat(local.length);
      return `${maskedLocal}@${domain}`;
    }
    
    // Phone number masking
    if (/^\+?[\d\s\-\(\)]+$/.test(stringValue)) {
      const digits = stringValue.replace(/\D/g, '');
      if (digits.length >= 8) {
        const masked = digits.substring(0, 2) + '*'.repeat(digits.length - 4) + digits.substring(digits.length - 2);
        return masked;
      }
    }
    
    // General string masking
    if (stringValue.length > 4) {
      return stringValue.substring(0, 2) + '*'.repeat(stringValue.length - 4) + stringValue.substring(stringValue.length - 2);
    } else {
      return '*'.repeat(stringValue.length);
    }
  }

  private generalizeValue(value: any): any {
    // Age generalization
    if (typeof value === 'number' && value > 0 && value < 150) {
      if (value < 18) return '0-17';
      if (value < 30) return '18-29';
      if (value < 50) return '30-49';
      if (value < 65) return '50-64';
      return '65+';
    }
    
    // Date generalization (year only)
    if (value instanceof Date) {
      return value.getFullYear();
    }
    
    // String generalization (first letter only)
    if (typeof value === 'string' && value.length > 0) {
      return value.charAt(0).toUpperCase() + '*';
    }
    
    return value;
  }

  private pseudonymizeValue(value: any): string {
    const stringValue = String(value);
    const hash = crypto.createHmac('sha256', this.encryptionKey).update(stringValue).digest('hex');
    return `PSEUDO_${hash.substring(0, 16)}`;
  }

  private randomizeValue(value: any): any {
    if (typeof value === 'number') {
      // Randomize within same order of magnitude
      const magnitude = Math.pow(10, Math.floor(Math.log10(Math.abs(value))));
      return Math.floor(Math.random() * magnitude * 9) + magnitude;
    }
    
    if (typeof value === 'string') {
      // Generate random string of same length
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < value.length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    }
    
    if (value instanceof Date) {
      // Random date within same year
      const year = value.getFullYear();
      const start = new Date(year, 0, 1);
      const end = new Date(year, 11, 31);
      return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    }
    
    return value;
  }

  private shiftDate(value: any): Date | any {
    if (!(value instanceof Date)) {
      return value;
    }
    
    // Shift date by random number of days (±30 days)
    const shiftDays = Math.floor(Math.random() * 61) - 30; // -30 to +30 days
    const shiftedDate = new Date(value);
    shiftedDate.setDate(shiftedDate.getDate() + shiftDays);
    
    return shiftedDate;
  }

  private aggregateValue(value: any): string {
    // Convert to range or category
    if (typeof value === 'number') {
      if (value < 100) return 'LOW';
      if (value < 1000) return 'MEDIUM';
      return 'HIGH';
    }
    
    return 'AGGREGATED';
  }

  private isIrreversibleMethod(method: AnonymizationMethod): boolean {
    const irreversibleMethods = [
      AnonymizationMethod.HASH,
      AnonymizationMethod.SUPPRESS,
      AnonymizationMethod.RANDOMIZE,
      AnonymizationMethod.AGGREGATE,
    ];
    
    return irreversibleMethods.includes(method);
  }

  async getAnonymizationRules(dataType: string): Promise<AnonymizationRule[]> {
    // Default rules for common data types
    const defaultRules: Record<string, AnonymizationRule[]> = {
      'User': [
        { field: 'email', method: AnonymizationMethod.HASH, retentionDays: 2555 }, // 7 years
        { field: 'phone', method: AnonymizationMethod.MASK, retentionDays: 1825 }, // 5 years
        { field: 'cpf', method: AnonymizationMethod.HASH, retentionDays: 2555 },
        { field: 'birthDate', method: AnonymizationMethod.GENERALIZE, retentionDays: 1825 },
        { field: 'address', method: AnonymizationMethod.GENERALIZE, retentionDays: 1095 }, // 3 years
      ],
      'Campaign': [
        { field: 'targetAudience', method: AnonymizationMethod.GENERALIZE, retentionDays: 1095 },
        { field: 'personalizedContent', method: AnonymizationMethod.SUPPRESS, retentionDays: 730 }, // 2 years
      ],
      'Transaction': [
        { field: 'amount', method: AnonymizationMethod.GENERALIZE, retentionDays: 2555 },
        { field: 'paymentMethod', method: AnonymizationMethod.GENERALIZE, retentionDays: 1825 },
      ],
    };

    return defaultRules[dataType] || [];
  }

  async anonymizeExpiredData(dataType: string, entityId: string): Promise<void> {
    const rules = await this.getAnonymizationRules(dataType);
    
    if (rules.length === 0) {
      this.logger.warn(`No anonymization rules found for data type: ${dataType}`);
      return;
    }

    // This would typically fetch the actual data from the database
    // For now, we'll simulate the process
    this.logger.log(`Anonymizing expired data for ${dataType}:${entityId}`);

    // Log the anonymization
    await this.auditLogger.logComplianceEvent({
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.DATA_ANONYMIZATION,
      description: `Automatic anonymization of expired data`,
      dataCategories: [dataType],
      metadata: {
        entityId,
        rulesApplied: rules.length,
        trigger: 'automatic_retention_policy',
      },
    });
  }

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async processAutomaticAnonymization(): Promise<void> {
    this.logger.log('Starting automatic data anonymization process');

    try {
      // Find data that needs to be anonymized based on retention policies
      const expiredData = await this.dataInventoryRepository
        .createQueryBuilder('inventory')
        .where('inventory.retentionDeadline <= :now', { now: new Date() })
        .andWhere('inventory.isAnonymized = :isAnonymized', { isAnonymized: false })
        .getMany();

      this.logger.log(`Found ${expiredData.length} data items requiring anonymization`);

      for (const dataItem of expiredData) {
        try {
          await this.anonymizeExpiredData(dataItem.dataType, dataItem.entityId);
          
          // Mark as anonymized
          dataItem.isAnonymized = true;
          dataItem.anonymizedAt = new Date();
          await this.dataInventoryRepository.save(dataItem);
          
        } catch (error) {
          this.logger.error(`Error anonymizing data ${dataItem.id}:`, error);
        }
      }

      this.logger.log(`Automatic anonymization process completed: ${expiredData.length} items processed`);
      
    } catch (error) {
      this.logger.error('Error in automatic anonymization process:', error);
    }
  }

  async validateAnonymization(
    originalData: Record<string, any>,
    anonymizedData: Record<string, any>,
    rules: AnonymizationRule[]
  ): Promise<{
    isValid: boolean;
    issues: string[];
    riskScore: number;
  }> {
    const issues: string[] = [];
    let riskScore = 0;

    // Check if all required fields were anonymized
    for (const rule of rules) {
      if (originalData[rule.field] !== undefined) {
        if (anonymizedData[rule.field] === originalData[rule.field]) {
          issues.push(`Field '${rule.field}' was not anonymized`);
          riskScore += 0.3;
        }
      }
    }

    // Check for potential re-identification risks
    const identifiableFields = ['email', 'phone', 'cpf', 'ssn', 'passport'];
    for (const field of identifiableFields) {
      if (anonymizedData[field] && typeof anonymizedData[field] === 'string') {
        const value = anonymizedData[field];
        
        // Check if it still looks like original format
        if (field === 'email' && value.includes('@') && !value.includes('*')) {
          issues.push(`Email field '${field}' may still be identifiable`);
          riskScore += 0.4;
        }
        
        if (field === 'phone' && /^\+?[\d\-\(\)\s]+$/.test(value) && !value.includes('*')) {
          issues.push(`Phone field '${field}' may still be identifiable`);
          riskScore += 0.4;
        }
      }
    }

    // Check for quasi-identifiers combination
    const quasiIdentifiers = ['age', 'zipCode', 'gender', 'occupation'];
    const presentQuasiIdentifiers = quasiIdentifiers.filter(field => 
      anonymizedData[field] !== undefined && anonymizedData[field] !== null
    );
    
    if (presentQuasiIdentifiers.length >= 3) {
      issues.push('Combination of quasi-identifiers may allow re-identification');
      riskScore += 0.2;
    }

    const isValid = issues.length === 0;
    riskScore = Math.min(riskScore, 1.0); // Cap at 1.0

    return { isValid, issues, riskScore };
  }

  async generateAnonymizationReport(
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalRecordsAnonymized: number;
    byDataType: Record<string, number>;
    byMethod: Record<AnonymizationMethod, number>;
    complianceRate: number;
    riskAssessment: {
      lowRisk: number;
      mediumRisk: number;
      highRisk: number;
    };
  }> {
    // This would typically query actual anonymization logs
    // For now, we'll return mock data
    
    return {
      totalRecordsAnonymized: 1250,
      byDataType: {
        'User': 800,
        'Campaign': 300,
        'Transaction': 150,
      },
      byMethod: {
        [AnonymizationMethod.HASH]: 400,
        [AnonymizationMethod.MASK]: 350,
        [AnonymizationMethod.GENERALIZE]: 300,
        [AnonymizationMethod.SUPPRESS]: 100,
        [AnonymizationMethod.PSEUDONYMIZE]: 100,
        [AnonymizationMethod.RANDOMIZE]: 0,
        [AnonymizationMethod.DATE_SHIFT]: 0,
        [AnonymizationMethod.AGGREGATE]: 0,
      },
      complianceRate: 98.5,
      riskAssessment: {
        lowRisk: 1100,
        mediumRisk: 130,
        highRisk: 20,
      },
    };
  }
}
