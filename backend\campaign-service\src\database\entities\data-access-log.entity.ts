import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

export enum DataAccessType {
  READ = 'read',
  EXPORT = 'export',
  DOWNLOAD = 'download',
  SEARCH = 'search',
  FILTER = 'filter',
  REPORT = 'report',
  BULK_ACCESS = 'bulk_access',
  API_ACCESS = 'api_access',
  ADMIN_ACCESS = 'admin_access',
}

export enum DataSensitivity {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  RESTRICTED = 'restricted',
  PII = 'pii', // Personally Identifiable Information
  FINANCIAL = 'financial',
  HEALTH = 'health',
  LEGAL = 'legal',
}

export enum AccessPurpose {
  BUSINESS_OPERATION = 'business_operation',
  ANALYTICS = 'analytics',
  REPORTING = 'reporting',
  AUDIT = 'audit',
  COMPLIANCE = 'compliance',
  SUPPORT = 'support',
  INVESTIGATION = 'investigation',
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  MAINTENANCE = 'maintenance',
  RESEARCH = 'research',
  MARKETING = 'marketing',
}

@Entity('data_access_logs')
@Index(['userId', 'timestamp'])
@Index(['dataType', 'timestamp'])
@Index(['accessType', 'timestamp'])
@Index(['sensitivity', 'timestamp'])
@Index(['purpose', 'timestamp'])
@Index(['timestamp'])
@Index(['ipAddress'])
@Index(['isAuthorized'])
export class DataAccessLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  userId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  sessionId: string;

  @Column({ type: 'enum', enum: DataAccessType })
  accessType: DataAccessType;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  dataType: string; // e.g., 'Campaign', 'User', 'Product', 'Report'

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  dataId: string; // Specific record ID

  @Column({ type: 'enum', enum: DataSensitivity, default: DataSensitivity.INTERNAL })
  sensitivity: DataSensitivity;

  @Column({ type: 'enum', enum: AccessPurpose })
  purpose: AccessPurpose;

  @Column({ type: 'varchar', length: 500 })
  description: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  endpoint: string; // API endpoint or page URL

  @Column({ type: 'varchar', length: 10, nullable: true })
  httpMethod: string;

  @Column({ type: 'json', nullable: true })
  queryParameters: Record<string, any>; // Search/filter parameters

  @Column({ type: 'json', nullable: true })
  requestBody: Record<string, any>;

  @Column({ type: 'int', nullable: true })
  recordCount: number; // Number of records accessed

  @Column({ type: 'int', nullable: true })
  responseSize: number; // Response size in bytes

  @Column({ type: 'varchar', length: 45, nullable: true })
  @Index()
  ipAddress: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  userAgent: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  countryCode: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  city: string;

  @Column({ type: 'boolean', default: true })
  @Index()
  isAuthorized: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  authorizationMethod: string; // e.g., 'JWT', 'API_KEY', 'SESSION'

  @Column({ type: 'json', nullable: true })
  permissions: string[]; // User permissions at time of access

  @Column({ type: 'varchar', length: 255, nullable: true })
  businessJustification: string; // Why this access was needed

  @Column({ type: 'varchar', length: 255, nullable: true })
  approvedBy: string; // Who approved this access (if required)

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'int', nullable: true })
  duration: number; // Access duration in milliseconds

  @Column({ type: 'boolean', default: false })
  isExported: boolean; // Whether data was exported/downloaded

  @Column({ type: 'varchar', length: 255, nullable: true })
  exportFormat: string; // e.g., 'CSV', 'PDF', 'JSON'

  @Column({ type: 'varchar', length: 255, nullable: true })
  exportDestination: string; // Where data was exported to

  @Column({ type: 'json', nullable: true })
  dataFields: string[]; // Specific fields accessed

  @Column({ type: 'json', nullable: true })
  filters: Record<string, any>; // Applied filters

  @Column({ type: 'varchar', length: 255, nullable: true })
  correlationId: string; // For grouping related accesses

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  @Index()
  timestamp: Date;

  // Helper methods
  static createDataAccessLog(data: {
    userId: string;
    sessionId?: string;
    accessType: DataAccessType;
    dataType: string;
    dataId?: string;
    sensitivity?: DataSensitivity;
    purpose: AccessPurpose;
    description: string;
    endpoint?: string;
    httpMethod?: string;
    queryParameters?: Record<string, any>;
    requestBody?: Record<string, any>;
    recordCount?: number;
    responseSize?: number;
    ipAddress?: string;
    userAgent?: string;
    countryCode?: string;
    city?: string;
    isAuthorized?: boolean;
    authorizationMethod?: string;
    permissions?: string[];
    businessJustification?: string;
    approvedBy?: string;
    approvedAt?: Date;
    duration?: number;
    isExported?: boolean;
    exportFormat?: string;
    exportDestination?: string;
    dataFields?: string[];
    filters?: Record<string, any>;
    correlationId?: string;
    metadata?: Record<string, any>;
  }): Partial<DataAccessLog> {
    return {
      userId: data.userId,
      sessionId: data.sessionId,
      accessType: data.accessType,
      dataType: data.dataType,
      dataId: data.dataId,
      sensitivity: data.sensitivity || DataSensitivity.INTERNAL,
      purpose: data.purpose,
      description: data.description,
      endpoint: data.endpoint,
      httpMethod: data.httpMethod,
      queryParameters: data.queryParameters,
      requestBody: data.requestBody,
      recordCount: data.recordCount,
      responseSize: data.responseSize,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      countryCode: data.countryCode,
      city: data.city,
      isAuthorized: data.isAuthorized !== false, // Default to true
      authorizationMethod: data.authorizationMethod,
      permissions: data.permissions,
      businessJustification: data.businessJustification,
      approvedBy: data.approvedBy,
      approvedAt: data.approvedAt,
      duration: data.duration,
      isExported: data.isExported || false,
      exportFormat: data.exportFormat,
      exportDestination: data.exportDestination,
      dataFields: data.dataFields,
      filters: data.filters,
      correlationId: data.correlationId,
      metadata: data.metadata,
    };
  }

  getAccessTypeLabel(): string {
    const labels = {
      [DataAccessType.READ]: 'Leitura',
      [DataAccessType.EXPORT]: 'Exportação',
      [DataAccessType.DOWNLOAD]: 'Download',
      [DataAccessType.SEARCH]: 'Busca',
      [DataAccessType.FILTER]: 'Filtro',
      [DataAccessType.REPORT]: 'Relatório',
      [DataAccessType.BULK_ACCESS]: 'Acesso em Massa',
      [DataAccessType.API_ACCESS]: 'Acesso via API',
      [DataAccessType.ADMIN_ACCESS]: 'Acesso Administrativo',
    };

    return labels[this.accessType] || this.accessType;
  }

  getSensitivityLabel(): string {
    const labels = {
      [DataSensitivity.PUBLIC]: 'Público',
      [DataSensitivity.INTERNAL]: 'Interno',
      [DataSensitivity.CONFIDENTIAL]: 'Confidencial',
      [DataSensitivity.RESTRICTED]: 'Restrito',
      [DataSensitivity.PII]: 'Dados Pessoais',
      [DataSensitivity.FINANCIAL]: 'Financeiro',
      [DataSensitivity.HEALTH]: 'Saúde',
      [DataSensitivity.LEGAL]: 'Legal',
    };

    return labels[this.sensitivity] || this.sensitivity;
  }

  getPurposeLabel(): string {
    const labels = {
      [AccessPurpose.BUSINESS_OPERATION]: 'Operação de Negócio',
      [AccessPurpose.ANALYTICS]: 'Análise',
      [AccessPurpose.REPORTING]: 'Relatório',
      [AccessPurpose.AUDIT]: 'Auditoria',
      [AccessPurpose.COMPLIANCE]: 'Conformidade',
      [AccessPurpose.SUPPORT]: 'Suporte',
      [AccessPurpose.INVESTIGATION]: 'Investigação',
      [AccessPurpose.DEVELOPMENT]: 'Desenvolvimento',
      [AccessPurpose.TESTING]: 'Teste',
      [AccessPurpose.MAINTENANCE]: 'Manutenção',
      [AccessPurpose.RESEARCH]: 'Pesquisa',
      [AccessPurpose.MARKETING]: 'Marketing',
    };

    return labels[this.purpose] || this.purpose;
  }

  getFormattedResponseSize(): string | null {
    if (!this.responseSize) return null;

    const sizes = ['B', 'KB', 'MB', 'GB'];
    let size = this.responseSize;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < sizes.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${sizes[unitIndex]}`;
  }

  getFormattedDuration(): string | null {
    if (!this.duration) return null;

    if (this.duration < 1000) {
      return `${this.duration}ms`;
    } else if (this.duration < 60000) {
      return `${(this.duration / 1000).toFixed(2)}s`;
    } else {
      return `${(this.duration / 60000).toFixed(2)}min`;
    }
  }

  isHighRisk(): boolean {
    const highRiskSensitivities = [
      DataSensitivity.RESTRICTED,
      DataSensitivity.PII,
      DataSensitivity.FINANCIAL,
      DataSensitivity.HEALTH,
      DataSensitivity.LEGAL,
    ];

    const highRiskAccessTypes = [
      DataAccessType.EXPORT,
      DataAccessType.DOWNLOAD,
      DataAccessType.BULK_ACCESS,
      DataAccessType.ADMIN_ACCESS,
    ];

    return (
      !this.isAuthorized ||
      highRiskSensitivities.includes(this.sensitivity) ||
      highRiskAccessTypes.includes(this.accessType) ||
      (this.recordCount && this.recordCount > 1000) ||
      this.isExported
    );
  }

  requiresApproval(): boolean {
    return (
      this.sensitivity === DataSensitivity.RESTRICTED ||
      this.accessType === DataAccessType.BULK_ACCESS ||
      this.accessType === DataAccessType.ADMIN_ACCESS ||
      (this.recordCount && this.recordCount > 10000)
    );
  }

  isCompliant(): boolean {
    if (this.requiresApproval()) {
      return !!(this.approvedBy && this.approvedAt);
    }

    return this.isAuthorized && !!this.businessJustification;
  }
}
