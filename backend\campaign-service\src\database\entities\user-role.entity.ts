import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { Role } from './role.entity';

@Entity('user_roles')
@Unique(['userId', 'roleId', 'scope', 'scopeId'])
@Index(['userId'])
@Index(['roleId'])
@Index(['scope', 'scopeId'])
export class UserRole {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  userId: string;

  @Column({ type: 'uuid' })
  roleId: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  scope: string; // e.g., 'global', 'industry', 'campaign'

  @Column({ type: 'uuid', nullable: true })
  scopeId: string; // ID of the scoped resource (industry ID, campaign ID, etc.)

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ type: 'uuid' })
  assignedBy: string; // User who assigned this role

  @Column({ type: 'text', nullable: true })
  reason: string; // Reason for role assignment

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Role, role => role.userRoles, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'roleId' })
  role: Role;

  // Helper methods
  isExpired(): boolean {
    return this.expiresAt && new Date() > this.expiresAt;
  }

  isEffective(): boolean {
    return this.isActive && !this.isExpired() && this.role?.isActive;
  }

  isGlobalScope(): boolean {
    return !this.scope || this.scope === 'global';
  }

  isIndustryScope(): boolean {
    return this.scope === 'industry';
  }

  isCampaignScope(): boolean {
    return this.scope === 'campaign';
  }

  getScopeDisplay(): string {
    if (this.isGlobalScope()) {
      return 'Global';
    }
    if (this.isIndustryScope()) {
      return `Indústria: ${this.scopeId}`;
    }
    if (this.isCampaignScope()) {
      return `Campanha: ${this.scopeId}`;
    }
    return `${this.scope}: ${this.scopeId}`;
  }
}
