import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';

/**
 * Database performance optimization configuration
 * 
 * This service provides methods to optimize database performance:
 * - Connection pool tuning
 * - Query optimization
 * - Index management
 * - Performance monitoring
 * - Maintenance tasks
 */
@Injectable()
export class DatabasePerformanceConfig {
  private readonly logger = new Logger(DatabasePerformanceConfig.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Initialize performance optimizations
   */
  async initializePerformanceOptimizations(): Promise<void> {
    this.logger.log('Initializing database performance optimizations...');

    try {
      await this.configurePostgreSQLSettings();
      await this.setupPerformanceMonitoring();
      await this.scheduleMaintenanceTasks();
      
      this.logger.log('Database performance optimizations initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize performance optimizations', error.stack);
      throw error;
    }
  }

  /**
   * Configure PostgreSQL settings for optimal performance
   */
  private async configurePostgreSQLSettings(): Promise<void> {
    const isProduction = this.configService.get('NODE_ENV') === 'production';
    
    const settings = [
      // Memory settings
      `SET shared_buffers = '${isProduction ? '256MB' : '128MB'}'`,
      `SET effective_cache_size = '${isProduction ? '1GB' : '512MB'}'`,
      `SET work_mem = '${isProduction ? '16MB' : '8MB'}'`,
      `SET maintenance_work_mem = '${isProduction ? '64MB' : '32MB'}'`,
      
      // Query planner settings
      `SET random_page_cost = 1.1`, // SSD optimization
      `SET effective_io_concurrency = 200`, // SSD optimization
      `SET default_statistics_target = 100`,
      
      // WAL settings for performance
      `SET wal_buffers = '16MB'`,
      `SET checkpoint_completion_target = 0.9`,
      `SET checkpoint_timeout = '10min'`,
      
      // Connection settings
      `SET max_connections = ${isProduction ? 200 : 100}`,
      `SET shared_preload_libraries = 'pg_stat_statements'`,
      
      // Query optimization
      `SET enable_seqscan = on`,
      `SET enable_indexscan = on`,
      `SET enable_bitmapscan = on`,
      `SET enable_hashjoin = on`,
      `SET enable_mergejoin = on`,
      `SET enable_nestloop = on`,
      
      // Logging for performance analysis
      `SET log_min_duration_statement = ${isProduction ? 1000 : 100}`, // Log slow queries
      `SET log_checkpoints = on`,
      `SET log_connections = ${isProduction ? 'off' : 'on'}`,
      `SET log_disconnections = ${isProduction ? 'off' : 'on'}`,
      `SET log_lock_waits = on`,
      
      // Auto vacuum settings
      `SET autovacuum = on`,
      `SET autovacuum_max_workers = 3`,
      `SET autovacuum_naptime = '1min'`,
      `SET autovacuum_vacuum_threshold = 50`,
      `SET autovacuum_analyze_threshold = 50`,
    ];

    for (const setting of settings) {
      try {
        await this.dataSource.query(setting);
        this.logger.debug(`Applied setting: ${setting}`);
      } catch (error) {
        this.logger.warn(`Failed to apply setting: ${setting}`, error.message);
      }
    }
  }

  /**
   * Setup performance monitoring
   */
  private async setupPerformanceMonitoring(): Promise<void> {
    // Enable pg_stat_statements for query performance monitoring
    try {
      await this.dataSource.query(`CREATE EXTENSION IF NOT EXISTS pg_stat_statements`);
      this.logger.log('pg_stat_statements extension enabled');
    } catch (error) {
      this.logger.warn('Could not enable pg_stat_statements extension', error.message);
    }

    // Create performance monitoring views
    const monitoringQueries = [
      // View for slow queries
      `
      CREATE OR REPLACE VIEW slow_queries AS
      SELECT 
        query,
        calls,
        total_time,
        mean_time,
        rows,
        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
      FROM pg_stat_statements 
      WHERE mean_time > 100 -- queries taking more than 100ms on average
      ORDER BY mean_time DESC
      LIMIT 20
      `,

      // View for table statistics
      `
      CREATE OR REPLACE VIEW table_stats AS
      SELECT 
        schemaname,
        tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        n_live_tup as live_tuples,
        n_dead_tup as dead_tuples,
        last_vacuum,
        last_autovacuum,
        last_analyze,
        last_autoanalyze
      FROM pg_stat_user_tables
      ORDER BY n_live_tup DESC
      `,

      // View for index usage
      `
      CREATE OR REPLACE VIEW index_usage AS
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_tup_read,
        idx_tup_fetch,
        idx_scan,
        CASE 
          WHEN idx_scan = 0 THEN 'Unused'
          WHEN idx_scan < 10 THEN 'Low usage'
          ELSE 'Active'
        END as usage_status
      FROM pg_stat_user_indexes
      ORDER BY idx_scan DESC
      `,

      // View for connection statistics
      `
      CREATE OR REPLACE VIEW connection_stats AS
      SELECT 
        state,
        count(*) as connection_count,
        avg(extract(epoch from (now() - state_change))) as avg_duration_seconds
      FROM pg_stat_activity 
      WHERE state IS NOT NULL
      GROUP BY state
      ORDER BY connection_count DESC
      `,
    ];

    for (const query of monitoringQueries) {
      try {
        await this.dataSource.query(query);
      } catch (error) {
        this.logger.warn('Failed to create monitoring view', error.message);
      }
    }
  }

  /**
   * Schedule maintenance tasks
   */
  private async scheduleMaintenanceTasks(): Promise<void> {
    // These would typically be scheduled as cron jobs in production
    this.logger.log('Performance maintenance tasks should be scheduled:');
    this.logger.log('1. Daily: VACUUM ANALYZE on large tables');
    this.logger.log('2. Weekly: REINDEX on heavily updated indexes');
    this.logger.log('3. Monthly: Full database VACUUM FULL (during maintenance window)');
    this.logger.log('4. Daily: Clean up old audit logs and temporary data');
  }

  /**
   * Get database performance metrics
   */
  async getPerformanceMetrics(): Promise<any> {
    const metrics = {};

    try {
      // Connection pool stats
      const connectionStats = await this.dataSource.query(`
        SELECT state, count(*) as count 
        FROM pg_stat_activity 
        GROUP BY state
      `);
      metrics['connections'] = connectionStats;

      // Cache hit ratio
      const cacheHitRatio = await this.dataSource.query(`
        SELECT 
          sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) * 100 as cache_hit_ratio
        FROM pg_statio_user_tables
      `);
      metrics['cache_hit_ratio'] = cacheHitRatio[0]?.cache_hit_ratio || 0;

      // Index usage
      const indexUsage = await this.dataSource.query(`
        SELECT 
          schemaname,
          tablename,
          count(*) as index_count,
          sum(idx_scan) as total_scans
        FROM pg_stat_user_indexes 
        GROUP BY schemaname, tablename
        ORDER BY total_scans DESC
        LIMIT 10
      `);
      metrics['top_indexed_tables'] = indexUsage;

      // Slow queries (if pg_stat_statements is available)
      try {
        const slowQueries = await this.dataSource.query(`
          SELECT 
            left(query, 100) as query_preview,
            calls,
            mean_time,
            total_time
          FROM pg_stat_statements 
          WHERE mean_time > 100
          ORDER BY mean_time DESC
          LIMIT 5
        `);
        metrics['slow_queries'] = slowQueries;
      } catch (error) {
        metrics['slow_queries'] = 'pg_stat_statements not available';
      }

      // Table sizes
      const tableSizes = await this.dataSource.query(`
        SELECT 
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 10
      `);
      metrics['largest_tables'] = tableSizes;

      return metrics;
    } catch (error) {
      this.logger.error('Failed to get performance metrics', error.stack);
      throw error;
    }
  }

  /**
   * Optimize specific table
   */
  async optimizeTable(tableName: string): Promise<void> {
    this.logger.log(`Optimizing table: ${tableName}`);

    try {
      // Analyze table statistics
      await this.dataSource.query(`ANALYZE ${tableName}`);
      
      // Vacuum table to reclaim space
      await this.dataSource.query(`VACUUM ${tableName}`);
      
      this.logger.log(`Table ${tableName} optimized successfully`);
    } catch (error) {
      this.logger.error(`Failed to optimize table ${tableName}`, error.stack);
      throw error;
    }
  }

  /**
   * Check for unused indexes
   */
  async findUnusedIndexes(): Promise<any[]> {
    const unusedIndexes = await this.dataSource.query(`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_scan,
        pg_size_pretty(pg_relation_size(indexrelid)) as size
      FROM pg_stat_user_indexes 
      WHERE idx_scan < 10 -- Less than 10 scans
      AND schemaname = 'public'
      ORDER BY pg_relation_size(indexrelid) DESC
    `);

    return unusedIndexes;
  }

  /**
   * Get query execution plan for optimization
   */
  async explainQuery(query: string): Promise<any[]> {
    const plan = await this.dataSource.query(`EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${query}`);
    return plan;
  }

  /**
   * Clean up old data for performance
   */
  async cleanupOldData(): Promise<void> {
    this.logger.log('Starting cleanup of old data...');

    const cleanupQueries = [
      // Clean up old audit logs (older than 90 days)
      `DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL '90 days'`,
      
      // Clean up expired tokens
      `UPDATE users SET refresh_token = NULL, refresh_token_expires_at = NULL 
       WHERE refresh_token_expires_at < NOW()`,
      
      // Clean up old LGPD requests (completed ones older than 1 year)
      `DELETE FROM lgpd_data_requests 
       WHERE status = 'completed' AND created_at < NOW() - INTERVAL '1 year'`,
      
      // Clean up expired consents
      `UPDATE lgpd_consents SET status = 'expired' 
       WHERE expires_at < NOW() AND status = 'active'`,
    ];

    for (const query of cleanupQueries) {
      try {
        const result = await this.dataSource.query(query);
        this.logger.log(`Cleanup query executed: ${result.length || 0} rows affected`);
      } catch (error) {
        this.logger.warn(`Cleanup query failed: ${query}`, error.message);
      }
    }

    // Vacuum after cleanup
    await this.dataSource.query('VACUUM ANALYZE');
    this.logger.log('Data cleanup completed');
  }
}
