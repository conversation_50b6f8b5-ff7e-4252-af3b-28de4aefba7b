import { 
  validateCampaignDates, 
  calculateIncentiveValue, 
  validateIncentiveRules,
  formatCurrency,
  isValidTimeZone,
  BusinessRuleError 
} from './business-rules.utils';

describe('Business Rules Utils', () => {
  describe('validateCampaignDates', () => {
    it('should accept valid date ranges', () => {
      const startDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow
      const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

      expect(() => validateCampaignDates(startDate, endDate)).not.toThrow();
    });

    it('should reject end date before start date', () => {
      const startDate = new Date('2024-01-31');
      const endDate = new Date('2024-01-01');
      
      expect(() => validateCampaignDates(startDate, endDate))
        .toThrow(BusinessRuleError);
    });

    it('should reject past start dates', () => {
      const startDate = new Date('2020-01-01');
      const endDate = new Date('2024-12-31');
      
      expect(() => validateCampaignDates(startDate, endDate))
        .toThrow(BusinessRuleError);
    });

    it('should reject campaigns longer than 1 year', () => {
      const startDate = new Date();
      const endDate = new Date(startDate.getTime() + 366 * 24 * 60 * 60 * 1000);
      
      expect(() => validateCampaignDates(startDate, endDate))
        .toThrow(BusinessRuleError);
    });
  });

  describe('calculateIncentiveValue', () => {
    it('should calculate percentage incentive correctly', () => {
      const result = calculateIncentiveValue(100, 'percentage', 10);
      expect(result).toBe(10);
    });

    it('should calculate fixed incentive correctly', () => {
      const result = calculateIncentiveValue(100, 'fixed', 15);
      expect(result).toBe(15);
    });

    it('should handle zero values', () => {
      expect(calculateIncentiveValue(0, 'percentage', 10)).toBe(0);
      expect(calculateIncentiveValue(100, 'percentage', 0)).toBe(0);
      expect(calculateIncentiveValue(100, 'fixed', 0)).toBe(0);
    });

    it('should throw error for invalid incentive type', () => {
      expect(() => calculateIncentiveValue(100, 'invalid' as any, 10))
        .toThrow(BusinessRuleError);
    });
  });

  describe('validateIncentiveRules', () => {
    const validRules = {
      minIncentiveValue: 5,
      maxIncentiveValue: 50,
      allowedTypes: ['percentage', 'fixed'] as const,
      maxCampaignDuration: 365,
    };

    it('should accept valid incentive rules', () => {
      expect(() => validateIncentiveRules(validRules)).not.toThrow();
    });

    it('should reject negative minimum incentive', () => {
      const invalidRules = { ...validRules, minIncentiveValue: -1 };
      expect(() => validateIncentiveRules(invalidRules))
        .toThrow(BusinessRuleError);
    });

    it('should reject max incentive lower than min', () => {
      const invalidRules = { 
        ...validRules, 
        minIncentiveValue: 50, 
        maxIncentiveValue: 10 
      };
      expect(() => validateIncentiveRules(invalidRules))
        .toThrow(BusinessRuleError);
    });

    it('should reject empty allowed types', () => {
      const invalidRules = { ...validRules, allowedTypes: [] as any };
      expect(() => validateIncentiveRules(invalidRules))
        .toThrow(BusinessRuleError);
    });
  });

  describe('formatCurrency', () => {
    it('should format Brazilian currency correctly', () => {
      expect(formatCurrency(1234.56)).toContain('1.234,56');
      expect(formatCurrency(0)).toContain('0,00');
      expect(formatCurrency(1000000)).toContain('1.000.000,00');
    });

    it('should handle decimal places correctly', () => {
      expect(formatCurrency(10.5)).toContain('10,50');
      expect(formatCurrency(10.123)).toContain('10,12');
    });

    it('should handle negative values', () => {
      const result = formatCurrency(-100);
      expect(result).toContain('100,00');
      expect(result).toContain('-');
    });
  });

  describe('isValidTimeZone', () => {
    it('should accept valid time zones', () => {
      expect(isValidTimeZone('America/Sao_Paulo')).toBe(true);
      expect(isValidTimeZone('UTC')).toBe(true);
      expect(isValidTimeZone('America/New_York')).toBe(true);
    });

    it('should reject invalid time zones', () => {
      expect(isValidTimeZone('Invalid/TimeZone')).toBe(false);
      expect(isValidTimeZone('')).toBe(false);
      expect(isValidTimeZone('GMT+3')).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(isValidTimeZone(null as any)).toBe(false);
      expect(isValidTimeZone(undefined as any)).toBe(false);
    });
  });

  describe('BusinessRuleError', () => {
    it('should create error with correct properties', () => {
      const error = new BusinessRuleError('Test error', 'TEST_CODE');
      
      expect(error.message).toBe('Test error');
      expect(error.code).toBe('TEST_CODE');
      expect(error.name).toBe('BusinessRuleError');
      expect(error instanceof Error).toBe(true);
    });

    it('should have default code if not provided', () => {
      const error = new BusinessRuleError('Test error');
      
      expect(error.code).toBe('BUSINESS_RULE_VIOLATION');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null and undefined inputs gracefully', () => {
      expect(() => calculateIncentiveValue(null as any, 'percentage', 10))
        .toThrow(BusinessRuleError);
      
      expect(() => calculateIncentiveValue(100, null as any, 10))
        .toThrow(BusinessRuleError);
    });

    it('should validate extreme values', () => {
      expect(() => calculateIncentiveValue(Number.MAX_VALUE, 'percentage', 1))
        .not.toThrow();
      
      expect(() => calculateIncentiveValue(100, 'percentage', Number.MAX_VALUE))
        .toThrow(BusinessRuleError);
    });

    it('should handle floating point precision', () => {
      const result = calculateIncentiveValue(0.1 + 0.2, 'percentage', 10);
      expect(result).toBeCloseTo(0.03, 2);
    });
  });
});
