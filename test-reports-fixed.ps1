Write-Host "Testing Reports API..." -ForegroundColor Cyan

$headers = @{
    "Content-Type" = "application/json"
}

$body = @{
    dateRange = @{
        startDate = "2025-01-01"
        endDate = "2025-12-31"
    }
    status = @("active", "paused")
} | ConvertTo-Json

Write-Host "`nTesting: POST http://localhost:8080/api/v1/reports/campaign-performance" -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/reports/campaign-performance" `
        -Method POST `
        -Headers $headers `
        -Body $body `
        -UseBasicParsing

    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Green
    $response.Content | ConvertFrom-Json | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

