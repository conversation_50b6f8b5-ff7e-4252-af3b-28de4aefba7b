import { useState, useEffect, useCallback } from 'react';
import { Product } from '../types';
import toast from 'react-hot-toast';

const MOCK_PRODUCTS: Product[] = [
  {
    id: '1',
    sku: 'SHP001',
    name: 'Shampoo Anticaspa Premium',
    brand: 'BeautyBrand',
    category: 'Higiene e Beleza',
    price: 29.90,
    isActive: true,
    isInActiveCampaign: false,
    description: 'Shampoo anticaspa com fórmula avançada'
  },
  {
    id: '2',
    sku: 'SHP002',
    name: 'Shampoo Hidratante Natural',
    brand: 'BeautyBrand',
    category: 'Higiene e Beleza',
    price: 24.90,
    isActive: true,
    isInActiveCampaign: true,
    description: 'Shampoo com ingredientes naturais'
  },
  {
    id: '3',
    sku: 'CND001',
    name: 'Condicionador Reparador',
    brand: 'BeautyBrand',
    category: 'Higiene e Beleza',
    price: 32.90,
    isActive: true,
    isInActiveCampaign: false,
    description: 'Condicionador para cabelos danificados'
  },
  {
    id: '4',
    sku: 'CRM001',
    name: 'Creme Dental Branqueador',
    brand: 'OralCare',
    category: 'Higiene Bucal',
    price: 8.90,
    isActive: true,
    isInActiveCampaign: false,
    description: 'Creme dental com ação branqueadora'
  },
  {
    id: '5',
    sku: 'SAB001',
    name: 'Sabonete Líquido Antibacteriano',
    brand: 'CleanBrand',
    category: 'Higiene Pessoal',
    price: 12.90,
    isActive: true,
    isInActiveCampaign: false,
    description: 'Sabonete líquido com proteção antibacteriana'
  }
];

export function useProductData() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadProducts = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setProducts(MOCK_PRODUCTS);
    } catch (err) {
      const errorMessage = 'Erro ao carregar produtos';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  return {
    products,
    isLoading,
    error,
    retryLoad: loadProducts
  };
}
