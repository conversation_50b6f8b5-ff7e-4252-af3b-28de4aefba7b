import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import ExportModal from '@/components/reports/ExportModal'

// Mock the API
jest.mock('@/lib/api', () => ({
  reportsAPI: {
    exportReport: jest.fn(),
  },
}))

const mockProps = {
  isOpen: true,
  onClose: jest.fn(),
  onExport: jest.fn(),
  filters: {
    campaignIds: ['1', '2'],
    status: ['active', 'paused'],
    search: 'test'
  },
  period: {
    type: 'preset' as const,
    preset: '30d'
  }
}

describe('ExportModal', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders when open', () => {
    render(<ExportModal {...mockProps} />)
    
    expect(screen.getByText('Exportar Relatório')).toBeInTheDocument()
    expect(screen.getByText('Formato de Exportação')).toBeInTheDocument()
    expect(screen.getByText('Tipo de Relatório')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<ExportModal {...mockProps} isOpen={false} />)
    
    expect(screen.queryByText('Exportar Relatório')).not.toBeInTheDocument()
  })

  it('displays translated status in filter summary', () => {
    render(<ExportModal {...mockProps} />)
    
    expect(screen.getByText(/Status: Ativas, Pausadas/)).toBeInTheDocument()
    expect(screen.getByText(/2 campanhas selecionadas/)).toBeInTheDocument()
    expect(screen.getByText(/Período: Últimos 30 dias/)).toBeInTheDocument()
  })

  it('allows format selection', async () => {
    const user = userEvent.setup()
    render(<ExportModal {...mockProps} />)
    
    // Excel should be selected by default
    expect(screen.getByText('Excel (XLSX)')).toBeInTheDocument()
    
    // Click PDF option
    const pdfOption = screen.getByText('PDF')
    await user.click(pdfOption)
    
    // Should show PDF as selected
    expect(screen.getByText('Relatório formatado para impressão')).toBeInTheDocument()
  })

  it('allows report type selection', async () => {
    const user = userEvent.setup()
    render(<ExportModal {...mockProps} />)
    
    // Click on a report type
    const incentiveAnalysis = screen.getByText('Análise de Incentivos')
    await user.click(incentiveAnalysis)
    
    expect(screen.getByText('ROI e efetividade dos incentivos')).toBeInTheDocument()
  })

  it('calls onClose when cancel button is clicked', async () => {
    const user = userEvent.setup()
    render(<ExportModal {...mockProps} />)
    
    const cancelButton = screen.getByText('Cancelar')
    await user.click(cancelButton)
    
    expect(mockProps.onClose).toHaveBeenCalledTimes(1)
  })

  it('calls onClose when X button is clicked', async () => {
    const user = userEvent.setup()
    render(<ExportModal {...mockProps} />)
    
    const closeButton = screen.getByRole('button', { name: /close/i })
    await user.click(closeButton)
    
    expect(mockProps.onClose).toHaveBeenCalledTimes(1)
  })

  it('calls onExport with correct options when export button is clicked', async () => {
    const user = userEvent.setup()
    render(<ExportModal {...mockProps} />)
    
    // Select PDF format
    const pdfOption = screen.getByText('PDF')
    await user.click(pdfOption)
    
    // Select report type
    const productPerformance = screen.getByText('Performance de Produtos')
    await user.click(productPerformance)
    
    // Click export button
    const exportButton = screen.getByText('Exportar')
    await user.click(exportButton)
    
    expect(mockProps.onExport).toHaveBeenCalledWith({
      format: 'pdf',
      reportType: 'product',
      includeCharts: true,
      includeRawData: true
    })
  })

  it('shows loading state during export', async () => {
    const user = userEvent.setup()
    
    // Mock a slow export
    const slowExport = jest.fn(() => new Promise(resolve => setTimeout(resolve, 1000)))
    render(<ExportModal {...mockProps} onExport={slowExport} />)
    
    const exportButton = screen.getByText('Exportar')
    await user.click(exportButton)
    
    expect(screen.getByText('Exportando...')).toBeInTheDocument()
    expect(exportButton).toBeDisabled()
  })

  it('handles export options correctly', async () => {
    const user = userEvent.setup()
    render(<ExportModal {...mockProps} />)

    // Uncheck include charts
    const chartsCheckbox = screen.getByLabelText(/Incluir Gráficos/i)
    await user.click(chartsCheckbox)

    // Uncheck include raw data
    const rawDataCheckbox = screen.getByLabelText(/Incluir Dados Brutos/i)
    await user.click(rawDataCheckbox)

    const exportButton = screen.getByText('Exportar')
    await user.click(exportButton)

    expect(mockProps.onExport).toHaveBeenCalledWith({
      format: 'excel',
      reportType: 'campaign',
      includeCharts: false,
      includeRawData: false
    })
  })

  it('displays correct filter summary for different filter combinations', () => {
    const customProps = {
      ...mockProps,
      filters: {
        campaignIds: [],
        status: ['ended'],
        search: ''
      },
      period: {
        type: 'custom' as const,
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      }
    }

    render(<ExportModal {...customProps} />)

    expect(screen.getByText(/Status: Concluídas/)).toBeInTheDocument()
    expect(screen.getByText(/Período:/)).toBeInTheDocument()
  })
})
