/**
 * Validation utilities for the auth service
 * Following Clean Code principles and SOLID design
 */

/**
 * Validates if an email is from a corporate domain (not public)
 * @param email - Email address to validate
 * @returns true if corporate email, false otherwise
 */
export function isValidCorporateEmail(email: string): boolean {
  if (!email || typeof email !== 'string') {
    return false;
  }

  // Basic email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return false;
  }

  // List of public email domains to reject
  const publicDomains = [
    'gmail.com',
    'hotmail.com',
    'yahoo.com',
    'outlook.com',
    'live.com',
    'icloud.com',
    'aol.com',
    'protonmail.com',
    'mail.com',
    'yandex.com',
  ];

  const domain = email.split('@')[1]?.toLowerCase();
  return !publicDomains.includes(domain);
}

/**
 * Formats a CPF string with proper masking
 * @param value - Raw CPF string
 * @returns Formatted CPF (000.000.000-00)
 */
export function formatCPF(value: string): string {
  if (!value || typeof value !== 'string') {
    return '';
  }

  // Remove all non-numeric characters
  const numbers = value.replace(/\D/g, '');
  
  if (numbers.length === 0) {
    return '';
  }

  // Apply CPF mask
  if (numbers.length <= 3) {
    return numbers;
  } else if (numbers.length <= 6) {
    return numbers.replace(/(\d{3})(\d+)/, '$1.$2');
  } else if (numbers.length <= 9) {
    return numbers.replace(/(\d{3})(\d{3})(\d+)/, '$1.$2.$3');
  } else {
    return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  }
}

/**
 * Validates CPF format (not algorithm, just format)
 * @param cpf - CPF string to validate
 * @returns true if format is valid
 */
export function validateCPF(cpf: string): boolean {
  if (!cpf || typeof cpf !== 'string') {
    return false;
  }

  // Check if CPF matches the format 000.000.000-00
  const cpfRegex = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/;
  return cpfRegex.test(cpf);
}

/**
 * Validates if a password meets security requirements
 * @param password - Password to validate
 * @returns Object with validation result and errors
 */
export function validatePassword(password: string): { 
  isValid: boolean; 
  errors: string[] 
} {
  const errors: string[] = [];

  if (!password || typeof password !== 'string') {
    errors.push('Senha é obrigatória');
    return { isValid: false, errors };
  }

  if (password.length < 8) {
    errors.push('Senha deve ter pelo menos 8 caracteres');
  }

  if (password.length > 128) {
    errors.push('Senha deve ter no máximo 128 caracteres');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Senha deve conter pelo menos uma letra minúscula');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Senha deve conter pelo menos uma letra maiúscula');
  }

  if (!/\d/.test(password)) {
    errors.push('Senha deve conter pelo menos um número');
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Senha deve conter pelo menos um caractere especial');
  }

  // Check for common weak passwords
  const commonPasswords = [
    'password',
    '123456',
    '12345678',
    'qwerty',
    'abc123',
    'password123',
    'admin',
    'letmein',
  ];

  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push('Senha muito comum, escolha uma senha mais segura');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Sanitizes user input to prevent XSS attacks
 * @param input - User input string
 * @returns Sanitized string
 */
export function sanitizeInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  return input
    .replace(/[<>]/g, '') // Remove < and > characters
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Validates if a string is a valid UUID v4
 * @param uuid - String to validate
 * @returns true if valid UUID v4
 */
export function isValidUUID(uuid: string): boolean {
  if (!uuid || typeof uuid !== 'string') {
    return false;
  }

  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Validates if a phone number is in Brazilian format
 * @param phone - Phone number to validate
 * @returns true if valid Brazilian phone format
 */
export function validateBrazilianPhone(phone: string): boolean {
  if (!phone || typeof phone !== 'string') {
    return false;
  }

  // Remove all non-numeric characters
  const numbers = phone.replace(/\D/g, '');

  // Brazilian phone numbers: 11 digits (with area code and 9)
  // Format: (11) 99999-9999 or 11999999999
  return numbers.length === 11 && numbers.startsWith('1');
}

/**
 * Formats a Brazilian phone number
 * @param phone - Raw phone number
 * @returns Formatted phone number
 */
export function formatBrazilianPhone(phone: string): string {
  if (!phone || typeof phone !== 'string') {
    return '';
  }

  const numbers = phone.replace(/\D/g, '');
  
  if (numbers.length !== 11) {
    return phone; // Return original if not 11 digits
  }

  return numbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
}
