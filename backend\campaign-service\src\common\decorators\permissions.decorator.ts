import { SetMetadata } from '@nestjs/common';
import { PermissionResource, PermissionAction } from '../../database/entities/permission.entity';

export interface RequiredPermission {
  resource: PermissionResource;
  action: PermissionAction;
  scope?: string;
}

export const PERMISSIONS_KEY = 'permissions';

export const RequirePermissions = (...permissions: RequiredPermission[]) =>
  SetMetadata(PERMISSIONS_KEY, permissions);

// Convenience decorators for common permissions
export const RequirePermission = (resource: PermissionResource, action: PermissionAction, scope?: string) =>
  RequirePermissions({ resource, action, scope });

// Campaign permissions
export const CanCreateCampaigns = () => RequirePermission(PermissionResource.CAMPAIGNS, PermissionAction.CREATE);
export const CanReadCampaigns = () => RequirePermission(PermissionResource.CAMPAIGNS, PermissionAction.READ);
export const CanUpdateCampaigns = () => RequirePermission(PermissionResource.CAMPAIGNS, PermissionAction.UPDATE);
export const CanDeleteCampaigns = () => RequirePermission(PermissionResource.CAMPAIGNS, PermissionAction.DELETE);

// User permissions
export const CanCreateUsers = () => RequirePermission(PermissionResource.USERS, PermissionAction.CREATE);
export const CanReadUsers = () => RequirePermission(PermissionResource.USERS, PermissionAction.READ);
export const CanUpdateUsers = () => RequirePermission(PermissionResource.USERS, PermissionAction.UPDATE);
export const CanDeleteUsers = () => RequirePermission(PermissionResource.USERS, PermissionAction.DELETE);

// Role permissions
export const CanCreateRoles = () => RequirePermission(PermissionResource.ROLES, PermissionAction.CREATE);
export const CanReadRoles = () => RequirePermission(PermissionResource.ROLES, PermissionAction.READ);
export const CanUpdateRoles = () => RequirePermission(PermissionResource.ROLES, PermissionAction.UPDATE);
export const CanDeleteRoles = () => RequirePermission(PermissionResource.ROLES, PermissionAction.DELETE);

// Report permissions
export const CanReadReports = () => RequirePermission(PermissionResource.REPORTS, PermissionAction.READ);
export const CanExportReports = () => RequirePermission(PermissionResource.REPORTS, PermissionAction.EXPORT);

// Invitation permissions
export const CanCreateInvitations = () => RequirePermission(PermissionResource.INVITATIONS, PermissionAction.CREATE);
export const CanReadInvitations = () => RequirePermission(PermissionResource.INVITATIONS, PermissionAction.READ);
export const CanDeleteInvitations = () => RequirePermission(PermissionResource.INVITATIONS, PermissionAction.DELETE);
