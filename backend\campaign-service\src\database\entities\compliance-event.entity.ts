import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

export enum ComplianceType {
  LGPD = 'lgpd', // Lei Geral de Proteção de Dados (Brazil)
  GDPR = 'gdpr', // General Data Protection Regulation (EU)
  CCPA = 'ccpa', // California Consumer Privacy Act (US)
  SOX = 'sox', // Sarbanes-Oxley Act
  PCI_DSS = 'pci_dss', // Payment Card Industry Data Security Standard
  HIPAA = 'hipaa', // Health Insurance Portability and Accountability Act
  ISO_27001 = 'iso_27001',
  CUSTOM = 'custom',
}

export enum ComplianceEventType {
  DATA_COLLECTION = 'data_collection',
  DATA_PROCESSING = 'data_processing',
  DATA_SHARING = 'data_sharing',
  DATA_DELETION = 'data_deletion',
  DATA_ANONYMIZATION = 'data_anonymization',
  DATA_ACCESS = 'data_access',
  CONSENT_GIVEN = 'consent_given',
  CONSENT_WITHDRAWN = 'consent_withdrawn',
  DATA_SUBJECT_REQUEST = 'data_subject_request',
  DATA_BREACH = 'data_breach',
  DATA_EXPORT = 'data_export',
  DATA_RETENTION = 'data_retention',
  PRIVACY_POLICY_UPDATE = 'privacy_policy_update',
  TERMS_ACCEPTANCE = 'terms_acceptance',
  COOKIE_CONSENT = 'cookie_consent',
  MARKETING_CONSENT = 'marketing_consent',
  PROFILING_CONSENT = 'profiling_consent',
  THIRD_PARTY_SHARING = 'third_party_sharing',
  CROSS_BORDER_TRANSFER = 'cross_border_transfer',
  DATA_MINIMIZATION = 'data_minimization',
  PURPOSE_LIMITATION = 'purpose_limitation',
}

export enum ComplianceStatus {
  COMPLIANT = 'compliant',
  NON_COMPLIANT = 'non_compliant',
  PENDING_REVIEW = 'pending_review',
  REQUIRES_ACTION = 'requires_action',
  EXEMPTED = 'exempted',
  UNDER_INVESTIGATION = 'under_investigation',
}

@Entity('compliance_events')
@Index(['userId', 'timestamp'])
@Index(['type', 'timestamp'])
@Index(['eventType', 'timestamp'])
@Index(['status', 'timestamp'])
@Index(['dataSubjectId', 'timestamp'])
@Index(['timestamp'])
@Index(['isAutomated'])
export class ComplianceEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  userId: string; // User who triggered the event

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  dataSubjectId: string; // Person whose data is involved

  @Column({ type: 'enum', enum: ComplianceType })
  type: ComplianceType;

  @Column({ type: 'enum', enum: ComplianceEventType })
  eventType: ComplianceEventType;

  @Column({ type: 'enum', enum: ComplianceStatus, default: ComplianceStatus.COMPLIANT })
  status: ComplianceStatus;

  @Column({ type: 'varchar', length: 500 })
  description: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  legalBasis: string; // Legal basis for processing (LGPD/GDPR)

  @Column({ type: 'varchar', length: 255, nullable: true })
  processingPurpose: string; // Purpose of data processing

  @Column({ type: 'json', nullable: true })
  dataCategories: string[]; // Categories of personal data involved

  @Column({ type: 'json', nullable: true })
  dataFields: string[]; // Specific data fields involved

  @Column({ type: 'varchar', length: 255, nullable: true })
  dataSource: string; // Where the data came from

  @Column({ type: 'varchar', length: 255, nullable: true })
  dataDestination: string; // Where the data is going

  @Column({ type: 'json', nullable: true })
  consentDetails: Record<string, any>; // Consent information

  @Column({ type: 'timestamp', nullable: true })
  consentTimestamp: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  consentMethod: string; // How consent was obtained

  @Column({ type: 'boolean', default: false })
  @Index()
  isAutomated: boolean; // Automated processing

  @Column({ type: 'boolean', default: false })
  isProfiling: boolean; // Involves profiling

  @Column({ type: 'boolean', default: false })
  isCrossBorder: boolean; // Cross-border data transfer

  @Column({ type: 'varchar', length: 255, nullable: true })
  recipientCountry: string; // Country receiving data

  @Column({ type: 'varchar', length: 255, nullable: true })
  adequacyDecision: string; // EU adequacy decision

  @Column({ type: 'json', nullable: true })
  safeguards: string[]; // Safeguards in place

  @Column({ type: 'int', nullable: true })
  retentionPeriod: number; // Data retention period in days

  @Column({ type: 'timestamp', nullable: true })
  scheduledDeletion: Date; // When data should be deleted

  @Column({ type: 'varchar', length: 255, nullable: true })
  requestType: string; // Type of data subject request

  @Column({ type: 'varchar', length: 255, nullable: true })
  requestId: string; // Reference to data subject request

  @Column({ type: 'timestamp', nullable: true })
  responseDeadline: Date; // Deadline to respond to request

  @Column({ type: 'timestamp', nullable: true })
  responseDate: Date; // When response was provided

  @Column({ type: 'varchar', length: 255, nullable: true })
  responseMethod: string; // How response was provided

  @Column({ type: 'json', nullable: true })
  breachDetails: Record<string, any>; // Data breach information

  @Column({ type: 'boolean', default: false })
  isNotifiable: boolean; // Must be reported to authorities

  @Column({ type: 'timestamp', nullable: true })
  notificationDate: Date; // When authorities were notified

  @Column({ type: 'varchar', length: 255, nullable: true })
  notificationReference: string; // Authority notification reference

  @Column({ type: 'json', nullable: true })
  mitigationMeasures: string[]; // Measures taken

  @Column({ type: 'varchar', length: 255, nullable: true })
  responsiblePerson: string; // DPO or responsible person

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  evidence: Record<string, any>; // Supporting evidence

  @Column({ type: 'varchar', length: 255, nullable: true })
  correlationId: string; // For grouping related events

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  @Index()
  timestamp: Date;

  // Helper methods
  static createComplianceEvent(data: {
    userId?: string;
    dataSubjectId?: string;
    type: ComplianceType;
    eventType: ComplianceEventType;
    status?: ComplianceStatus;
    description: string;
    legalBasis?: string;
    processingPurpose?: string;
    dataCategories?: string[];
    dataFields?: string[];
    dataSource?: string;
    dataDestination?: string;
    consentDetails?: Record<string, any>;
    consentTimestamp?: Date;
    consentMethod?: string;
    isAutomated?: boolean;
    isProfiling?: boolean;
    isCrossBorder?: boolean;
    recipientCountry?: string;
    adequacyDecision?: string;
    safeguards?: string[];
    retentionPeriod?: number;
    scheduledDeletion?: Date;
    requestType?: string;
    requestId?: string;
    responseDeadline?: Date;
    responseDate?: Date;
    responseMethod?: string;
    breachDetails?: Record<string, any>;
    isNotifiable?: boolean;
    notificationDate?: Date;
    notificationReference?: string;
    mitigationMeasures?: string[];
    responsiblePerson?: string;
    notes?: string;
    evidence?: Record<string, any>;
    correlationId?: string;
    metadata?: Record<string, any>;
  }): Partial<ComplianceEvent> {
    return {
      userId: data.userId,
      dataSubjectId: data.dataSubjectId,
      type: data.type,
      eventType: data.eventType,
      status: data.status || ComplianceStatus.COMPLIANT,
      description: data.description,
      legalBasis: data.legalBasis,
      processingPurpose: data.processingPurpose,
      dataCategories: data.dataCategories,
      dataFields: data.dataFields,
      dataSource: data.dataSource,
      dataDestination: data.dataDestination,
      consentDetails: data.consentDetails,
      consentTimestamp: data.consentTimestamp,
      consentMethod: data.consentMethod,
      isAutomated: data.isAutomated || false,
      isProfiling: data.isProfiling || false,
      isCrossBorder: data.isCrossBorder || false,
      recipientCountry: data.recipientCountry,
      adequacyDecision: data.adequacyDecision,
      safeguards: data.safeguards,
      retentionPeriod: data.retentionPeriod,
      scheduledDeletion: data.scheduledDeletion,
      requestType: data.requestType,
      requestId: data.requestId,
      responseDeadline: data.responseDeadline,
      responseDate: data.responseDate,
      responseMethod: data.responseMethod,
      breachDetails: data.breachDetails,
      isNotifiable: data.isNotifiable || false,
      notificationDate: data.notificationDate,
      notificationReference: data.notificationReference,
      mitigationMeasures: data.mitigationMeasures,
      responsiblePerson: data.responsiblePerson,
      notes: data.notes,
      evidence: data.evidence,
      correlationId: data.correlationId,
      metadata: data.metadata,
    };
  }

  getTypeLabel(): string {
    const labels = {
      [ComplianceType.LGPD]: 'LGPD',
      [ComplianceType.GDPR]: 'GDPR',
      [ComplianceType.CCPA]: 'CCPA',
      [ComplianceType.SOX]: 'SOX',
      [ComplianceType.PCI_DSS]: 'PCI DSS',
      [ComplianceType.HIPAA]: 'HIPAA',
      [ComplianceType.ISO_27001]: 'ISO 27001',
      [ComplianceType.CUSTOM]: 'Personalizado',
    };

    return labels[this.type] || this.type;
  }

  getEventTypeLabel(): string {
    const labels = {
      [ComplianceEventType.DATA_COLLECTION]: 'Coleta de Dados',
      [ComplianceEventType.DATA_PROCESSING]: 'Processamento de Dados',
      [ComplianceEventType.DATA_SHARING]: 'Compartilhamento de Dados',
      [ComplianceEventType.DATA_DELETION]: 'Exclusão de Dados',
      [ComplianceEventType.DATA_ANONYMIZATION]: 'Anonimização de Dados',
      [ComplianceEventType.CONSENT_GIVEN]: 'Consentimento Concedido',
      [ComplianceEventType.CONSENT_WITHDRAWN]: 'Consentimento Retirado',
      [ComplianceEventType.DATA_SUBJECT_REQUEST]: 'Solicitação do Titular',
      [ComplianceEventType.DATA_BREACH]: 'Violação de Dados',
      [ComplianceEventType.DATA_EXPORT]: 'Exportação de Dados',
      [ComplianceEventType.DATA_RETENTION]: 'Retenção de Dados',
      [ComplianceEventType.PRIVACY_POLICY_UPDATE]: 'Atualização da Política de Privacidade',
      [ComplianceEventType.TERMS_ACCEPTANCE]: 'Aceitação de Termos',
      [ComplianceEventType.COOKIE_CONSENT]: 'Consentimento de Cookies',
      [ComplianceEventType.MARKETING_CONSENT]: 'Consentimento de Marketing',
      [ComplianceEventType.PROFILING_CONSENT]: 'Consentimento de Perfilagem',
      [ComplianceEventType.THIRD_PARTY_SHARING]: 'Compartilhamento com Terceiros',
      [ComplianceEventType.CROSS_BORDER_TRANSFER]: 'Transferência Internacional',
      [ComplianceEventType.DATA_MINIMIZATION]: 'Minimização de Dados',
      [ComplianceEventType.PURPOSE_LIMITATION]: 'Limitação de Finalidade',
    };

    return labels[this.eventType] || this.eventType;
  }

  getStatusLabel(): string {
    const labels = {
      [ComplianceStatus.COMPLIANT]: 'Conforme',
      [ComplianceStatus.NON_COMPLIANT]: 'Não Conforme',
      [ComplianceStatus.PENDING_REVIEW]: 'Pendente de Revisão',
      [ComplianceStatus.REQUIRES_ACTION]: 'Requer Ação',
      [ComplianceStatus.EXEMPTED]: 'Isento',
      [ComplianceStatus.UNDER_INVESTIGATION]: 'Em Investigação',
    };

    return labels[this.status] || this.status;
  }

  isOverdue(): boolean {
    if (!this.responseDeadline) return false;
    return new Date() > this.responseDeadline && !this.responseDate;
  }

  getDaysUntilDeadline(): number | null {
    if (!this.responseDeadline) return null;
    
    const now = new Date();
    const diffMs = this.responseDeadline.getTime() - now.getTime();
    return Math.ceil(diffMs / (1000 * 60 * 60 * 24));
  }

  requiresNotification(): boolean {
    const notifiableEvents = [
      ComplianceEventType.DATA_BREACH,
      ComplianceEventType.DATA_SUBJECT_REQUEST,
    ];

    return (
      this.isNotifiable ||
      notifiableEvents.includes(this.eventType) ||
      this.status === ComplianceStatus.NON_COMPLIANT
    );
  }

  isHighRisk(): boolean {
    const highRiskEvents = [
      ComplianceEventType.DATA_BREACH,
      ComplianceEventType.DATA_SHARING,
      ComplianceEventType.CROSS_BORDER_TRANSFER,
      ComplianceEventType.CONSENT_WITHDRAWN,
    ];

    return (
      highRiskEvents.includes(this.eventType) ||
      this.status === ComplianceStatus.NON_COMPLIANT ||
      this.isProfiling ||
      this.isCrossBorder ||
      this.isOverdue()
    );
  }
}
