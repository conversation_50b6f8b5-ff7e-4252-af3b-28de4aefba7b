import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual, MoreThanOrEqual, Not } from 'typeorm';
import { CampaignCycle, CycleStatus, CycleType, CyclePeriod } from '../database/entities/campaign-cycle.entity';

export class CreateCycleDto {
  name: string;
  description?: string;
  industryId: string;
  type: CycleType;
  period: CyclePeriod;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  isRecurring?: boolean;
  recurringCount?: number;
  recurringInterval?: number;
  maxCampaigns?: number;
  maxProducts?: number;
  maxBudget?: number;
  maxIncentiveValue?: number;
  maxIncentivePercentage?: number;
  maxParticipants?: number;
  allowedDaysOfWeek?: number[];
  excludedDates?: string[];
  businessHours?: { start: string; end: string };
  timezone?: string;
  allowWeekends?: boolean;
  allowHolidays?: boolean;
  requiresApproval?: boolean;
  notifyOnLimitReached?: boolean;
  warningThresholdPercentage?: number;
  notificationEmails?: string[];
  metadata?: Record<string, any>;
  tags?: string[];
}

export class UpdateCycleDto {
  name?: string;
  description?: string;
  type?: CycleType;
  period?: CyclePeriod;
  startDate?: string;
  endDate?: string;
  isRecurring?: boolean;
  recurringCount?: number;
  recurringInterval?: number;
  maxCampaigns?: number;
  maxProducts?: number;
  maxBudget?: number;
  maxIncentiveValue?: number;
  maxIncentivePercentage?: number;
  maxParticipants?: number;
  allowedDaysOfWeek?: number[];
  excludedDates?: string[];
  businessHours?: { start: string; end: string };
  timezone?: string;
  allowWeekends?: boolean;
  allowHolidays?: boolean;
  requiresApproval?: boolean;
  notifyOnLimitReached?: boolean;
  warningThresholdPercentage?: number;
  notificationEmails?: string[];
  metadata?: Record<string, any>;
  tags?: string[];
  status?: CycleStatus;
  isActive?: boolean;
}

export interface CycleUsageUpdate {
  campaigns?: number;
  products?: number;
  budgetUsed?: number;
  incentiveValue?: number;
  participants?: number;
}

export interface CycleLimitCheck {
  canProceed: boolean;
  limitType?: string;
  currentUsage: number;
  maxLimit: number;
  remainingCapacity: number;
  usagePercentage: number;
  warnings: string[];
  errors: string[];
}

@Injectable()
export class CycleManagementService {
  private readonly logger = new Logger(CycleManagementService.name);

  constructor(
    @InjectRepository(CampaignCycle)
    private readonly cycleRepository: Repository<CampaignCycle>,
  ) {}

  async createCycle(createDto: CreateCycleDto, userId: string): Promise<CampaignCycle> {
    // Validate dates
    const startDate = new Date(createDto.startDate);
    const endDate = new Date(createDto.endDate);

    if (startDate >= endDate) {
      throw new BadRequestException('Data de início deve ser anterior à data de fim');
    }

    // Check for overlapping cycles
    const overlappingCycles = await this.cycleRepository.find({
      where: {
        industryId: createDto.industryId,
        status: CycleStatus.ACTIVE,
        startDate: LessThanOrEqual(endDate),
        endDate: MoreThanOrEqual(startDate),
      },
    });

    if (overlappingCycles.length > 0) {
      throw new BadRequestException('Já existe um ciclo ativo no período especificado');
    }

    const cycle = this.cycleRepository.create({
      ...createDto,
      startDate,
      endDate,
      timezone: createDto.timezone || 'America/Sao_Paulo',
      createdBy: userId,
    });

    const savedCycle = await this.cycleRepository.save(cycle);
    
    this.logger.log(`Cycle created: ${savedCycle.id} by user ${userId}`);
    
    return savedCycle;
  }

  async updateCycle(id: string, updateDto: UpdateCycleDto, userId: string): Promise<CampaignCycle> {
    const cycle = await this.cycleRepository.findOne({ where: { id } });
    
    if (!cycle) {
      throw new NotFoundException('Ciclo não encontrado');
    }

    // Validate date changes
    if (updateDto.startDate || updateDto.endDate) {
      const startDate = updateDto.startDate ? new Date(updateDto.startDate) : cycle.startDate;
      const endDate = updateDto.endDate ? new Date(updateDto.endDate) : cycle.endDate;

      if (startDate >= endDate) {
        throw new BadRequestException('Data de início deve ser anterior à data de fim');
      }

      // Check for overlapping cycles (excluding current cycle)
      const overlappingCycles = await this.cycleRepository.find({
        where: {
          industryId: cycle.industryId,
          status: CycleStatus.ACTIVE,
          startDate: LessThanOrEqual(endDate),
          endDate: MoreThanOrEqual(startDate),
          id: Not(id),
        },
      });

      if (overlappingCycles.length > 0) {
        throw new BadRequestException('Já existe um ciclo ativo no período especificado');
      }

      updateDto.startDate = startDate.toISOString();
      updateDto.endDate = endDate.toISOString();
    }

    Object.assign(cycle, updateDto, { updatedBy: userId });
    
    const updatedCycle = await this.cycleRepository.save(cycle);
    
    this.logger.log(`Cycle updated: ${id} by user ${userId}`);
    
    return updatedCycle;
  }

  async deleteCycle(id: string): Promise<void> {
    const cycle = await this.cycleRepository.findOne({ where: { id } });
    
    if (!cycle) {
      throw new NotFoundException('Ciclo não encontrado');
    }

    // Check if cycle has active campaigns
    if (cycle.currentCampaigns > 0) {
      throw new BadRequestException('Não é possível excluir ciclo com campanhas ativas');
    }

    cycle.status = CycleStatus.CANCELLED;
    cycle.isActive = false;
    
    await this.cycleRepository.save(cycle);
    
    this.logger.log(`Cycle cancelled: ${id}`);
  }

  async findAll(industryId?: string, status?: CycleStatus): Promise<CampaignCycle[]> {
    const query = this.cycleRepository.createQueryBuilder('cycle')
      .orderBy('cycle.startDate', 'DESC');

    if (industryId) {
      query.andWhere('cycle.industryId = :industryId', { industryId });
    }

    if (status) {
      query.andWhere('cycle.status = :status', { status });
    }

    return query.getMany();
  }

  async findById(id: string): Promise<CampaignCycle> {
    const cycle = await this.cycleRepository.findOne({ where: { id } });
    
    if (!cycle) {
      throw new NotFoundException('Ciclo não encontrado');
    }

    return cycle;
  }

  async getActiveCycles(industryId: string): Promise<CampaignCycle[]> {
    const now = new Date();
    
    return this.cycleRepository.find({
      where: {
        industryId,
        status: CycleStatus.ACTIVE,
        isActive: true,
        startDate: LessThanOrEqual(now),
        endDate: MoreThanOrEqual(now),
      },
      order: { startDate: 'ASC' },
    });
  }

  async getCurrentCycle(industryId: string): Promise<CampaignCycle | null> {
    const activeCycles = await this.getActiveCycles(industryId);
    return activeCycles.length > 0 ? activeCycles[0] : null;
  }

  async checkLimits(
    cycleId: string,
    type: 'campaigns' | 'products' | 'budget' | 'incentive' | 'participants',
    additionalAmount: number = 1
  ): Promise<CycleLimitCheck> {
    const cycle = await this.findById(cycleId);
    
    const currentUsage = this.getCurrentUsage(cycle, type);
    const maxLimit = this.getMaxLimit(cycle, type);
    const newUsage = currentUsage + additionalAmount;
    
    const canProceed = !maxLimit || newUsage <= maxLimit;
    const remainingCapacity = maxLimit ? Math.max(0, maxLimit - currentUsage) : Infinity;
    const usagePercentage = maxLimit ? (currentUsage / maxLimit) * 100 : 0;
    
    const warnings: string[] = [];
    const errors: string[] = [];

    if (maxLimit) {
      if (newUsage > maxLimit) {
        errors.push(`Limite de ${type} excedido. Máximo: ${maxLimit}, Tentativa: ${newUsage}`);
      } else if (cycle.isWarningThresholdReached(type)) {
        warnings.push(`Limite de ${type} próximo do máximo (${usagePercentage.toFixed(1)}%)`);
      }
    }

    return {
      canProceed,
      limitType: type,
      currentUsage,
      maxLimit: maxLimit || 0,
      remainingCapacity: remainingCapacity === Infinity ? 0 : remainingCapacity,
      usagePercentage,
      warnings,
      errors,
    };
  }

  async updateUsage(cycleId: string, usage: CycleUsageUpdate): Promise<CampaignCycle> {
    const cycle = await this.findById(cycleId);
    
    if (usage.campaigns !== undefined) {
      cycle.currentCampaigns += usage.campaigns;
    }
    
    if (usage.products !== undefined) {
      cycle.currentProducts += usage.products;
    }
    
    if (usage.budgetUsed !== undefined) {
      cycle.currentBudgetUsed += usage.budgetUsed;
    }
    
    if (usage.incentiveValue !== undefined) {
      cycle.currentIncentiveValue += usage.incentiveValue;
    }
    
    if (usage.participants !== undefined) {
      cycle.currentParticipants += usage.participants;
    }

    const updatedCycle = await this.cycleRepository.save(cycle);
    
    // Check for warning thresholds and send notifications if needed
    await this.checkAndSendWarnings(updatedCycle);
    
    return updatedCycle;
  }

  async getCycleStatistics(cycleId: string): Promise<{
    cycle: CampaignCycle;
    usage: {
      campaigns: { current: number; max: number; percentage: number };
      products: { current: number; max: number; percentage: number };
      budget: { current: number; max: number; percentage: number };
      incentive: { current: number; max: number; percentage: number };
      participants: { current: number; max: number; percentage: number };
    };
    timeline: {
      daysTotal: number;
      daysElapsed: number;
      daysRemaining: number;
      progressPercentage: number;
    };
    warnings: string[];
  }> {
    const cycle = await this.findById(cycleId);
    
    const now = new Date();
    const totalDays = Math.ceil((cycle.endDate.getTime() - cycle.startDate.getTime()) / (1000 * 60 * 60 * 24));
    const elapsedDays = Math.max(0, Math.ceil((now.getTime() - cycle.startDate.getTime()) / (1000 * 60 * 60 * 24)));
    const remainingDays = Math.max(0, cycle.getDaysRemaining());
    const progressPercentage = totalDays > 0 ? (elapsedDays / totalDays) * 100 : 0;

    const warnings: string[] = [];
    
    // Check all warning thresholds
    ['campaigns', 'products', 'budget', 'incentive', 'participants'].forEach(type => {
      if (cycle.isWarningThresholdReached(type as any)) {
        const percentage = cycle.getUsagePercentage(type as any);
        warnings.push(`Limite de ${type} próximo do máximo (${percentage.toFixed(1)}%)`);
      }
    });

    return {
      cycle,
      usage: {
        campaigns: {
          current: cycle.currentCampaigns,
          max: cycle.maxCampaigns || 0,
          percentage: cycle.getUsagePercentage('campaigns'),
        },
        products: {
          current: cycle.currentProducts,
          max: cycle.maxProducts || 0,
          percentage: cycle.getUsagePercentage('products'),
        },
        budget: {
          current: cycle.currentBudgetUsed,
          max: cycle.maxBudget || 0,
          percentage: cycle.getUsagePercentage('budget'),
        },
        incentive: {
          current: cycle.currentIncentiveValue,
          max: cycle.maxIncentiveValue || 0,
          percentage: cycle.getUsagePercentage('incentive'),
        },
        participants: {
          current: cycle.currentParticipants,
          max: cycle.maxParticipants || 0,
          percentage: cycle.getUsagePercentage('participants'),
        },
      },
      timeline: {
        daysTotal: totalDays,
        daysElapsed: elapsedDays,
        daysRemaining: remainingDays,
        progressPercentage,
      },
      warnings,
    };
  }

  private getCurrentUsage(cycle: CampaignCycle, type: string): number {
    switch (type) {
      case 'campaigns': return cycle.currentCampaigns;
      case 'products': return cycle.currentProducts;
      case 'budget': return cycle.currentBudgetUsed;
      case 'incentive': return cycle.currentIncentiveValue;
      case 'participants': return cycle.currentParticipants;
      default: return 0;
    }
  }

  private getMaxLimit(cycle: CampaignCycle, type: string): number | null {
    switch (type) {
      case 'campaigns': return cycle.maxCampaigns;
      case 'products': return cycle.maxProducts;
      case 'budget': return cycle.maxBudget;
      case 'incentive': return cycle.maxIncentiveValue;
      case 'participants': return cycle.maxParticipants;
      default: return null;
    }
  }

  private async checkAndSendWarnings(cycle: CampaignCycle): Promise<void> {
    if (!cycle.notifyOnLimitReached || !cycle.notificationEmails?.length) {
      return;
    }

    const warnings: string[] = [];
    
    ['campaigns', 'products', 'budget', 'incentive', 'participants'].forEach(type => {
      if (cycle.isWarningThresholdReached(type as any)) {
        const percentage = cycle.getUsagePercentage(type as any);
        warnings.push(`${type}: ${percentage.toFixed(1)}%`);
      }
    });

    if (warnings.length > 0) {
      this.logger.warn(`Cycle ${cycle.id} warning thresholds reached: ${warnings.join(', ')}`);
      // Here you would implement email notification logic
    }
  }
}
