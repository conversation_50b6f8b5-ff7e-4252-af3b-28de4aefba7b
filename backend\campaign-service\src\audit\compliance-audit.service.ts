import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { ComplianceEvent, ComplianceType, ComplianceEventType } from '../database/entities/compliance-event.entity';

@Injectable()
export class ComplianceAuditService {
  private readonly logger = new Logger(ComplianceAuditService.name);

  constructor(
    @InjectRepository(ComplianceEvent)
    private readonly complianceEventRepository: Repository<ComplianceEvent>,
  ) {}

  async logComplianceEvent(eventData: {
    type: ComplianceType;
    eventType: ComplianceEventType;
    description: string;
    userId?: string;
    dataSubjectId?: string;
    legalBasis?: string;
    processingPurpose?: string;
    dataCategories?: string[];
    consentDetails?: Record<string, any>;
    requestType?: string;
    requestId?: string;
    responseDate?: Date;
    responseDeadline?: Date;
    responseMethod?: string;
    consentTimestamp?: Date;
    consentMethod?: string;
    metadata?: Record<string, any>;
  }): Promise<ComplianceEvent> {
    const event = this.complianceEventRepository.create({
      ...eventData,
      timestamp: new Date(),
    });

    const savedEvent = await this.complianceEventRepository.save(event);

    this.logger.log(`Compliance Event [${eventData.type}]: ${eventData.description}`);

    return savedEvent;
  }

  async getComplianceEvents(
    limit: number = 100,
    offset: number = 0,
    type?: ComplianceType,
    eventType?: ComplianceEventType,
    dataSubjectId?: string
  ): Promise<{ events: ComplianceEvent[]; total: number }> {
    const queryBuilder = this.complianceEventRepository.createQueryBuilder('event')
      .orderBy('event.timestamp', 'DESC')
      .limit(limit)
      .offset(offset);

    if (type) {
      queryBuilder.andWhere('event.type = :type', { type });
    }

    if (eventType) {
      queryBuilder.andWhere('event.eventType = :eventType', { eventType });
    }

    if (dataSubjectId) {
      queryBuilder.andWhere('event.dataSubjectId = :dataSubjectId', { dataSubjectId });
    }

    const [events, total] = await queryBuilder.getManyAndCount();

    return { events, total };
  }

  async getComplianceEventsByDataSubject(
    dataSubjectId: string,
    limit: number = 50
  ): Promise<ComplianceEvent[]> {
    return this.complianceEventRepository.find({
      where: { dataSubjectId },
      order: { timestamp: 'DESC' },
      take: limit,
    });
  }

  async getComplianceStatistics(): Promise<{
    totalEvents: number;
    eventsByType: Record<ComplianceType, number>;
    eventsByEventType: Record<ComplianceEventType, number>;
    recentEvents: number;
  }> {
    const totalEvents = await this.complianceEventRepository.count();

    // Get events by compliance type
    const typeResults = await this.complianceEventRepository
      .createQueryBuilder('event')
      .select('event.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('event.type')
      .getRawMany();

    const eventsByType = typeResults.reduce((acc, result) => {
      acc[result.type as ComplianceType] = parseInt(result.count);
      return acc;
    }, {} as Record<ComplianceType, number>);

    // Get events by event type
    const eventTypeResults = await this.complianceEventRepository
      .createQueryBuilder('event')
      .select('event.eventType', 'eventType')
      .addSelect('COUNT(*)', 'count')
      .groupBy('event.eventType')
      .getRawMany();

    const eventsByEventType = eventTypeResults.reduce((acc, result) => {
      acc[result.eventType as ComplianceEventType] = parseInt(result.count);
      return acc;
    }, {} as Record<ComplianceEventType, number>);

    // Get recent events (last 7 days)
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);

    const recentEvents = await this.complianceEventRepository.count({
      where: {
        timestamp: weekAgo,
      },
    });

    return {
      totalEvents,
      eventsByType,
      eventsByEventType,
      recentEvents,
    };
  }

  async logConsentGiven(
    dataSubjectId: string,
    purpose: string,
    method: string,
    legalBasis?: string,
    dataCategories?: string[]
  ): Promise<void> {
    await this.logComplianceEvent({
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.CONSENT_GIVEN,
      description: `Consent given for ${purpose}`,
      dataSubjectId,
      processingPurpose: purpose,
      consentMethod: method,
      legalBasis,
      dataCategories,
      consentTimestamp: new Date(),
    });
  }

  async logConsentWithdrawn(
    dataSubjectId: string,
    purpose: string,
    reason?: string
  ): Promise<void> {
    await this.logComplianceEvent({
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.CONSENT_WITHDRAWN,
      description: `Consent withdrawn for ${purpose}${reason ? `: ${reason}` : ''}`,
      dataSubjectId,
      processingPurpose: purpose,
      metadata: { reason },
    });
  }

  async logDataSubjectRequest(
    dataSubjectId: string,
    requestType: string,
    requestId: string,
    description: string,
    responseDeadline: Date
  ): Promise<void> {
    await this.logComplianceEvent({
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.DATA_SUBJECT_REQUEST,
      description,
      dataSubjectId,
      requestType,
      requestId,
      responseDeadline,
    });
  }

  async logDataAnonymization(
    description: string,
    dataCategories?: string[],
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logComplianceEvent({
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.DATA_ANONYMIZATION,
      description,
      dataCategories,
      metadata,
    });
  }

  async logDataAccess(
    userId: string,
    dataSubjectId: string,
    dataCategories: string[],
    purpose: string
  ): Promise<void> {
    await this.logComplianceEvent({
      type: ComplianceType.LGPD,
      eventType: ComplianceEventType.DATA_ACCESS,
      description: `Data access for ${purpose}`,
      userId,
      dataSubjectId,
      dataCategories,
      processingPurpose: purpose,
    });
  }

  async generateComplianceReport(
    startDate: Date,
    endDate: Date
  ): Promise<{
    period: { start: Date; end: Date };
    totalEvents: number;
    eventsByType: Record<ComplianceType, number>;
    eventsByEventType: Record<ComplianceEventType, number>;
    dataSubjectsAffected: number;
    complianceScore: number;
  }> {
    const events = await this.complianceEventRepository
      .createQueryBuilder('event')
      .where('event.timestamp >= :startDate', { startDate })
      .andWhere('event.timestamp <= :endDate', { endDate })
      .getMany();

    const eventsByType = events.reduce((acc, event) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {} as Record<ComplianceType, number>);

    const eventsByEventType = events.reduce((acc, event) => {
      acc[event.eventType] = (acc[event.eventType] || 0) + 1;
      return acc;
    }, {} as Record<ComplianceEventType, number>);

    const dataSubjectsAffected = new Set(
      events.filter(e => e.dataSubjectId).map(e => e.dataSubjectId)
    ).size;

    // Calculate compliance score based on events
    let complianceScore = 100;
    
    // Deduct points for negative events
    const negativeEvents = events.filter(e => 
      e.eventType === ComplianceEventType.CONSENT_WITHDRAWN ||
      e.eventType === ComplianceEventType.DATA_SUBJECT_REQUEST
    );
    
    complianceScore -= Math.min(negativeEvents.length * 2, 30);
    complianceScore = Math.max(0, complianceScore);

    return {
      period: { start: startDate, end: endDate },
      totalEvents: events.length,
      eventsByType,
      eventsByEventType,
      dataSubjectsAffected,
      complianceScore,
    };
  }
}
