'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { isValidCorporateEmail } from '@/lib/utils';
import { Mail, Shield } from 'lucide-react';
import toast from 'react-hot-toast';

export default function LoginPage() {
  const [step, setStep] = useState<'email' | 'otp'>('email');
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login, requestOtp } = useAuth();
  const router = useRouter();

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Por favor, insira seu e-mail');
      return;
    }

    if (!isValidCorporateEmail(email)) {
      toast.error('Por favor, use um e-mail corporativo válido');
      return;
    }

    setIsLoading(true);
    try {
      await requestOtp(email);
      setStep('otp');
    } catch (error) {
      // Error is handled in the context
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!code || code.length !== 6) {
      toast.error('Por favor, insira o código de 6 dígitos');
      return;
    }

    setIsLoading(true);
    try {
      await login(email, code);
      router.push('/dashboard');
    } catch (error) {
      // Error is handled in the context
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToEmail = () => {
    setStep('email');
    setCode('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header com ícone */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Shield className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Acesso ao Sistema</h1>
          <p className="text-gray-700 text-sm">
            Digite seu e-mail corporativo para receber o código de acesso
          </p>
        </div>

        {step === 'email' ? (
          /* Formulário de E-mail */
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <form onSubmit={handleEmailSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-800 mb-2">
                  E-mail Corporativo
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all placeholder:text-gray-600 text-gray-900"
                    required
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
                    Enviando...
                  </>
                ) : (
                  'Enviar Código OTP'
                )}
              </button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-red-600 text-sm font-medium">Sistema Retail Media</p>
              <p className="text-gray-700 text-sm mt-1">Gestão de Campanhas de Incentivo</p>
            </div>
          </div>
        ) : (
          /* Formulário de OTP */
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <form onSubmit={handleOtpSubmit} className="space-y-6">
              <div>
                <label htmlFor="code" className="block text-sm font-medium text-gray-800 mb-2">
                  Código de Verificação
                </label>
                <input
                  id="code"
                  type="text"
                  placeholder="000000"
                  value={code}
                  onChange={(e) => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all text-center text-lg tracking-widest"
                  maxLength={6}
                  required
                />
                <p className="text-xs text-gray-700 text-center mt-2">
                  Código enviado para {email}
                </p>
              </div>

              <div className="space-y-3">
                <button
                  type="submit"
                  disabled={isLoading || code.length !== 6}
                  className="w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2 inline-block" />
                      Verificando...
                    </>
                  ) : (
                    'Entrar'
                  )}
                </button>

                <button
                  type="button"
                  onClick={handleBackToEmail}
                  disabled={isLoading}
                  className="w-full bg-white border border-gray-300 text-gray-800 font-medium py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  Voltar
                </button>
              </div>

              <div className="text-center">
                <button
                  type="button"
                  className="text-sm text-blue-600 hover:underline"
                  onClick={() => handleEmailSubmit({ preventDefault: () => {} } as React.FormEvent)}
                  disabled={isLoading}
                >
                  Reenviar código
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-700">
          <p>
            Problemas para acessar?{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              Entre em contato
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
