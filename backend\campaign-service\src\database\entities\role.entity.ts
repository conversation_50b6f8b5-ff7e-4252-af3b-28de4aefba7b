import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { RolePermission } from './role-permission.entity';
import { UserRole } from './user-role.entity';

export enum RoleType {
  SYSTEM = 'system',
  CUSTOM = 'custom',
}

export enum RoleScope {
  GLOBAL = 'global',
  INDUSTRY = 'industry',
  CAMPAIGN = 'campaign',
}

@Entity('roles')
@Index(['name'])
@Index(['type'])
@Index(['scope'])
export class Role {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  name: string;

  @Column({ type: 'varchar', length: 255 })
  displayName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: RoleType, default: RoleType.CUSTOM })
  type: RoleType;

  @Column({ type: 'enum', enum: RoleScope, default: RoleScope.GLOBAL })
  scope: RoleScope;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isDefault: boolean;

  @Column({ type: 'int', default: 0 })
  priority: number; // Higher priority = more permissions

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => RolePermission, rolePermission => rolePermission.role)
  rolePermissions: RolePermission[];

  @OneToMany(() => UserRole, userRole => userRole.role)
  userRoles: UserRole[];

  // Helper methods
  isSystemRole(): boolean {
    return this.type === RoleType.SYSTEM;
  }

  canBeDeleted(): boolean {
    return !this.isSystemRole() && !this.isDefault;
  }

  canBeModified(): boolean {
    return !this.isSystemRole();
  }
}
