import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum RequestType {
  ACCESS = 'access', // Art. 9, II - acesso aos dados
  RECTIFICATION = 'rectification', // Art. 9, III - correção de dados
  DELETION = 'deletion', // Art. 9, VI - eliminação dos dados
  PORTABILITY = 'portability', // Art. 9, V - portabilidade dos dados
  ANONYMIZATION = 'anonymization', // Art. 9, VI - anonimização dos dados
  CONSENT_WITHDRAWAL = 'consent_withdrawal', // Art. 9, VIII - revogação do consentimento
  OBJECTION = 'objection', // Art. 9, VII - oposição ao tratamento
  INFORMATION = 'information', // Art. 9, I - confirmação da existência de tratamento
  RESTRICTION = 'restriction', // Limitação do tratamento
  COMPLAINT = 'complaint', // Reclamação
}

export enum RequestStatus {
  RECEIVED = 'received',
  UNDER_REVIEW = 'under_review',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  OVERDUE = 'overdue',
  ESCALATED = 'escalated',
}

export enum RequestPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('data_subject_requests')
@Index(['dataSubjectId', 'type'])
@Index(['status', 'createdAt'])
@Index(['type', 'status'])
@Index(['deadline'])
@Index(['assignedTo'])
export class DataSubjectRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  dataSubjectId: string; // User ID or email

  @Column({ type: 'enum', enum: RequestType })
  type: RequestType;

  @Column({ type: 'enum', enum: RequestStatus, default: RequestStatus.RECEIVED })
  status: RequestStatus;

  @Column({ type: 'enum', enum: RequestPriority, default: RequestPriority.MEDIUM })
  priority: RequestPriority;

  @Column({ type: 'text' })
  description: string; // Request details

  @Column({ type: 'varchar', length: 255, nullable: true })
  subject: string; // Request subject/title

  @Column({ type: 'json', nullable: true })
  requestedData: string[]; // Specific data requested

  @Column({ type: 'json', nullable: true })
  dataCategories: string[]; // Categories of data involved

  @Column({ type: 'varchar', length: 255, nullable: true })
  legalBasis: string; // Legal basis for the request

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  assignedTo: string; // Who is handling the request

  @Column({ type: 'timestamp' })
  @Index()
  deadline: Date; // Response deadline (15 days from receipt)

  @Column({ type: 'timestamp', nullable: true })
  respondedAt: Date; // When response was provided

  @Column({ type: 'varchar', length: 255, nullable: true })
  responseMethod: string; // How response was delivered

  @Column({ type: 'text', nullable: true })
  response: string; // Response content

  @Column({ type: 'json', nullable: true })
  responseData: Record<string, any>; // Structured response data

  @Column({ type: 'json', nullable: true })
  attachments: string[]; // File attachments

  @Column({ type: 'varchar', length: 255, nullable: true })
  verificationMethod: string; // How identity was verified

  @Column({ type: 'boolean', default: false })
  isVerified: boolean; // Whether identity is verified

  @Column({ type: 'varchar', length: 255, nullable: true })
  verifiedBy: string; // Who verified the identity

  @Column({ type: 'timestamp', nullable: true })
  verifiedAt: Date; // When identity was verified

  @Column({ type: 'varchar', length: 45, nullable: true })
  ipAddress: string; // IP address of request

  @Column({ type: 'varchar', length: 500, nullable: true })
  userAgent: string; // User agent of request

  @Column({ type: 'varchar', length: 255, nullable: true })
  source: string; // Source of request (web, email, phone)

  @Column({ type: 'text', nullable: true })
  escalationReason: string; // Reason for escalation

  @Column({ type: 'timestamp', nullable: true })
  escalatedAt: Date; // When request was escalated

  @Column({ type: 'varchar', length: 255, nullable: true })
  referenceNumber: string; // External reference number

  @Column({ type: 'text', nullable: true })
  internalNotes: string; // Internal processing notes

  @Column({ type: 'json', nullable: true })
  processingSteps: Array<{
    step: string;
    completedAt: Date;
    completedBy: string;
    notes?: string;
  }>; // Processing workflow

  @Column({ type: 'varchar', length: 255, nullable: true })
  rejectionReason: string; // Reason for rejection

  @Column({ type: 'text', nullable: true })
  rejectionDetails: string; // Detailed rejection explanation

  @Column({ type: 'boolean', default: false })
  requiresApproval: boolean; // Whether request needs approval

  @Column({ type: 'varchar', length: 255, nullable: true })
  approvedBy: string; // Who approved the request

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date; // When request was approved

  @Column({ type: 'json', nullable: true })
  impactAssessment: Record<string, any>; // Impact of fulfilling request

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  estimatedCost: number; // Cost to fulfill request

  @Column({ type: 'int', nullable: true })
  estimatedHours: number; // Hours to fulfill request

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  static createRequest(data: {
    dataSubjectId: string;
    type: RequestType;
    description: string;
    subject?: string;
    requestedData?: string[];
    dataCategories?: string[];
    legalBasis?: string;
    priority?: RequestPriority;
    ipAddress?: string;
    userAgent?: string;
    source?: string;
    referenceNumber?: string;
    verificationMethod?: string;
    metadata?: Record<string, any>;
  }): Partial<DataSubjectRequest> {
    const deadline = new Date();
    deadline.setDate(deadline.getDate() + 15); // 15 days as per LGPD

    return {
      dataSubjectId: data.dataSubjectId,
      type: data.type,
      status: RequestStatus.RECEIVED,
      priority: data.priority || RequestPriority.MEDIUM,
      description: data.description,
      subject: data.subject,
      requestedData: data.requestedData,
      dataCategories: data.dataCategories,
      legalBasis: data.legalBasis,
      deadline,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      source: data.source,
      referenceNumber: data.referenceNumber,
      verificationMethod: data.verificationMethod,
      isVerified: false,
      requiresApproval: data.type === RequestType.DELETION || data.type === RequestType.ANONYMIZATION,
      metadata: data.metadata,
    };
  }

  getTypeLabel(): string {
    const labels = {
      [RequestType.ACCESS]: 'Acesso aos Dados',
      [RequestType.RECTIFICATION]: 'Correção de Dados',
      [RequestType.DELETION]: 'Eliminação de Dados',
      [RequestType.PORTABILITY]: 'Portabilidade de Dados',
      [RequestType.ANONYMIZATION]: 'Anonimização de Dados',
      [RequestType.CONSENT_WITHDRAWAL]: 'Revogação do Consentimento',
      [RequestType.OBJECTION]: 'Oposição ao Tratamento',
      [RequestType.INFORMATION]: 'Informações sobre Tratamento',
      [RequestType.RESTRICTION]: 'Limitação do Tratamento',
      [RequestType.COMPLAINT]: 'Reclamação',
    };

    return labels[this.type] || this.type;
  }

  getStatusLabel(): string {
    const labels = {
      [RequestStatus.RECEIVED]: 'Recebido',
      [RequestStatus.UNDER_REVIEW]: 'Em Análise',
      [RequestStatus.IN_PROGRESS]: 'Em Andamento',
      [RequestStatus.COMPLETED]: 'Concluído',
      [RequestStatus.REJECTED]: 'Rejeitado',
      [RequestStatus.CANCELLED]: 'Cancelado',
      [RequestStatus.OVERDUE]: 'Em Atraso',
    };

    return labels[this.status] || this.status;
  }

  getPriorityLabel(): string {
    const labels = {
      [RequestPriority.LOW]: 'Baixa',
      [RequestPriority.MEDIUM]: 'Média',
      [RequestPriority.HIGH]: 'Alta',
      [RequestPriority.URGENT]: 'Urgente',
    };

    return labels[this.priority] || this.priority;
  }

  isOverdue(): boolean {
    return new Date() > this.deadline && !this.isCompleted();
  }

  isCompleted(): boolean {
    return this.status === RequestStatus.COMPLETED || this.status === RequestStatus.REJECTED;
  }

  getDaysUntilDeadline(): number {
    const now = new Date();
    const diffMs = this.deadline.getTime() - now.getTime();
    return Math.ceil(diffMs / (1000 * 60 * 60 * 24));
  }

  getDaysOverdue(): number {
    if (!this.isOverdue()) return 0;
    
    const now = new Date();
    const diffMs = now.getTime() - this.deadline.getTime();
    return Math.ceil(diffMs / (1000 * 60 * 60 * 24));
  }

  getProcessingTime(): number | null {
    if (!this.respondedAt) return null;
    
    return this.respondedAt.getTime() - this.createdAt.getTime();
  }

  getFormattedProcessingTime(): string | null {
    const processingTime = this.getProcessingTime();
    if (!processingTime) return null;
    
    const days = Math.floor(processingTime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((processingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) {
      return `${days} dia${days > 1 ? 's' : ''} e ${hours} hora${hours > 1 ? 's' : ''}`;
    } else {
      return `${hours} hora${hours > 1 ? 's' : ''}`;
    }
  }

  addProcessingStep(step: string, completedBy: string, notes?: string): void {
    if (!this.processingSteps) {
      this.processingSteps = [];
    }
    
    this.processingSteps.push({
      step,
      completedAt: new Date(),
      completedBy,
      notes,
    });
  }

  assign(assignedTo: string): void {
    this.assignedTo = assignedTo;
    this.status = RequestStatus.UNDER_REVIEW;
    this.addProcessingStep('Solicitação atribuída', assignedTo);
  }

  verify(verifiedBy: string, method: string): void {
    this.isVerified = true;
    this.verifiedBy = verifiedBy;
    this.verifiedAt = new Date();
    this.verificationMethod = method;
    this.addProcessingStep('Identidade verificada', verifiedBy, `Método: ${method}`);
  }

  approve(approvedBy: string): void {
    if (!this.requiresApproval) {
      throw new Error('This request does not require approval');
    }
    
    this.approvedBy = approvedBy;
    this.approvedAt = new Date();
    this.status = RequestStatus.IN_PROGRESS;
    this.addProcessingStep('Solicitação aprovada', approvedBy);
  }

  complete(response: string, responseMethod: string, responseData?: Record<string, any>): void {
    this.status = RequestStatus.COMPLETED;
    this.response = response;
    this.responseMethod = responseMethod;
    this.responseData = responseData;
    this.respondedAt = new Date();
    this.addProcessingStep('Solicitação concluída', this.assignedTo || 'system', 'Resposta enviada');
  }

  reject(reason: string, details?: string, rejectedBy?: string): void {
    this.status = RequestStatus.REJECTED;
    this.rejectionReason = reason;
    this.rejectionDetails = details;
    this.respondedAt = new Date();
    this.addProcessingStep('Solicitação rejeitada', rejectedBy || this.assignedTo || 'system', reason);
  }

  cancel(cancelledBy?: string, reason?: string): void {
    this.status = RequestStatus.CANCELLED;
    this.addProcessingStep('Solicitação cancelada', cancelledBy || 'system', reason);
  }

  requiresUrgentAttention(): boolean {
    return (
      this.priority === RequestPriority.URGENT ||
      this.isOverdue() ||
      this.getDaysUntilDeadline() <= 2
    );
  }

  escalate(reason: string, escalatedBy?: string): void {
    this.status = RequestStatus.ESCALATED;
    this.escalationReason = reason;
    this.escalatedAt = new Date();
    this.addProcessingStep('Solicitação escalada', escalatedBy || this.assignedTo || 'system', reason);
  }

  canBeProcessed(): boolean {
    return (
      this.isVerified &&
      (this.status === RequestStatus.UNDER_REVIEW || this.status === RequestStatus.IN_PROGRESS) &&
      (!this.requiresApproval || this.approvedAt !== null)
    );
  }
}
