import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

export enum AuditAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  READ = 'read',
  LOGIN = 'login',
  LOGOUT = 'logout',
  EXPORT = 'export',
  IMPORT = 'import',
  APPROVE = 'approve',
  REJECT = 'reject',
  ACTIVATE = 'activate',
  DEACTIVATE = 'deactivate',
  CONFIGURE = 'configure',
  EXECUTE = 'execute',
  CANCEL = 'cancel',
  SUSPEND = 'suspend',
  RESUME = 'resume',
}

export enum AuditLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

export enum AuditCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  SYSTEM_CONFIGURATION = 'system_configuration',
  CAMPAIGN_MANAGEMENT = 'campaign_management',
  USER_MANAGEMENT = 'user_management',
  SECURITY = 'security',
  COMPLIANCE = 'compliance',
  PERFORMANCE = 'performance',
  ERROR = 'error',
  BUSINESS_LOGIC = 'business_logic',
}

@Entity('audit_logs')
@Index(['userId', 'timestamp'])
@Index(['action', 'timestamp'])
@Index(['category', 'timestamp'])
@Index(['level', 'timestamp'])
@Index(['entityType', 'entityId'])
@Index(['timestamp'])
@Index(['ipAddress'])
@Index(['sessionId'])
export class AuditLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  userId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  sessionId: string;

  @Column({ type: 'enum', enum: AuditAction })
  action: AuditAction;

  @Column({ type: 'enum', enum: AuditLevel, default: AuditLevel.INFO })
  level: AuditLevel;

  @Column({ type: 'enum', enum: AuditCategory })
  category: AuditCategory;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  entityType: string; // e.g., 'Campaign', 'User', 'Product'

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  entityId: string;

  @Column({ type: 'varchar', length: 500 })
  description: string;

  @Column({ type: 'json', nullable: true })
  details: Record<string, any>; // Additional structured data

  @Column({ type: 'json', nullable: true })
  oldValues: Record<string, any>; // Previous values for updates

  @Column({ type: 'json', nullable: true })
  newValues: Record<string, any>; // New values for updates

  @Column({ type: 'varchar', length: 45, nullable: true })
  @Index()
  ipAddress: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  userAgent: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  source: string; // Source system or module

  @Column({ type: 'varchar', length: 255, nullable: true })
  correlationId: string; // For tracing related events

  @Column({ type: 'int', nullable: true })
  duration: number; // Operation duration in milliseconds

  @Column({ type: 'boolean', default: false })
  @Index()
  isSuccessful: boolean;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  errorCode: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // Additional metadata

  @CreateDateColumn()
  @Index()
  timestamp: Date;

  // Helper methods
  static createAuditLog(data: {
    userId?: string;
    sessionId?: string;
    action: AuditAction;
    level?: AuditLevel;
    category: AuditCategory;
    entityType?: string;
    entityId?: string;
    description: string;
    details?: Record<string, any>;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
    source?: string;
    correlationId?: string;
    duration?: number;
    isSuccessful?: boolean;
    errorMessage?: string;
    errorCode?: string;
    metadata?: Record<string, any>;
  }): Partial<AuditLog> {
    return {
      userId: data.userId,
      sessionId: data.sessionId,
      action: data.action,
      level: data.level || AuditLevel.INFO,
      category: data.category,
      entityType: data.entityType,
      entityId: data.entityId,
      description: data.description,
      details: data.details,
      oldValues: data.oldValues,
      newValues: data.newValues,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      source: data.source,
      correlationId: data.correlationId,
      duration: data.duration,
      isSuccessful: data.isSuccessful !== false, // Default to true
      errorMessage: data.errorMessage,
      errorCode: data.errorCode,
      metadata: data.metadata,
    };
  }

  getActionLabel(): string {
    const labels = {
      [AuditAction.CREATE]: 'Criar',
      [AuditAction.UPDATE]: 'Atualizar',
      [AuditAction.DELETE]: 'Excluir',
      [AuditAction.READ]: 'Visualizar',
      [AuditAction.LOGIN]: 'Login',
      [AuditAction.LOGOUT]: 'Logout',
      [AuditAction.EXPORT]: 'Exportar',
      [AuditAction.IMPORT]: 'Importar',
      [AuditAction.APPROVE]: 'Aprovar',
      [AuditAction.REJECT]: 'Rejeitar',
      [AuditAction.ACTIVATE]: 'Ativar',
      [AuditAction.DEACTIVATE]: 'Desativar',
      [AuditAction.CONFIGURE]: 'Configurar',
      [AuditAction.EXECUTE]: 'Executar',
      [AuditAction.CANCEL]: 'Cancelar',
      [AuditAction.SUSPEND]: 'Suspender',
      [AuditAction.RESUME]: 'Retomar',
    };

    return labels[this.action] || this.action;
  }

  getLevelLabel(): string {
    const labels = {
      [AuditLevel.INFO]: 'Informação',
      [AuditLevel.WARNING]: 'Aviso',
      [AuditLevel.ERROR]: 'Erro',
      [AuditLevel.CRITICAL]: 'Crítico',
    };

    return labels[this.level] || this.level;
  }

  getCategoryLabel(): string {
    const labels = {
      [AuditCategory.AUTHENTICATION]: 'Autenticação',
      [AuditCategory.AUTHORIZATION]: 'Autorização',
      [AuditCategory.DATA_ACCESS]: 'Acesso a Dados',
      [AuditCategory.DATA_MODIFICATION]: 'Modificação de Dados',
      [AuditCategory.SYSTEM_CONFIGURATION]: 'Configuração do Sistema',
      [AuditCategory.CAMPAIGN_MANAGEMENT]: 'Gestão de Campanhas',
      [AuditCategory.USER_MANAGEMENT]: 'Gestão de Usuários',
      [AuditCategory.SECURITY]: 'Segurança',
      [AuditCategory.COMPLIANCE]: 'Conformidade',
      [AuditCategory.PERFORMANCE]: 'Performance',
      [AuditCategory.ERROR]: 'Erro',
      [AuditCategory.BUSINESS_LOGIC]: 'Lógica de Negócio',
    };

    return labels[this.category] || this.category;
  }

  getFormattedDuration(): string | null {
    if (!this.duration) return null;

    if (this.duration < 1000) {
      return `${this.duration}ms`;
    } else if (this.duration < 60000) {
      return `${(this.duration / 1000).toFixed(2)}s`;
    } else {
      return `${(this.duration / 60000).toFixed(2)}min`;
    }
  }

  isHighRisk(): boolean {
    const highRiskActions = [
      AuditAction.DELETE,
      AuditAction.EXPORT,
      AuditAction.CONFIGURE,
    ];

    const highRiskCategories = [
      AuditCategory.SECURITY,
      AuditCategory.SYSTEM_CONFIGURATION,
      AuditCategory.USER_MANAGEMENT,
    ];

    return (
      this.level === AuditLevel.CRITICAL ||
      this.level === AuditLevel.ERROR ||
      highRiskActions.includes(this.action) ||
      highRiskCategories.includes(this.category) ||
      !this.isSuccessful
    );
  }

  getChangeSummary(): string | null {
    if (!this.oldValues || !this.newValues) return null;

    const changes: string[] = [];
    const allKeys = new Set([
      ...Object.keys(this.oldValues),
      ...Object.keys(this.newValues),
    ]);

    for (const key of allKeys) {
      const oldValue = this.oldValues[key];
      const newValue = this.newValues[key];

      if (oldValue !== newValue) {
        if (oldValue === undefined) {
          changes.push(`${key}: adicionado (${newValue})`);
        } else if (newValue === undefined) {
          changes.push(`${key}: removido (${oldValue})`);
        } else {
          changes.push(`${key}: ${oldValue} → ${newValue}`);
        }
      }
    }

    return changes.length > 0 ? changes.join(', ') : null;
  }
}
