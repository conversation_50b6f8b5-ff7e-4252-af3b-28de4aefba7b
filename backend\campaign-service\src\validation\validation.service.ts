import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IndustryRule, RuleType, RuleStatus } from '../database/entities/industry-rules.entity';

export interface ValidationContext {
  industryId: string;
  campaignId?: string;
  productIds?: string[];
  incentivePercentage?: number;
  incentiveValue?: number;
  productCount?: number;
  validityDays?: number;
  cycleId?: string;
}

export interface ValidationError {
  code: string;
  message: string;
  field: string;
  value: any;
  ruleId: string;
  severity: 'error' | 'warning';
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  performance: {
    executionTime: number;
    rulesEvaluated: number;
    cacheHits: number;
  };
}

@Injectable()
export class ValidationService {
  private readonly logger = new Logger(ValidationService.name);
  private rulesCache = new Map<string, IndustryRule[]>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(
    @InjectRepository(IndustryRule)
    private readonly industryRuleRepository: Repository<IndustryRule>,
  ) {}

  async validateCampaign(context: ValidationContext): Promise<ValidationResult> {
    const startTime = Date.now();
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    let cacheHits = 0;

    try {
      // Get rules for industry (with caching)
      const rules = await this.getRulesForIndustry(context.industryId);
      if (this.rulesCache.has(context.industryId)) {
        cacheHits++;
      }

      // Validate each rule
      for (const rule of rules) {
        if (!rule.enabled || rule.status !== RuleStatus.ACTIVE) {
          continue;
        }

        const ruleErrors = await this.validateRule(rule, context);
        errors.push(...ruleErrors.filter(e => e.severity === 'error'));
        warnings.push(...ruleErrors.filter(e => e.severity === 'warning'));
      }

      const executionTime = Date.now() - startTime;

      // Log performance if slow
      if (executionTime > 100) {
        this.logger.warn(`Validation took ${executionTime}ms for industry ${context.industryId}`);
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        performance: {
          executionTime,
          rulesEvaluated: rules.length,
          cacheHits,
        },
      };
    } catch (error) {
      this.logger.error(`Validation error for industry ${context.industryId}:`, error);
      
      return {
        isValid: false,
        errors: [{
          code: 'VALIDATION_ERROR',
          message: 'Erro interno na validação',
          field: 'system',
          value: null,
          ruleId: 'system',
          severity: 'error',
        }],
        warnings: [],
        performance: {
          executionTime: Date.now() - startTime,
          rulesEvaluated: 0,
          cacheHits,
        },
      };
    }
  }

  async validateRule(rule: IndustryRule, context: ValidationContext): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];

    switch (rule.ruleType) {
      case RuleType.MINIMUM_PERCENTAGE:
        if (context.incentivePercentage !== undefined) {
          if (context.incentivePercentage < rule.minPercentage) {
            errors.push({
              code: 'MIN_PERCENTAGE_VIOLATION',
              message: `Incentivo abaixo do mínimo de ${rule.minPercentage}%`,
              field: 'incentivePercentage',
              value: context.incentivePercentage,
              ruleId: rule.id,
              severity: 'error',
            });
          }
        }
        break;

      case RuleType.MAXIMUM_PERCENTAGE:
        if (context.incentivePercentage !== undefined) {
          if (context.incentivePercentage > rule.maxPercentage) {
            errors.push({
              code: 'MAX_PERCENTAGE_VIOLATION',
              message: `Incentivo acima do máximo de ${rule.maxPercentage}%`,
              field: 'incentivePercentage',
              value: context.incentivePercentage,
              ruleId: rule.id,
              severity: 'error',
            });
          }
        }
        break;

      case RuleType.MINIMUM_VALUE:
        if (context.incentiveValue !== undefined) {
          if (context.incentiveValue < rule.minValue) {
            errors.push({
              code: 'MIN_VALUE_VIOLATION',
              message: `Incentivo abaixo do mínimo de R$ ${rule.minValue.toFixed(2)}`,
              field: 'incentiveValue',
              value: context.incentiveValue,
              ruleId: rule.id,
              severity: 'error',
            });
          }
        }
        break;

      case RuleType.MAXIMUM_VALUE:
        if (context.incentiveValue !== undefined) {
          if (context.incentiveValue > rule.maxValue) {
            errors.push({
              code: 'MAX_VALUE_VIOLATION',
              message: `Incentivo acima do máximo de R$ ${rule.maxValue.toFixed(2)}`,
              field: 'incentiveValue',
              value: context.incentiveValue,
              ruleId: rule.id,
              severity: 'error',
            });
          }
        }
        break;

      case RuleType.PRODUCT_LIMIT_PER_CYCLE:
        if (context.productCount !== undefined) {
          if (context.productCount > rule.integerValue) {
            errors.push({
              code: 'PRODUCT_LIMIT_VIOLATION',
              message: `Limite de ${rule.integerValue} produtos por ciclo excedido`,
              field: 'productCount',
              value: context.productCount,
              ruleId: rule.id,
              severity: 'error',
            });
          }
          // Warning when approaching limit (80%)
          else if (context.productCount > rule.integerValue * 0.8) {
            errors.push({
              code: 'PRODUCT_LIMIT_WARNING',
              message: `Aproximando do limite de ${rule.integerValue} produtos por ciclo`,
              field: 'productCount',
              value: context.productCount,
              ruleId: rule.id,
              severity: 'warning',
            });
          }
        }
        break;

      case RuleType.INCENTIVE_VALIDITY_DAYS:
        if (context.validityDays !== undefined) {
          if (context.validityDays > rule.integerValue) {
            errors.push({
              code: 'VALIDITY_VIOLATION',
              message: `Validade deve ser entre 1 e ${rule.integerValue} dias`,
              field: 'validityDays',
              value: context.validityDays,
              ruleId: rule.id,
              severity: 'error',
            });
          }
          // Warning for very short validity (less than 7 days)
          else if (context.validityDays < 7) {
            errors.push({
              code: 'SHORT_VALIDITY_WARNING',
              message: 'Validade muito curta pode afetar a performance da campanha',
              field: 'validityDays',
              value: context.validityDays,
              ruleId: rule.id,
              severity: 'warning',
            });
          }
        }
        break;
    }

    return errors;
  }

  async getRulesForIndustry(industryId: string): Promise<IndustryRule[]> {
    // Check cache first
    const cacheKey = `rules_${industryId}`;
    const now = Date.now();
    
    if (this.rulesCache.has(cacheKey) && this.cacheExpiry.get(cacheKey) > now) {
      return this.rulesCache.get(cacheKey);
    }

    // Fetch from database
    const rules = await this.industryRuleRepository.find({
      where: { industryId },
      order: { ruleType: 'ASC' },
    });

    // Update cache
    this.rulesCache.set(cacheKey, rules);
    this.cacheExpiry.set(cacheKey, now + this.CACHE_TTL);

    return rules;
  }

  async validateInRealTime(context: ValidationContext): Promise<ValidationResult> {
    // Optimized validation for real-time use
    // Only validate essential rules for immediate feedback
    const startTime = Date.now();
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    try {
      const rules = await this.getRulesForIndustry(context.industryId);
      
      // Only validate percentage and value rules for real-time feedback
      const realTimeRules = rules.filter(rule => 
        rule.enabled && 
        rule.status === RuleStatus.ACTIVE &&
        [RuleType.MINIMUM_PERCENTAGE, RuleType.MAXIMUM_PERCENTAGE, 
         RuleType.MINIMUM_VALUE, RuleType.MAXIMUM_VALUE].includes(rule.ruleType)
      );

      for (const rule of realTimeRules) {
        const ruleErrors = await this.validateRule(rule, context);
        errors.push(...ruleErrors.filter(e => e.severity === 'error'));
        warnings.push(...ruleErrors.filter(e => e.severity === 'warning'));
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        performance: {
          executionTime: Date.now() - startTime,
          rulesEvaluated: realTimeRules.length,
          cacheHits: this.rulesCache.has(`rules_${context.industryId}`) ? 1 : 0,
        },
      };
    } catch (error) {
      this.logger.error(`Real-time validation error:`, error);
      return {
        isValid: false,
        errors: [{
          code: 'VALIDATION_ERROR',
          message: 'Erro na validação em tempo real',
          field: 'system',
          value: null,
          ruleId: 'system',
          severity: 'error',
        }],
        warnings: [],
        performance: {
          executionTime: Date.now() - startTime,
          rulesEvaluated: 0,
          cacheHits: 0,
        },
      };
    }
  }

  clearCache(industryId?: string): void {
    if (industryId) {
      const cacheKey = `rules_${industryId}`;
      this.rulesCache.delete(cacheKey);
      this.cacheExpiry.delete(cacheKey);
    } else {
      this.rulesCache.clear();
      this.cacheExpiry.clear();
    }
  }

  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.rulesCache.size,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
    };
  }
}
