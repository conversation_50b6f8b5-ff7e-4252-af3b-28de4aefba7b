import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

describe('Authentication Integration (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.setGlobalPrefix('api/v1');
    app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Protected Endpoints - Without Authentication', () => {
    it('GET /api/v1/campaigns - should return 401 without token', () => {
      return request(app.getHttpServer())
        .get('/api/v1/campaigns')
        .expect(401);
    });

    it('POST /api/v1/campaigns - should return 401 without token', () => {
      return request(app.getHttpServer())
        .post('/api/v1/campaigns')
        .send({
          name: 'Test Campaign',
          description: 'Test',
          industryId: '123e4567-e89b-12d3-a456-426614174000',
          incentiveType: 'percentage',
          incentivePercentage: 15,
        })
        .expect(401);
    });

    it('POST /api/v1/reports/campaign-performance - should return 401 without token', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/campaign-performance')
        .send({
          dateRange: {
            startDate: '2025-01-01',
            endDate: '2025-12-31',
          },
        })
        .expect(401);
    });

    it('GET /api/v1/reports/dashboard - should return 401 without token', () => {
      return request(app.getHttpServer())
        .get('/api/v1/reports/dashboard')
        .expect(401);
    });
  });

  describe('Protected Endpoints - With Invalid Token', () => {
    it('GET /api/v1/campaigns - should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .get('/api/v1/campaigns')
        .set('Authorization', 'Bearer invalid-token-here')
        .expect(401);
    });

    it('POST /api/v1/reports/campaign-performance - should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .post('/api/v1/reports/campaign-performance')
        .set('Authorization', 'Bearer invalid-token-here')
        .send({
          dateRange: {
            startDate: '2025-01-01',
            endDate: '2025-12-31',
          },
        })
        .expect(401);
    });
  });

  describe('Public Endpoints - Should work without authentication', () => {
    it('GET /api/v1/test/simple - should return 200 without token', () => {
      return request(app.getHttpServer())
        .get('/api/v1/test/simple')
        .expect(200);
    });
  });

  describe('CORS Headers', () => {
    it('OPTIONS /api/v1/campaigns - should handle preflight request', () => {
      return request(app.getHttpServer())
        .options('/api/v1/campaigns')
        .expect((res) => {
          // Note: CORS is handled by Nginx in production
          // In tests, we just verify the endpoint exists
          expect([200, 204, 404]).toContain(res.status);
        });
    });
  });
});

