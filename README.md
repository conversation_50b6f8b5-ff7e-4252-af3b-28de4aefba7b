# 🚀 Retail Media System

> **Plataforma completa para gestão de campanhas de incentivos no varejo**

[![CI/CD Pipeline](https://github.com/retail-media/system/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/retail-media/system/actions)
[![Coverage](https://codecov.io/gh/retail-media/system/branch/main/graph/badge.svg)](https://codecov.io/gh/retail-media/system)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)

## 📋 Índice

- [Visão <PERSON>](#-visão-geral)
- [Funcionalidades](#-funcionalidades)
- [Stack Tecnológica](#-stack-tecnológica)
- [Instalação Rápida](#-instalação-rápida)
- [Documentação](#-documentação)
- [Contribuição](#-contribuição)
- [Licença](#-licença)

## 🎯 Visão Geral

O **Retail Media System** é uma solução enterprise para gestão de campanhas de incentivos no varejo, desenvolvida com arquitetura de microservices moderna e seguindo as melhores práticas de desenvolvimento.

### 🏗️ Arquitetura

```mermaid
graph TB
    subgraph "Frontend"
        FE[React + Next.js]
    end
    
    subgraph "API Gateway"
        NG[Nginx]
    end
    
    subgraph "Microservices"
        AUTH[Auth Service]
        CAMP[Campaign Service]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        RD[(Redis)]
        RMQ[RabbitMQ]
    end
    
    FE --> NG
    NG --> AUTH
    NG --> CAMP
    AUTH --> PG
    CAMP --> PG
    AUTH --> RD
    CAMP --> RD
    CAMP --> RMQ
```

## ✨ Funcionalidades

### 🔐 Autenticação Avançada
- **OTP por e-mail** com códigos de 6 dígitos
- **2FA com Google Authenticator** para segurança extra
- **JWT + Refresh Tokens** para sessões seguras
- **Rate limiting** para prevenção de ataques

### 📊 Gestão de Campanhas
- **Criação e edição** de campanhas de incentivos
- **Tipos de incentivo**: Percentual ou valor fixo
- **Agendamento automático** de ativação/encerramento
- **Transições de estado** com auditoria completa

### 🎯 Analytics e IA
- **Métricas em tempo real** de performance
- **Estimativas inteligentes** com OpenAI
- **Dashboards interativos** com visualizações
- **Relatórios customizáveis** por período

### 👥 Controle de Acesso
- **RBAC** (Admin/Editor/Reader)
- **Gestão de usuários** com convites por e-mail
- **Auditoria completa** de todas as ações
- **Filtros por indústria** para segmentação

### 🛡️ Segurança Enterprise
- **Headers de segurança** configurados
- **Validação rigorosa** de entrada
- **Logs estruturados** para auditoria
- **Compliance LGPD** com anonimização

## 💻 Stack Tecnológica

### Frontend
- **React 18** + **Next.js 14** + **TypeScript**
- **Tailwind CSS** + **shadcn/ui** para design system
- **React Query** para gerenciamento de estado
- **React Hook Form** + **Zod** para formulários

### Backend
- **Node.js 18** + **NestJS** + **TypeScript**
- **TypeORM** + **PostgreSQL** para persistência
- **Redis** para cache e sessões
- **RabbitMQ** para filas de mensagens

### DevOps
- **Docker** + **Kubernetes** para containerização
- **GitHub Actions** para CI/CD
- **Prometheus** + **Grafana** para monitoramento
- **Nginx** como API Gateway

### Testes
- **Jest** + **Supertest** para backend
- **Testing Library** para frontend
- **Cobertura 70%+** em todos os módulos

## 🚀 Instalação Rápida

### Pré-requisitos
- Node.js 18+
- Docker & Docker Compose
- Git

### Opção 1: Docker (Recomendado)

```bash
# 1. Clone o repositório
git clone https://github.com/retail-media/system.git
cd retail-media-system

# 2. Configure variáveis de ambiente
cp .env.example .env
# Edite o arquivo .env com suas configurações

# 3. Inicie todo o sistema
docker-compose up -d

# 4. Acesse a aplicação
open http://localhost:3000
```

### Opção 2: Desenvolvimento Local

```bash
# 1. Clone o repositório
git clone https://github.com/retail-media/system.git
cd retail-media-system

# 2. Inicie infraestrutura
docker-compose up -d postgres-auth postgres-campaign redis rabbitmq

# 3. Backend - Auth Service
cd backend/auth-service
npm install
npm run start:dev

# 4. Backend - Campaign Service
cd ../campaign-service
npm install
npm run start:dev

# 5. Frontend
cd ../../frontend-nextjs
npm install
npm run dev
```

### Verificação da Instalação

```bash
# Verificar se todos os serviços estão rodando
docker-compose ps

# Verificar logs
docker-compose logs -f

# Testar APIs via API Gateway
curl http://localhost:8080/api/v1/auth/health  # Auth Service
curl http://localhost:8080/api/v1/campaigns/health  # Campaign Service

# Acessar frontend
open http://localhost:3000
```

## 📚 Documentação

### 📖 Documentação Técnica
- [**Documentação Técnica Completa**](TECHNICAL_DOCUMENTATION.md) - Guia detalhado de arquitetura, APIs e deploy
- [**API Reference**](docs/API.md) - Documentação completa das APIs
- [**Database Schema**](docs/DATABASE.md) - Estrutura e relacionamentos do banco

### 🎯 Guias de Uso
- [**Guia de Instalação**](docs/INSTALLATION.md) - Setup detalhado para desenvolvimento
- [**Guia de Deploy**](docs/DEPLOYMENT.md) - Deploy em produção com Kubernetes
- [**Guia de Testes**](docs/TESTING.md) - Como executar e criar testes

### 🔧 Desenvolvimento
- [**Guia de Contribuição**](CONTRIBUTING.md) - Como contribuir para o projeto
- [**Padrões de Código**](docs/CODE_STANDARDS.md) - Convenções e boas práticas
- [**Troubleshooting**](docs/TROUBLESHOOTING.md) - Solução de problemas comuns

## 📊 Métricas e Performance

### 🚀 Performance
- **Response Time P95**: < 300ms
- **Availability**: 99.9%
- **Throughput**: 1000+ req/s
- **Error Rate**: < 0.1%

### 📈 Cobertura de Testes
- **Backend**: 85%+ cobertura
- **Frontend**: 80%+ cobertura
- **E2E**: Fluxos críticos cobertos
- **Security**: Scans automatizados

### 🔒 Segurança
- **OWASP Top 10**: Protegido
- **Rate Limiting**: Configurado
- **Headers de Segurança**: Implementados
- **Auditoria**: 100% das ações

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor, leia nosso [Guia de Contribuição](CONTRIBUTING.md) para detalhes sobre nosso código de conduta e processo de submissão de pull requests.

### 🛠️ Desenvolvimento

```bash
# 1. Fork o projeto
# 2. Crie uma branch para sua feature
git checkout -b feature/amazing-feature

# 3. Commit suas mudanças
git commit -m 'feat: add amazing feature'

# 4. Push para a branch
git push origin feature/amazing-feature

# 5. Abra um Pull Request
```

### 📋 Roadmap

- [ ] **Q1 2024**: Integração com ERPs externos
- [ ] **Q2 2024**: Mobile app (React Native)
- [ ] **Q3 2024**: Machine Learning para otimização
- [ ] **Q4 2024**: Multi-tenancy e white-label

## 👥 Equipe

### 🏆 Core Team
- **Tech Lead**: [@tech-lead](https://github.com/tech-lead)
- **Backend Lead**: [@backend-lead](https://github.com/backend-lead)
- **Frontend Lead**: [@frontend-lead](https://github.com/frontend-lead)
- **DevOps Lead**: [@devops-lead](https://github.com/devops-lead)

### 🙏 Contribuidores

Agradecemos a todos os [contribuidores](https://github.com/retail-media/system/contributors) que ajudaram a tornar este projeto possível!

## 📄 Licença

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🔗 Links Úteis

- [**Website**](https://retail-media.example.com) - Site oficial do projeto
- [**Demo**](https://demo.retail-media.example.com) - Demonstração online
- [**Status Page**](https://status.retail-media.example.com) - Status dos serviços
- [**Monitoring**](https://monitoring.retail-media.example.com) - Dashboards de monitoramento

## 📞 Suporte

- 📧 **Email**: <EMAIL>
- 💬 **Discord**: [Servidor da Comunidade](https://discord.gg/retail-media)
- 📖 **Docs**: [Documentação Oficial](https://docs.retail-media.example.com)
- 🐛 **Issues**: [GitHub Issues](https://github.com/retail-media/system/issues)

---

<div align="center">

**Desenvolvido com ❤️ pela equipe Retail Media**

[![GitHub stars](https://img.shields.io/github/stars/retail-media/system.svg?style=social&label=Star)](https://github.com/retail-media/system)
[![GitHub forks](https://img.shields.io/github/forks/retail-media/system.svg?style=social&label=Fork)](https://github.com/retail-media/system/fork)
[![GitHub watchers](https://img.shields.io/github/watchers/retail-media/system.svg?style=social&label=Watch)](https://github.com/retail-media/system)

</div>
