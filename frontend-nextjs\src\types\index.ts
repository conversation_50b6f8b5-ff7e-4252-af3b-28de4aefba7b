// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  status: UserStatus;
  industryId?: string;
  emailVerified: boolean;
  emailVerifiedAt?: Date;
  lastLoginAt?: Date;
  lastLoginIp?: string;
  twoFactorEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  EDITOR = 'editor',
  READER = 'reader',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
}

// Campaign Types
export interface Campaign {
  id: string;
  name: string;
  description: string;
  status: CampaignStatus;
  startDate: Date;
  endDate: Date;
  budget: number;
  spent: number;
  impressions: number;
  clicks: number;
  conversions: number;
  ctr: number;
  cpc: number;
  cpm: number;
  roas: number;
  createdBy: string;
  industryId: string;
  targetAudience: TargetAudience;
  products: Product[];
  incentives: Incentive[];
  createdAt: Date;
  updatedAt: Date;
}

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ENDED = 'ended',
  REACTIVATED = 'reactivated',
}

export interface TargetAudience {
  ageRange: [number, number];
  gender: 'all' | 'male' | 'female';
  location: string[];
  interests: string[];
  behaviors: string[];
  estimatedReach: number;
}

export interface Product {
  id: string;
  name: string;
  sku: string;
  category: string;
  price: number;
  imageUrl?: string;
  description?: string;
  brand: string;
  inStock: boolean;
}

export interface Incentive {
  id: string;
  type: IncentiveType;
  value: number;
  description: string;
  minPurchaseAmount?: number;
  maxUsagePerUser?: number;
  validFrom: Date;
  validTo: Date;
  isActive: boolean;
}

export enum IncentiveType {
  PERCENTAGE = 'percentage',
  FIXED = 'fixed',
  CASHBACK = 'cashback',
  FREE_SHIPPING = 'free_shipping',
}

// Analytics Types
export interface DashboardStats {
  totalCampaigns: number;
  activeCampaigns: number;
  totalSpent: number;
  totalRevenue: number;
  totalImpressions: number;
  totalClicks: number;
  totalConversions: number;
  averageCTR: number;
  averageCPC: number;
  averageROAS: number;
  recentCampaigns: Campaign[];
  topPerformingCampaigns: Campaign[];
}

export interface CampaignAnalytics {
  campaignId: string;
  impressions: number;
  clicks: number;
  conversions: number;
  spent: number;
  revenue: number;
  ctr: number;
  cpc: number;
  cpm: number;
  roas: number;
  conversionRate: number;
  dailyStats: DailyStats[];
  audienceBreakdown: AudienceBreakdown;
  topProducts: ProductPerformance[];
}

export interface DailyStats {
  date: string;
  impressions: number;
  clicks: number;
  conversions: number;
  spent: number;
  revenue: number;
}

export interface AudienceBreakdown {
  ageGroups: { range: string; percentage: number }[];
  genders: { gender: string; percentage: number }[];
  locations: { location: string; percentage: number }[];
  devices: { device: string; percentage: number }[];
}

export interface ProductPerformance {
  productId: string;
  productName: string;
  impressions: number;
  clicks: number;
  conversions: number;
  revenue: number;
  ctr: number;
  conversionRate: number;
}

// Attribution Types
export interface AttributionModel {
  id: string;
  name: string;
  type: AttributionType;
  description: string;
  configuration: AttributionConfiguration;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum AttributionType {
  FIRST_CLICK = 'first_click',
  LAST_CLICK = 'last_click',
  LINEAR = 'linear',
  TIME_DECAY = 'time_decay',
  POSITION_BASED = 'position_based',
  DATA_DRIVEN = 'data_driven',
}

export interface AttributionConfiguration {
  lookbackWindow: number; // days
  includeViewThroughs: boolean;
  customWeights?: { [key: string]: number };
}

// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
  timestamp?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
  path: string;
}

// Auth Types
export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginCredentials {
  email: string;
  code: string;
}

export interface OTPRequest {
  email: string;
}

export interface ProfileUpdateData {
  email: string;
  name: string;
  company: string;
  cpf?: string;
  termsAccepted: boolean;
  termsVersion: string;
}

// Form Types
export interface CampaignFormData {
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  budget: number;
  targetAudience: Partial<TargetAudience>;
  products: string[]; // Product IDs
  incentives: Partial<Incentive>[];
}

export interface UserFormData {
  email: string;
  name: string;
  role: UserRole;
  industryId?: string;
}

// Filter Types
export interface CampaignFilters {
  status?: CampaignStatus[];
  dateRange?: [Date, Date];
  budgetRange?: [number, number];
  search?: string;
  sortBy?: 'name' | 'startDate' | 'budget' | 'performance';
  sortOrder?: 'asc' | 'desc';
}

export interface UserFilters {
  role?: UserRole[];
  status?: UserStatus[];
  search?: string;
  sortBy?: 'name' | 'email' | 'createdAt' | 'lastLoginAt';
  sortOrder?: 'asc' | 'desc';
}

// Notification Types
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: Date;
  actionUrl?: string;
}

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  CAMPAIGN_STARTED = 'campaign_started',
  CAMPAIGN_ENDED = 'campaign_ended',
  BUDGET_ALERT = 'budget_alert',
  PERFORMANCE_ALERT = 'performance_alert',
}
