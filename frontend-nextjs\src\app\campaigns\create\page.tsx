'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import ProductSelector from '@/components/campaigns/ProductSelector';
import { 
  Package, 
  Target, 
  Calendar, 
  DollarSign,
  Save,
  Eye,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';
import toast from 'react-hot-toast';

interface Product {
  id: string;
  sku: string;
  name: string;
  brand: string;
  category: string;
  price: number;
  imageUrl?: string;
  isActive: boolean;
  isInActiveCampaign: boolean;
  description?: string;
}

interface CampaignData {
  name: string;
  description: string;
  incentiveValue: number;
  incentivePercentage: number;
  startDate: string;
  endDate: string;
  budget: number;
  targetAudience: string;
  products: Product[];
}

export default function CreateCampaignPage() {
  const [campaignData, setCampaignData] = useState<CampaignData>({
    name: '',
    description: '',
    incentiveValue: 0,
    incentivePercentage: 0,
    startDate: '',
    endDate: '',
    budget: 0,
    targetAudience: '',
    products: [],
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  // Mock industry ID - in real app, this would come from auth context
  const industryId = 'industry-123';

  const handleProductsChange = (products: Product[]) => {
    setCampaignData(prev => ({ ...prev, products }));
  };

  const handleInputChange = (field: keyof CampaignData, value: any) => {
    setCampaignData(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveDraft = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Rascunho salvo com sucesso!');
    } catch (error) {
      toast.error('Erro ao salvar rascunho');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreview = () => {
    if (campaignData.products.length === 0) {
      toast.error('Selecione pelo menos um produto');
      return;
    }
    
    if (!campaignData.name.trim()) {
      toast.error('Digite o nome da campanha');
      return;
    }

    toast.success('Visualização da campanha (funcionalidade em desenvolvimento)');
  };

  const steps = [
    { id: 1, name: 'Informações Básicas', icon: Package },
    { id: 2, name: 'Seleção de Produtos', icon: Target },
    { id: 3, name: 'Configurações', icon: Calendar },
    { id: 4, name: 'Revisão', icon: Eye },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/dashboard" className="mr-4">
                <Button variant="outline" size="sm">
                  <ArrowLeft />
                  <span>Voltar</span>
                </Button>
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Criar Nova Campanha</h1>
                <p className="text-gray-700">Configure sua campanha de retail media</p>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Button 
                variant="outline" 
                onClick={handleSaveDraft}
                disabled={isLoading}
              >
                <Save />
                <span>Salvar Rascunho</span>
              </Button>
              <Button onClick={handlePreview}>
                <Eye className="h-4 w-4 mr-2" />
                Visualizar
              </Button>
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = currentStep === step.id;
              const isCompleted = currentStep > step.id;
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 
                    ${isActive ? 'border-blue-600 bg-blue-600 text-white' : 
                      isCompleted ? 'border-green-600 bg-green-600 text-white' : 
                      'border-gray-300 bg-white text-gray-600'}
                  `}>
                    <Icon className="h-5 w-5" />
                  </div>
                  <span className={`ml-2 text-sm font-medium ${
                    isActive ? 'text-blue-600' : 
                    isCompleted ? 'text-green-600' : 
                    'text-gray-700'
                  }`}>
                    {step.name}
                  </span>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-0.5 mx-4 ${
                      isCompleted ? 'bg-green-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle>Informações Básicas da Campanha</CardTitle>
                  <CardDescription>
                    Configure as informações principais da sua campanha
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="name">Nome da Campanha</Label>
                    <Input
                      id="name"
                      placeholder="Ex: Promoção do Produto XPTO - Verão 2025"
                      value={campaignData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="description">Descrição</Label>
                    <textarea
                      id="description"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={3}
                      placeholder="Descreva os objetivos e detalhes da campanha..."
                      value={campaignData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="incentiveValue">Valor do Incentivo (R$)</Label>
                      <Input
                        id="incentiveValue"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        value={campaignData.incentiveValue}
                        onChange={(e) => handleInputChange('incentiveValue', parseFloat(e.target.value) || 0)}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="budget">Orçamento Total (R$)</Label>
                      <Input
                        id="budget"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        value={campaignData.budget}
                        onChange={(e) => handleInputChange('budget', parseFloat(e.target.value) || 0)}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startDate">Data de Início</Label>
                      <Input
                        id="startDate"
                        type="date"
                        value={campaignData.startDate}
                        onChange={(e) => handleInputChange('startDate', e.target.value)}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="endDate">Data de Fim</Label>
                      <Input
                        id="endDate"
                        type="date"
                        value={campaignData.endDate}
                        onChange={(e) => handleInputChange('endDate', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={() => setCurrentStep(2)}>
                      Próximo: Seleção de Produtos
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 2: Product Selection */}
            {currentStep === 2 && (
              <ProductSelector
                industryId={industryId}
                selectedProducts={campaignData.products}
                onProductsChange={handleProductsChange}
                maxProducts={50}
                incentiveValue={campaignData.incentiveValue}
              />
            )}

            {/* Navigation for Step 2 */}
            {currentStep === 2 && (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>
                      <span>Voltar: Informações Básicas</span>
                    </Button>
                    <Button 
                      onClick={() => setCurrentStep(3)}
                      disabled={campaignData.products.length === 0}
                    >
                      Próximo: Configurações
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Configuration */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle>Configurações Avançadas</CardTitle>
                  <CardDescription>
                    Configure as regras e parâmetros da campanha
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="targetAudience">Público-Alvo</Label>
                    <Input
                      id="targetAudience"
                      placeholder="Ex: Mulheres, 25-45 anos, interessadas em beleza"
                      value={campaignData.targetAudience}
                      onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                    />
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>
                      <span>Voltar: Seleção de Produtos</span>
                    </Button>
                    <Button onClick={() => setCurrentStep(4)}>
                      Próximo: Revisão
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 4: Review */}
            {currentStep === 4 && (
              <Card>
                <CardHeader>
                  <CardTitle>Revisão da Campanha</CardTitle>
                  <CardDescription>
                    Revise todas as informações antes de criar a campanha
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900">Nome</h4>
                      <p className="text-gray-800">{campaignData.name || 'Não informado'}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Incentivo</h4>
                      <p className="text-gray-800">R$ {campaignData.incentiveValue.toFixed(2)}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Produtos</h4>
                      <p className="text-gray-800">{campaignData.products.length} selecionados</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Orçamento</h4>
                      <p className="text-gray-800">R$ {campaignData.budget.toFixed(2)}</p>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>
                      <span>Voltar: Configurações</span>
                    </Button>
                    <Button onClick={() => toast.success('Campanha criada com sucesso!')}>
                      Criar Campanha
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Campaign Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  Resumo da Campanha
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-800">Produtos:</span>
                  <span className="font-medium text-gray-900">{campaignData.products.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-800">Incentivo:</span>
                  <span className="font-medium text-gray-900">R$ {campaignData.incentiveValue.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-800">Orçamento:</span>
                  <span className="font-medium text-gray-900">R$ {campaignData.budget.toFixed(2)}</span>
                </div>
                {campaignData.startDate && campaignData.endDate && (
                  <div className="flex justify-between">
                    <span className="text-gray-800">Duração:</span>
                    <span className="font-medium text-gray-900">
                      {Math.ceil((new Date(campaignData.endDate).getTime() - new Date(campaignData.startDate).getTime()) / (1000 * 60 * 60 * 24))} dias
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Help */}
            <Card>
              <CardHeader>
                <CardTitle>Precisa de Ajuda?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-800 mb-3">
                  Consulte nosso guia de criação de campanhas ou entre em contato com o suporte.
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  <span>Ver Guia</span>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
