import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductSelectionService } from './product-selection.service';
import { ProductSelectionController } from './product-selection.controller';
import { Product } from '../database/entities/product.entity';
import { ProductCategory } from '../database/entities/product-category.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Product, ProductCategory])],
  controllers: [ProductSelectionController],
  providers: [ProductSelectionService],
  exports: [ProductSelectionService],
})
export class ProductSelectionModule {}
