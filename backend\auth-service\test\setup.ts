// Global test setup
import { ConfigService } from '@nestjs/config';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-secret-key';
process.env.JWT_EXPIRES_IN = '8h';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret';
process.env.JWT_REFRESH_EXPIRES_IN = '7d';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.REDIS_PASSWORD = 'test123';
process.env.RABBITMQ_URL = 'amqp://test:test@localhost:5672';

// Increase timeout for integration tests
jest.setTimeout(30000);

// Mock external services
jest.mock('amqplib', () => ({
  connect: jest.fn().mockResolvedValue({
    createChannel: jest.fn().mockResolvedValue({
      assertExchange: jest.fn(),
      assertQueue: jest.fn(),
      bindQueue: jest.fn(),
      publish: jest.fn().mockReturnValue(true),
      prefetch: jest.fn(),
      checkQueue: jest.fn().mockResolvedValue({
        messageCount: 0,
        consumerCount: 0,
      }),
    }),
    close: jest.fn(),
  }),
}));

// Mock Redis
jest.mock('cache-manager-redis-store', () => ({
  create: jest.fn().mockReturnValue({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    reset: jest.fn(),
  }),
}));

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Suppress console logs during tests unless explicitly needed
if (process.env.SUPPRESS_LOGS !== 'false') {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
}
