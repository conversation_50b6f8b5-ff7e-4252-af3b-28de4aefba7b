import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  UseGuards,
  HttpStatus,
  ParseEnumPipe,
  ParseIntPipe,
  DefaultValuePipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsDateString } from 'class-validator';

import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

import { AuditService } from './audit.service';
import { AuditLoggerService } from './audit-logger.service';

import { AuditAction, AuditLevel, AuditCategory } from '../database/entities/audit-log.entity';

// DTOs
class ExportAuditDataDto {
  @IsDateString()
  startDate: string;

  @IsDateString()
  endDate: string;

  @IsOptional()
  @IsEnum(['json', 'csv'])
  format?: 'json' | 'csv';
}

class TestAuditLogDto {
  @IsString()
  description: string;

  @IsOptional()
  @IsEnum(AuditAction)
  action?: AuditAction;

  @IsOptional()
  @IsEnum(AuditLevel)
  level?: AuditLevel;

  @IsOptional()
  @IsEnum(AuditCategory)
  category?: AuditCategory;

  @IsOptional()
  @IsString()
  entityType?: string;

  @IsOptional()
  @IsString()
  entityId?: string;
}

@ApiTags('Audit')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('api/v1/audit')
export class AuditController {
  constructor(
    private readonly auditService: AuditService,
    private readonly auditLoggerService: AuditLoggerService,
  ) {}

  @Get('logs')
  @Roles('admin', 'manager')
  @ApiOperation({ summary: 'Get audit logs' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'action', required: false })
  @ApiQuery({ name: 'category', required: false })
  @ApiQuery({ name: 'level', required: false })
  @ApiQuery({ name: 'entityType', required: false })
  @ApiQuery({ name: 'entityId', required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'offset', required: false })
  async getAuditLogs(
    @Query('userId') userId?: string,
    @Query('action') action?: AuditAction,
    @Query('category') category?: AuditCategory,
    @Query('level') level?: AuditLevel,
    @Query('entityType') entityType?: string,
    @Query('entityId') entityId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit?: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset?: number,
  ) {
    return this.auditService.getAuditLogs(
      userId,
      action,
      category,
      level,
      entityType,
      entityId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      limit,
      offset,
    );
  }

  @Get('summary')
  @Roles('admin', 'manager')
  @ApiOperation({ summary: 'Get audit summary' })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  async getAuditSummary(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.auditService.getAuditSummary(
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('security-events')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Get security events' })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'severity', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'offset', required: false })
  async getSecurityEvents(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('severity') severity?: string,
    @Query('status') status?: string,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit?: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset?: number,
  ) {
    return this.auditService.getSecurityEvents(
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      severity,
      status,
      limit,
      offset,
    );
  }

  @Get('data-access')
  @Roles('admin', 'manager', 'dpo')
  @ApiOperation({ summary: 'Get data access logs' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'dataType', required: false })
  @ApiQuery({ name: 'accessType', required: false })
  @ApiQuery({ name: 'sensitivity', required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'offset', required: false })
  async getDataAccessLogs(
    @Query('userId') userId?: string,
    @Query('dataType') dataType?: string,
    @Query('accessType') accessType?: string,
    @Query('sensitivity') sensitivity?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit?: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset?: number,
  ) {
    return this.auditService.getDataAccessLogs(
      userId,
      dataType,
      accessType,
      sensitivity,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      limit,
      offset,
    );
  }

  @Get('compliance-events')
  @Roles('admin', 'manager', 'dpo')
  @ApiOperation({ summary: 'Get compliance events' })
  @ApiQuery({ name: 'type', required: false })
  @ApiQuery({ name: 'eventType', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'dataSubjectId', required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'offset', required: false })
  async getComplianceEvents(
    @Query('type') type?: string,
    @Query('eventType') eventType?: string,
    @Query('status') status?: string,
    @Query('dataSubjectId') dataSubjectId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit?: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset?: number,
  ) {
    return this.auditService.getComplianceEvents(
      type,
      eventType,
      status,
      dataSubjectId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      limit,
      offset,
    );
  }

  @Get('compliance-report')
  @Roles('admin', 'manager', 'dpo')
  @ApiOperation({ summary: 'Get compliance report' })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  async getComplianceReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.auditService.getComplianceReport(
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Post('export')
  @Roles('admin')
  @ApiOperation({ summary: 'Export audit data' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Audit data exported successfully' })
  async exportAuditData(@Body(ValidationPipe) exportDto: ExportAuditDataDto) {
    return this.auditService.exportAuditData(
      new Date(exportDto.startDate),
      new Date(exportDto.endDate),
      exportDto.format || 'json',
    );
  }

  @Post('test-log')
  @Roles('admin')
  @ApiOperation({ summary: 'Create test audit log (for testing purposes)' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Test audit log created successfully' })
  async createTestAuditLog(@Body(ValidationPipe) testLogDto: TestAuditLogDto) {
    return this.auditLoggerService.logAuditEvent({
      action: testLogDto.action || AuditAction.READ,
      level: testLogDto.level || AuditLevel.INFO,
      category: testLogDto.category || AuditCategory.BUSINESS_LOGIC,
      entityType: testLogDto.entityType || 'Test',
      entityId: testLogDto.entityId || 'test-id',
      description: testLogDto.description,
      source: 'test',
      metadata: {
        testEvent: true,
        timestamp: new Date().toISOString(),
      },
    });
  }

  @Get('health')
  @ApiOperation({ summary: 'Check audit system health' })
  async checkHealth() {
    try {
      // Test database connectivity by counting recent logs
      const recentLogs = await this.auditService.getAuditLogs(
        undefined, undefined, undefined, undefined, undefined, undefined,
        new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        undefined,
        1, 0
      );

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        recentLogsCount: recentLogs.total,
        message: 'Audit system is operational',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
        message: 'Audit system has issues',
      };
    }
  }
}
