import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  Logger,
  Inject,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import * as moment from 'moment-timezone';

import { Campaign, CampaignStatus, IncentiveType } from '../database/entities/campaign.entity';
import { CampaignProduct } from '../database/entities/campaign-product.entity';
import { CampaignTransition, TransitionAction, TransitionType } from '../database/entities/campaign-transition.entity';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignFiltersDto } from './dto/campaign-filters.dto';
import { TransitionService } from './transition.service';
import { AIEstimationService } from './ai-estimation.service';

@Injectable()
export class CampaignsService {
  private readonly logger = new Logger(CampaignsService.name);

  constructor(
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
    @InjectRepository(CampaignProduct)
    private readonly campaignProductRepository: Repository<CampaignProduct>,
    @InjectRepository(CampaignTransition)
    private readonly transitionRepository: Repository<CampaignTransition>,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    private readonly transitionService: TransitionService,
    private readonly aiEstimationService: AIEstimationService,
  ) {}

  async findAll(filters: CampaignFiltersDto, userId: string, userRole: string): Promise<{
    data: Campaign[];
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      industryId,
      startDate,
      endDate,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = filters;

    const query = this.campaignRepository.createQueryBuilder('campaign')
      .leftJoinAndSelect('campaign.products', 'products')
      .leftJoinAndSelect('campaign.transitions', 'transitions');

    // Apply filters based on user role and industry
    if (userRole !== 'admin') {
      query.where('campaign.industryId = :industryId', { industryId });
    } else if (industryId) {
      query.where('campaign.industryId = :industryId', { industryId });
    }

    if (status) {
      if (Array.isArray(status)) {
        query.andWhere('campaign.status IN (:...status)', { status });
      } else {
        query.andWhere('campaign.status = :status', { status });
      }
    }

    if (startDate && endDate) {
      query.andWhere('campaign.startDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    if (search) {
      query.andWhere(
        '(campaign.name ILIKE :search OR campaign.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Sorting
    query.orderBy(`campaign.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // Pagination
    const offset = (page - 1) * limit;
    query.skip(offset).take(limit);

    const [data, total] = await query.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages,
      },
    };
  }

  async findOne(id: string, userId: string, userRole: string): Promise<Campaign> {
    const query = this.campaignRepository.createQueryBuilder('campaign')
      .leftJoinAndSelect('campaign.products', 'products')
      .leftJoinAndSelect('campaign.transitions', 'transitions')
      .where('campaign.id = :id', { id });

    // Apply access control
    if (userRole !== 'admin') {
      // Users can only see campaigns from their industry
      // This would need to be enhanced with proper industry validation
      query.andWhere('campaign.industryId = :industryId', { 
        industryId: 'user-industry-id' // This should come from user context
      });
    }

    const campaign = await query.getOne();

    if (!campaign) {
      throw new NotFoundException('Campanha não encontrada');
    }

    return campaign;
  }

  async create(createCampaignDto: CreateCampaignDto, userId: string): Promise<Campaign> {
    const {
      name,
      description,
      industryId,
      incentiveType,
      incentiveValue,
      incentivePercentage,
      startDate,
      endDate,
      quantityLimit,
      products,
      businessRules,
    } = createCampaignDto;

    // Validate business rules
    await this.validateBusinessRules(createCampaignDto, industryId);

    // Create campaign
    const campaign = this.campaignRepository.create({
      name,
      description,
      industryId,
      createdBy: userId,
      incentiveType,
      incentiveValue,
      incentivePercentage,
      startDate: startDate ? moment.tz(startDate, 'America/Sao_Paulo').toDate() : null,
      endDate: endDate ? moment.tz(endDate, 'America/Sao_Paulo').toDate() : null,
      quantityLimit,
      businessRules,
      status: CampaignStatus.DRAFT,
    });

    const savedCampaign = await this.campaignRepository.save(campaign);

    // Add products if provided
    if (products && products.length > 0) {
      await this.addProductsToCampaign(savedCampaign.id, products);
    }

    // Generate AI estimation
    try {
      const aiEstimation = await this.aiEstimationService.estimateCampaignPerformance(savedCampaign);
      savedCampaign.aiEstimation = aiEstimation;
      await this.campaignRepository.save(savedCampaign);
    } catch (error) {
      this.logger.warn(`Failed to generate AI estimation for campaign ${savedCampaign.id}:`, error);
    }

    this.logger.log(`Campaign created: ${savedCampaign.id} by user ${userId}`);

    return this.findOne(savedCampaign.id, userId, 'admin');
  }

  async update(id: string, updateCampaignDto: UpdateCampaignDto, userId: string): Promise<Campaign> {
    const campaign = await this.findOne(id, userId, 'admin');

    // Check if campaign can be updated
    if (campaign.status === CampaignStatus.ACTIVE) {
      throw new BadRequestException('Não é possível editar uma campanha ativa');
    }

    if (campaign.status === CampaignStatus.ENDED) {
      throw new BadRequestException('Não é possível editar uma campanha encerrada');
    }

    // Validate business rules if provided
    if (updateCampaignDto.businessRules || updateCampaignDto.incentiveValue || updateCampaignDto.incentivePercentage) {
      await this.validateBusinessRules(updateCampaignDto, campaign.industryId);
    }

    // Update campaign
    Object.assign(campaign, {
      ...updateCampaignDto,
      startDate: updateCampaignDto.startDate ? 
        moment.tz(updateCampaignDto.startDate, 'America/Sao_Paulo').toDate() : campaign.startDate,
      endDate: updateCampaignDto.endDate ? 
        moment.tz(updateCampaignDto.endDate, 'America/Sao_Paulo').toDate() : campaign.endDate,
    });

    const updatedCampaign = await this.campaignRepository.save(campaign);

    // Update AI estimation if significant changes
    if (this.shouldUpdateAIEstimation(updateCampaignDto)) {
      try {
        const aiEstimation = await this.aiEstimationService.estimateCampaignPerformance(updatedCampaign);
        updatedCampaign.aiEstimation = aiEstimation;
        await this.campaignRepository.save(updatedCampaign);
      } catch (error) {
        this.logger.warn(`Failed to update AI estimation for campaign ${id}:`, error);
      }
    }

    this.logger.log(`Campaign updated: ${id} by user ${userId}`);

    return this.findOne(id, userId, 'admin');
  }

  async activate(id: string, userId: string): Promise<Campaign> {
    const campaign = await this.findOne(id, userId, 'admin');

    if (!campaign.canBeActivated()) {
      throw new BadRequestException('Campanha não pode ser ativada no status atual');
    }

    // Validate campaign is ready for activation
    await this.validateCampaignForActivation(campaign);

    // Create transition
    const transition = CampaignTransition.createTransition({
      campaignId: id,
      action: TransitionAction.ACTIVATE,
      type: TransitionType.MANUAL,
      triggeredBy: userId,
      fromStatus: campaign.status,
      toStatus: CampaignStatus.ACTIVE,
    });

    await this.transitionService.executeTransition(transition);

    this.logger.log(`Campaign activated: ${id} by user ${userId}`);

    return this.findOne(id, userId, 'admin');
  }

  async pause(id: string, userId: string, reason?: string): Promise<Campaign> {
    const campaign = await this.findOne(id, userId, 'admin');

    if (!campaign.canBePaused()) {
      throw new BadRequestException('Campanha não pode ser pausada no status atual');
    }

    const transition = CampaignTransition.createTransition({
      campaignId: id,
      action: TransitionAction.PAUSE,
      type: TransitionType.MANUAL,
      triggeredBy: userId,
      reason,
      fromStatus: campaign.status,
      toStatus: CampaignStatus.PAUSED,
    });

    await this.transitionService.executeTransition(transition);

    this.logger.log(`Campaign paused: ${id} by user ${userId}`);

    return this.findOne(id, userId, 'admin');
  }

  async resume(id: string, userId: string): Promise<Campaign> {
    const campaign = await this.findOne(id, userId, 'admin');

    if (!campaign.canBeResumed()) {
      throw new BadRequestException('Campanha não pode ser retomada no status atual');
    }

    const transition = CampaignTransition.createTransition({
      campaignId: id,
      action: TransitionAction.RESUME,
      type: TransitionType.MANUAL,
      triggeredBy: userId,
      fromStatus: campaign.status,
      toStatus: CampaignStatus.ACTIVE,
    });

    await this.transitionService.executeTransition(transition);

    this.logger.log(`Campaign resumed: ${id} by user ${userId}`);

    return this.findOne(id, userId, 'admin');
  }

  async end(id: string, userId: string, reason?: string): Promise<Campaign> {
    const campaign = await this.findOne(id, userId, 'admin');

    if (!campaign.canBeEnded()) {
      throw new BadRequestException('Campanha não pode ser encerrada no status atual');
    }

    const transition = CampaignTransition.createTransition({
      campaignId: id,
      action: TransitionAction.END,
      type: TransitionType.MANUAL,
      triggeredBy: userId,
      reason,
      fromStatus: campaign.status,
      toStatus: CampaignStatus.ENDED,
    });

    await this.transitionService.executeTransition(transition);

    this.logger.log(`Campaign ended: ${id} by user ${userId}`);

    return this.findOne(id, userId, 'admin');
  }

  async schedule(id: string, action: TransitionAction, executeAt: Date, userId: string): Promise<Campaign> {
    const campaign = await this.findOne(id, userId, 'admin');

    // Validate the scheduled action is valid for current status
    this.validateScheduledAction(campaign, action);

    // Create scheduled transition
    const transition = CampaignTransition.createScheduledTransition(
      id,
      action,
      moment.tz(executeAt, 'America/Sao_Paulo').toDate(),
      userId,
    );

    await this.transitionRepository.save(transition);

    // Update campaign with next transition info
    campaign.scheduleTransition(action, transition.executeAt);
    await this.campaignRepository.save(campaign);

    this.logger.log(`Campaign transition scheduled: ${id} action ${action} at ${executeAt} by user ${userId}`);

    return this.findOne(id, userId, 'admin');
  }

  async getMetrics(id: string, userId: string): Promise<any> {
    const campaign = await this.findOne(id, userId, 'admin');

    // Get real-time metrics from cache if available
    const cacheKey = `campaign_metrics:${id}`;
    let metrics = await this.cacheManager.get(cacheKey);

    if (!metrics) {
      // Calculate metrics from database
      metrics = await this.calculateCampaignMetrics(campaign);
      
      // Cache for 5 minutes
      await this.cacheManager.set(cacheKey, metrics, 300);
    }

    return metrics;
  }

  private async validateBusinessRules(dto: any, industryId: string): Promise<void> {
    // Get industry business rules (this would come from a configuration service)
    const industryRules = await this.getIndustryBusinessRules(industryId);

    if (dto.incentivePercentage && dto.incentivePercentage < industryRules.minIncentivePercent) {
      throw new BadRequestException(
        `Percentual de incentivo deve ser pelo menos ${industryRules.minIncentivePercent}%`
      );
    }

    if (dto.incentiveValue && dto.incentiveValue < industryRules.minIncentiveAmount) {
      throw new BadRequestException(
        `Valor do incentivo deve ser pelo menos R$ ${industryRules.minIncentiveAmount}`
      );
    }
  }

  private async validateCampaignForActivation(campaign: Campaign): Promise<void> {
    if (!campaign.products || campaign.products.length === 0) {
      throw new BadRequestException('Campanha deve ter pelo menos um produto');
    }

    if (!campaign.startDate || !campaign.endDate) {
      throw new BadRequestException('Campanha deve ter data de início e fim definidas');
    }

    if (campaign.startDate >= campaign.endDate) {
      throw new BadRequestException('Data de início deve ser anterior à data de fim');
    }

    if (campaign.incentiveType === IncentiveType.PERCENTAGE && !campaign.incentivePercentage) {
      throw new BadRequestException('Percentual de incentivo é obrigatório');
    }

    if (campaign.incentiveType === IncentiveType.AMOUNT_BRL && !campaign.incentiveValue) {
      throw new BadRequestException('Valor do incentivo é obrigatório');
    }
  }

  private validateScheduledAction(campaign: Campaign, action: TransitionAction): void {
    switch (action) {
      case TransitionAction.ACTIVATE:
        if (!campaign.canBeActivated()) {
          throw new BadRequestException('Campanha não pode ser agendada para ativação');
        }
        break;
      case TransitionAction.END:
        if (campaign.status !== CampaignStatus.ACTIVE && campaign.status !== CampaignStatus.SCHEDULED) {
          throw new BadRequestException('Campanha não pode ser agendada para encerramento');
        }
        break;
      default:
        throw new BadRequestException('Ação não pode ser agendada');
    }
  }

  private shouldUpdateAIEstimation(updateDto: UpdateCampaignDto): boolean {
    return !!(
      updateDto.incentiveValue ||
      updateDto.incentivePercentage ||
      updateDto.quantityLimit ||
      updateDto.startDate ||
      updateDto.endDate
    );
  }

  private async addProductsToCampaign(campaignId: string, products: any[]): Promise<void> {
    for (const product of products) {
      const campaignProduct = this.campaignProductRepository.create({
        campaignId,
        ...product,
      });
      await this.campaignProductRepository.save(campaignProduct);
    }
  }

  private async calculateCampaignMetrics(campaign: Campaign): Promise<any> {
    // This would integrate with analytics service or calculate from sales data
    return {
      impactedConsumers: campaign.impactedConsumers,
      convertedConsumers: campaign.convertedConsumers,
      conversionRate: campaign.conversionRate,
      totalInvestment: campaign.totalInvestment,
      totalDiscounts: campaign.totalDiscounts,
      totalRevenue: campaign.totalRevenue,
      roi: campaign.roi,
      productsCount: campaign.products?.length || 0,
      activeProductsCount: campaign.products?.filter(p => p.isActive).length || 0,
    };
  }

  private async getIndustryBusinessRules(industryId: string): Promise<any> {
    // This would come from a configuration service or database
    return {
      minIncentivePercent: 10,
      minIncentiveAmount: 0.50,
      maxProductsPerCycle: 100,
      minQuantityPerSale: 12,
      validityDays: 10,
    };
  }
}
