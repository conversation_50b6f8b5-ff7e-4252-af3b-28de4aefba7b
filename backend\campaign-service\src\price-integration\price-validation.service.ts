import { Injectable, Logger } from '@nestjs/common';

export interface PriceValidationRule {
  name: string;
  description: string;
  validate: (price: number, context?: any) => boolean;
  errorMessage: string;
}

export interface ValidationContext {
  productId?: string;
  priceType?: string;
  previousPrice?: number;
  category?: string;
  brand?: string;
}

@Injectable()
export class PriceValidationService {
  private readonly logger = new Logger(PriceValidationService.name);
  private readonly rules: PriceValidationRule[] = [];

  constructor() {
    this.initializeDefaultRules();
  }

  private initializeDefaultRules(): void {
    // Basic price validation rules
    this.addRule({
      name: 'positive_price',
      description: 'Price must be positive',
      validate: (price) => price > 0,
      errorMessage: 'Preço deve ser maior que zero',
    });

    this.addRule({
      name: 'reasonable_range',
      description: 'Price must be within reasonable range',
      validate: (price) => price >= 0.01 && price <= 1000000,
      errorMessage: 'Preço deve estar entre R$ 0,01 e R$ 1.000.000,00',
    });

    this.addRule({
      name: 'decimal_precision',
      description: 'Price should have at most 2 decimal places',
      validate: (price) => {
        const decimalPlaces = (price.toString().split('.')[1] || '').length;
        return decimalPlaces <= 2;
      },
      errorMessage: 'Preço deve ter no máximo 2 casas decimais',
    });

    this.addRule({
      name: 'not_suspicious_round',
      description: 'Price should not be suspiciously round (like exactly 100, 1000)',
      validate: (price) => {
        // Allow round numbers but flag very round ones as potentially suspicious
        if (price % 1000 === 0 && price >= 1000) return false;
        if (price % 100 === 0 && price >= 500) return false;
        return true;
      },
      errorMessage: 'Preço parece suspeito (muito redondo)',
    });
  }

  addRule(rule: PriceValidationRule): void {
    this.rules.push(rule);
    this.logger.debug(`Added price validation rule: ${rule.name}`);
  }

  removeRule(ruleName: string): void {
    const index = this.rules.findIndex(rule => rule.name === ruleName);
    if (index !== -1) {
      this.rules.splice(index, 1);
      this.logger.debug(`Removed price validation rule: ${ruleName}`);
    }
  }

  isValidPrice(price: number, context?: ValidationContext): boolean {
    return this.validatePrice(price, context).isValid;
  }

  validatePrice(price: number, context?: ValidationContext): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic type and NaN check
    if (typeof price !== 'number' || isNaN(price)) {
      errors.push('Preço deve ser um número válido');
      return { isValid: false, errors, warnings };
    }

    // Run all validation rules
    for (const rule of this.rules) {
      try {
        if (!rule.validate(price, context)) {
          if (rule.name === 'not_suspicious_round') {
            warnings.push(rule.errorMessage);
          } else {
            errors.push(rule.errorMessage);
          }
        }
      } catch (error) {
        this.logger.error(`Error in validation rule ${rule.name}:`, error);
        warnings.push(`Erro na validação: ${rule.name}`);
      }
    }

    // Additional context-based validations
    if (context) {
      const contextValidation = this.validateWithContext(price, context);
      errors.push(...contextValidation.errors);
      warnings.push(...contextValidation.warnings);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private validateWithContext(price: number, context: ValidationContext): {
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Price change validation
    if (context.previousPrice && context.previousPrice > 0) {
      const changePercentage = ((price - context.previousPrice) / context.previousPrice) * 100;
      
      // Flag large price changes
      if (Math.abs(changePercentage) > 50) {
        warnings.push(`Mudança de preço muito grande: ${changePercentage.toFixed(1)}%`);
      }
      
      // Flag extreme price changes as errors
      if (Math.abs(changePercentage) > 200) {
        errors.push(`Mudança de preço extrema: ${changePercentage.toFixed(1)}%`);
      }
    }

    // Category-specific validations
    if (context.category) {
      const categoryValidation = this.validateByCategory(price, context.category);
      errors.push(...categoryValidation.errors);
      warnings.push(...categoryValidation.warnings);
    }

    return { errors, warnings };
  }

  private validateByCategory(price: number, category: string): {
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Define category-specific price ranges
    const categoryRanges = {
      'electronics': { min: 10, max: 50000 },
      'clothing': { min: 5, max: 5000 },
      'food': { min: 0.5, max: 1000 },
      'books': { min: 5, max: 500 },
      'toys': { min: 2, max: 2000 },
      'home': { min: 10, max: 20000 },
      'beauty': { min: 5, max: 2000 },
      'sports': { min: 10, max: 10000 },
    };

    const normalizedCategory = category.toLowerCase();
    const range = categoryRanges[normalizedCategory];

    if (range) {
      if (price < range.min) {
        warnings.push(`Preço baixo para categoria ${category}: mínimo esperado R$ ${range.min}`);
      }
      
      if (price > range.max) {
        warnings.push(`Preço alto para categoria ${category}: máximo esperado R$ ${range.max}`);
      }
    }

    return { errors, warnings };
  }

  validatePriceBatch(prices: Array<{
    price: number;
    context?: ValidationContext;
  }>): Array<{
    index: number;
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    return prices.map((item, index) => ({
      index,
      ...this.validatePrice(item.price, item.context),
    }));
  }

  getValidationRules(): PriceValidationRule[] {
    return [...this.rules]; // Return a copy to prevent external modification
  }

  // Utility methods for common price operations
  formatPrice(price: number, currency: string = 'BRL'): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency,
    }).format(price);
  }

  roundPrice(price: number, precision: number = 2): number {
    const factor = Math.pow(10, precision);
    return Math.round(price * factor) / factor;
  }

  calculatePriceChange(oldPrice: number, newPrice: number): {
    amount: number;
    percentage: number;
    direction: 'increase' | 'decrease' | 'same';
  } {
    const amount = newPrice - oldPrice;
    const percentage = oldPrice > 0 ? (amount / oldPrice) * 100 : 0;
    
    let direction: 'increase' | 'decrease' | 'same';
    if (amount > 0) {
      direction = 'increase';
    } else if (amount < 0) {
      direction = 'decrease';
    } else {
      direction = 'same';
    }

    return {
      amount: this.roundPrice(amount),
      percentage: this.roundPrice(percentage),
      direction,
    };
  }

  // Statistical validation methods
  detectPriceAnomalies(prices: number[]): {
    outliers: number[];
    mean: number;
    median: number;
    standardDeviation: number;
  } {
    if (prices.length === 0) {
      return { outliers: [], mean: 0, median: 0, standardDeviation: 0 };
    }

    const sorted = [...prices].sort((a, b) => a - b);
    const mean = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const median = sorted.length % 2 === 0
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)];

    const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
    const standardDeviation = Math.sqrt(variance);

    // Detect outliers using IQR method
    const q1Index = Math.floor(sorted.length * 0.25);
    const q3Index = Math.floor(sorted.length * 0.75);
    const q1 = sorted[q1Index];
    const q3 = sorted[q3Index];
    const iqr = q3 - q1;
    const lowerBound = q1 - 1.5 * iqr;
    const upperBound = q3 + 1.5 * iqr;

    const outliers = prices.filter(price => price < lowerBound || price > upperBound);

    return {
      outliers,
      mean: this.roundPrice(mean),
      median: this.roundPrice(median),
      standardDeviation: this.roundPrice(standardDeviation),
    };
  }
}
