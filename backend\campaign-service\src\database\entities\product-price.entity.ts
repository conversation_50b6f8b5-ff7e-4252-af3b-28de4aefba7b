import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Product } from './product.entity';
import { PriceProvider } from './price-provider.entity';
import { PriceStatus, PriceType } from './price-types.entity';

@Entity('product_prices')
@Index(['productId', 'providerId', 'priceType'])
@Index(['productId', 'status'])
@Index(['lastUpdated'])
@Index(['expiresAt'])
export class ProductPrice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  @Index()
  productId: string;

  @Column({ type: 'uuid' })
  @Index()
  providerId: string;

  @Column({ type: 'enum', enum: PriceType, default: PriceType.REGULAR })
  priceType: PriceType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ type: 'varchar', length: 3, default: 'BRL' })
  currency: string;

  @Column({ type: 'enum', enum: PriceStatus, default: PriceStatus.ACTIVE })
  status: PriceStatus;

  @Column({ type: 'timestamp', nullable: true })
  validFrom: Date;

  @Column({ type: 'timestamp', nullable: true })
  validTo: Date;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date; // Cache expiration

  @Column({ type: 'timestamp' })
  lastUpdated: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  externalId: string; // ID from external system

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // Additional price data

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'int', default: 0 })
  retryCount: number;

  @Column({ type: 'timestamp', nullable: true })
  nextRetryAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'productId' })
  product: Product;

  @ManyToOne(() => PriceProvider, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'providerId' })
  provider: PriceProvider;

  // Helper methods
  isExpired(): boolean {
    return this.expiresAt && new Date() > this.expiresAt;
  }

  isValid(): boolean {
    const now = new Date();
    
    if (this.status !== PriceStatus.ACTIVE) {
      return false;
    }

    if (this.validFrom && now < this.validFrom) {
      return false;
    }

    if (this.validTo && now > this.validTo) {
      return false;
    }

    return !this.isExpired();
  }

  needsUpdate(): boolean {
    return this.isExpired() || this.status === PriceStatus.ERROR;
  }

  canRetry(): boolean {
    if (this.status !== PriceStatus.ERROR) {
      return false;
    }

    if (!this.nextRetryAt) {
      return true;
    }

    return new Date() >= this.nextRetryAt;
  }

  markAsError(errorMessage: string): void {
    this.status = PriceStatus.ERROR;
    this.errorMessage = errorMessage;
    this.retryCount += 1;
    
    // Exponential backoff: 1min, 5min, 15min, 30min, 1hr, 2hr, 4hr, 8hr
    const backoffMinutes = Math.min(Math.pow(2, this.retryCount - 1), 480);
    this.nextRetryAt = new Date(Date.now() + backoffMinutes * 60 * 1000);
  }

  markAsActive(price: number, expiresInMinutes: number = 60): void {
    this.status = PriceStatus.ACTIVE;
    this.price = price;
    this.lastUpdated = new Date();
    this.expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000);
    this.errorMessage = null;
    this.retryCount = 0;
    this.nextRetryAt = null;
  }

  getFormattedPrice(): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: this.currency,
    }).format(this.price);
  }
}
