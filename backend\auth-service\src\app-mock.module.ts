import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';

import { AuthMockController } from './auth/auth-mock.controller';
import { AuthMockService } from './auth/auth-mock.service';
import { EmailService } from './email/email.service';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // JWT
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '8h'),
        },
      }),
      inject: [ConfigService],
      global: true,
    }),
  ],
  controllers: [AppController, AuthMockController],
  providers: [AppService, AuthMockService, EmailService],
})
export class AppMockModule {}
