Write-Host "Testing Reports API with JWT..." -ForegroundColor Cyan

$headers = @{
    "Content-Type" = "application/json"
}

# Step 1: Verify OTP and get token (using OTP from logs: 197462)
Write-Host "`n=== Step 1: Verify OTP and get token ===" -ForegroundColor Yellow
$verifyBody = @{
    email = "<EMAIL>"
    code = "197462"
} | ConvertTo-Json

try {
    $authResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/auth/industries/verify-otp" `
        -Method POST `
        -Headers $headers `
        -Body $verifyBody `
        -UseBasicParsing

    Write-Host "Auth Status: $($authResponse.StatusCode)" -ForegroundColor Green
    $authData = $authResponse.Content | ConvertFrom-Json
    $accessToken = $authData.accessToken
    Write-Host "Access Token: $($accessToken.Substring(0, 50))..." -ForegroundColor Green
} catch {
    Write-Host "Error verifying OTP: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
    exit 1
}

# Step 2: Call Reports API with token
Write-Host "`n=== Step 2: Call Reports API with token ===" -ForegroundColor Yellow
$authHeaders = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $accessToken"
}

$reportBody = @{
    dateRange = @{
        startDate = "2025-01-01"
        endDate = "2025-12-31"
    }
    status = @("active", "paused")
} | ConvertTo-Json

try {
    $reportResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/reports/campaign-performance" `
        -Method POST `
        -Headers $authHeaders `
        -Body $reportBody `
        -UseBasicParsing

    Write-Host "Report Status: $($reportResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Report Data:" -ForegroundColor Green
    $reportResponse.Content | ConvertFrom-Json | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error calling Reports API: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
    exit 1
}

Write-Host "`n=== All tests passed! ===" -ForegroundColor Green

