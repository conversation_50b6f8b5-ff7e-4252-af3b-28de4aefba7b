import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
  BeforeUpdate,
  AfterLoad,
  VersionColumn,
} from 'typeorm';

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ENDED = 'ended',
}

export enum IncentiveType {
  PERCENTAGE = 'percentage',
  AMOUNT_BRL = 'amount_brl',
}

/**
 * Optimized Campaign entity with performance best practices
 * - Proper indexing strategy
 * - Efficient column types
 * - Lazy loading for heavy relations
 * - Computed properties
 * - Version control for optimistic locking
 */
@Entity('campaigns_optimized')
@Index('IDX_campaign_industry_status', ['industryId', 'status'])
@Index('IDX_campaign_dates', ['startDate', 'endDate'])
@Index('IDX_campaign_created_by', ['createdBy'])
@Index('IDX_campaign_active_period', ['status', 'startDate', 'endDate'], {
  where: "status IN ('active', 'scheduled')"
}) // Partial index for active campaigns
@Index('IDX_campaign_search', ['name', 'description'], {
  where: "status != 'draft'"
}) // Full-text search index
export class OptimizedCampaign {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Campaign name for display'
  })
  name: string;

  @Column({ 
    type: 'text', 
    nullable: true,
    comment: 'Campaign description'
  })
  description: string;

  @Column({ 
    type: 'uuid',
    comment: 'Industry that owns this campaign'
  })
  industryId: string;

  @Column({ 
    type: 'uuid',
    comment: 'User who created this campaign'
  })
  createdBy: string;

  @Column({
    type: 'enum',
    enum: CampaignStatus,
    default: CampaignStatus.DRAFT,
    comment: 'Current campaign status'
  })
  status: CampaignStatus;

  @Column({
    type: 'enum',
    enum: IncentiveType,
    default: IncentiveType.PERCENTAGE,
    comment: 'Type of incentive offered'
  })
  incentiveType: IncentiveType;

  @Column({ 
    type: 'decimal', 
    precision: 10, 
    scale: 2,
    comment: 'Incentive value (percentage or BRL amount)'
  })
  incentiveValue: number;

  @Column({ 
    type: 'decimal', 
    precision: 15, 
    scale: 2, 
    nullable: true,
    comment: 'Total campaign budget in BRL'
  })
  budget: number;

  @Column({ 
    type: 'decimal', 
    precision: 15, 
    scale: 2, 
    default: 0,
    comment: 'Amount spent so far'
  })
  spentAmount: number;

  @Column({
    type: 'timestamp',
    comment: 'Campaign start date and time'
  })
  startDate: Date;

  @Column({
    type: 'timestamp',
    comment: 'Campaign end date and time'
  })
  endDate: Date;

  @Column({ 
    type: 'integer', 
    default: 0,
    comment: 'Number of products in campaign'
  })
  productCount: number;

  @Column({ 
    type: 'integer', 
    default: 0,
    comment: 'Number of conversions achieved'
  })
  conversionCount: number;

  @Column({ 
    type: 'decimal', 
    precision: 15, 
    scale: 2, 
    default: 0,
    comment: 'Total revenue generated'
  })
  totalRevenue: number;

  @Column({ 
    type: 'jsonb', 
    nullable: true,
    comment: 'Campaign metadata and settings'
  })
  metadata: {
    targetAudience?: {
      ageRange?: [number, number];
      gender?: 'male' | 'female' | 'all';
      location?: string[];
      interests?: string[];
    };
    rules?: {
      maxUsagePerUser?: number;
      minPurchaseAmount?: number;
      validDays?: string[];
    };
    analytics?: {
      impressions?: number;
      clicks?: number;
      ctr?: number;
      conversionRate?: number;
    };
  };

  @Column({ 
    type: 'boolean', 
    default: true,
    comment: 'Whether campaign is active'
  })
  @Index() // Frequently filtered
  isActive: boolean;

  @Column({ 
    type: 'boolean', 
    default: false,
    comment: 'Whether campaign is featured'
  })
  isFeatured: boolean;

  @Column({ 
    type: 'timestamp', 
    nullable: true,
    comment: 'When campaign was published'
  })
  publishedAt: Date;

  @Column({ 
    type: 'timestamp', 
    nullable: true,
    comment: 'When campaign was paused'
  })
  pausedAt: Date;

  @Column({ 
    type: 'uuid', 
    nullable: true,
    comment: 'User who paused the campaign'
  })
  pausedBy: string;

  @CreateDateColumn({
    comment: 'When record was created'
  })
  @Index() // For sorting and filtering
  createdAt: Date;

  @UpdateDateColumn({
    comment: 'When record was last updated'
  })
  updatedAt: Date;

  @VersionColumn({
    comment: 'Version for optimistic locking'
  })
  version: number;

  // Computed properties (not stored in DB)
  private _isExpired?: boolean;
  private _daysRemaining?: number;
  private _roi?: number;

  @AfterLoad()
  computeProperties() {
    const now = new Date();
    this._isExpired = this.endDate < now;
    this._daysRemaining = Math.max(0, Math.ceil((this.endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
    this._roi = this.spentAmount > 0 ? ((this.totalRevenue - this.spentAmount) / this.spentAmount) * 100 : 0;
  }

  get isExpired(): boolean {
    return this._isExpired ?? this.endDate < new Date();
  }

  get daysRemaining(): number {
    if (this._daysRemaining !== undefined) return this._daysRemaining;
    const now = new Date();
    return Math.max(0, Math.ceil((this.endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
  }

  get roi(): number {
    if (this._roi !== undefined) return this._roi;
    return this.spentAmount > 0 ? ((this.totalRevenue - this.spentAmount) / this.spentAmount) * 100 : 0;
  }

  get conversionRate(): number {
    const impressions = this.metadata?.analytics?.impressions || 0;
    return impressions > 0 ? (this.conversionCount / impressions) * 100 : 0;
  }

  get budgetUtilization(): number {
    return this.budget > 0 ? (this.spentAmount / this.budget) * 100 : 0;
  }

  @BeforeInsert()
  @BeforeUpdate()
  validateDates() {
    if (this.startDate >= this.endDate) {
      throw new Error('Start date must be before end date');
    }
    
    if (this.budget && this.budget <= 0) {
      throw new Error('Budget must be positive');
    }
    
    if (this.incentiveValue <= 0) {
      throw new Error('Incentive value must be positive');
    }
  }

  @BeforeUpdate()
  updateSpentAmount() {
    // Recalculate spent amount based on conversions
    if (this.conversionCount > 0 && this.incentiveType === IncentiveType.AMOUNT_BRL) {
      this.spentAmount = this.conversionCount * this.incentiveValue;
    }
  }

  // Helper methods for business logic
  canBeActivated(): boolean {
    return this.status === CampaignStatus.DRAFT || this.status === CampaignStatus.SCHEDULED;
  }

  canBePaused(): boolean {
    return this.status === CampaignStatus.ACTIVE;
  }

  canBeResumed(): boolean {
    return this.status === CampaignStatus.PAUSED && !this.isExpired;
  }

  isWithinBudget(additionalSpend: number = 0): boolean {
    if (!this.budget) return true;
    return (this.spentAmount + additionalSpend) <= this.budget;
  }

  getRemainingBudget(): number {
    return this.budget ? Math.max(0, this.budget - this.spentAmount) : Infinity;
  }

  // Relations would be defined here with proper lazy loading
  // @OneToMany(() => CampaignProduct, product => product.campaign, { lazy: true })
  // products: Promise<CampaignProduct[]>;
  
  // @OneToMany(() => CampaignMetric, metric => metric.campaign, { lazy: true })
  // metrics: Promise<CampaignMetric[]>;
}
