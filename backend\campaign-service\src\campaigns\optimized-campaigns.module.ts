import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OptimizedCampaign } from '../database/entities/optimized-campaign.entity';
import { OptimizedCampaignRepository } from '../database/repositories/optimized-campaign.repository';
import { OptimizedCampaignsService } from './optimized-campaigns.service';
import { DatabasePerformanceConfig } from '../config/database-performance.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([OptimizedCampaign]),
  ],
  providers: [
    OptimizedCampaignRepository,
    OptimizedCampaignsService,
    DatabasePerformanceConfig,
  ],
  exports: [
    OptimizedCampaignsService,
    OptimizedCampaignRepository,
    DatabasePerformanceConfig,
  ],
})
export class OptimizedCampaignsModule {}
