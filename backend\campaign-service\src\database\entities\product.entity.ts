import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ProductCategory } from './product-category.entity';

export enum ProductStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DISCONTINUED = 'discontinued',
  PENDING = 'pending',
}

export enum ProductType {
  SIMPLE = 'simple',
  VARIANT = 'variant',
  BUNDLE = 'bundle',
  DIGITAL = 'digital',
}

@Entity('products')
export class Product {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  sku: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  barcode: string;

  @Column({ type: 'uuid', nullable: true })
  categoryId: string;

  @ManyToOne(() => ProductCategory, { nullable: true })
  @JoinColumn({ name: 'categoryId' })
  category?: ProductCategory;

  @Column({ type: 'uuid', nullable: true })
  subcategoryId: string;

  @Column({ type: 'varchar', length: 255 })
  industryId: string;

  @Column({ type: 'varchar', length: 255 })
  brandId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  manufacturerId: string;

  @Column({
    type: 'enum',
    enum: ProductType,
    default: ProductType.SIMPLE,
  })
  type: ProductType;

  @Column({
    type: 'enum',
    enum: ProductStatus,
    default: ProductStatus.ACTIVE,
  })
  status: ProductStatus;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  price: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  costPrice: number;

  @Column({ type: 'varchar', length: 10, nullable: true })
  currency: string;

  @Column({ type: 'decimal', precision: 8, scale: 3, nullable: true })
  weight: number;

  @Column({ type: 'varchar', length: 10, nullable: true })
  weightUnit: string;

  @Column({ type: 'json', nullable: true })
  dimensions: {
    length?: number;
    width?: number;
    height?: number;
    unit?: string;
  };

  @Column({ type: 'json', nullable: true })
  images: string[];

  @Column({ type: 'json', nullable: true })
  attributes: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'boolean', default: true })
  isEligibleForIncentives: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  maxIncentivePercentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxIncentiveValue: number;

  @Column({ type: 'int', default: 0 })
  stockQuantity: number;

  @Column({ type: 'int', nullable: true })
  minStockLevel: number;

  @Column({ type: 'boolean', default: true })
  trackStock: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  supplier: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  supplierSku: string;

  @Column({ type: 'date', nullable: true })
  launchDate: Date;

  @Column({ type: 'date', nullable: true })
  discontinueDate: Date;

  @Column({ type: 'boolean', default: false })
  isFeatured: boolean;

  @Column({ type: 'boolean', default: false })
  isPromotional: boolean;

  @Column({ type: 'int', default: 0 })
  salesCount: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  averageRating: number;

  @Column({ type: 'int', default: 0 })
  reviewCount: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  seoTitle: string;

  @Column({ type: 'text', nullable: true })
  seoDescription: string;

  @Column({ type: 'json', nullable: true })
  seoKeywords: string[];

  @Column({ type: 'varchar', length: 255, nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  updatedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  getDisplayName(): string {
    return `${this.name} (${this.sku})`;
  }

  isAvailable(): boolean {
    return this.status === ProductStatus.ACTIVE && 
           (!this.trackStock || this.stockQuantity > 0);
  }

  canReceiveIncentive(): boolean {
    return this.isEligibleForIncentives && this.isAvailable();
  }

  getMaxIncentive(type: 'percentage' | 'value'): number | null {
    if (type === 'percentage') {
      return this.maxIncentivePercentage;
    }
    return this.maxIncentiveValue;
  }

  isInStock(): boolean {
    if (!this.trackStock) {
      return true;
    }
    return this.stockQuantity > (this.minStockLevel || 0);
  }

  getStockStatus(): 'in_stock' | 'low_stock' | 'out_of_stock' {
    if (!this.trackStock) {
      return 'in_stock';
    }
    
    if (this.stockQuantity <= 0) {
      return 'out_of_stock';
    }
    
    if (this.minStockLevel && this.stockQuantity <= this.minStockLevel) {
      return 'low_stock';
    }
    
    return 'in_stock';
  }

  calculateIncentiveValue(percentage: number): number {
    if (!this.price) {
      return 0;
    }
    return (this.price * percentage) / 100;
  }

  validateIncentive(percentage?: number, value?: number): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.canReceiveIncentive()) {
      errors.push('Produto não elegível para incentivos');
    }

    if (percentage !== undefined) {
      if (this.maxIncentivePercentage && percentage > this.maxIncentivePercentage) {
        errors.push(`Percentual máximo permitido: ${this.maxIncentivePercentage}%`);
      }
    }

    if (value !== undefined) {
      if (this.maxIncentiveValue && value > this.maxIncentiveValue) {
        errors.push(`Valor máximo permitido: R$ ${this.maxIncentiveValue.toFixed(2)}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
