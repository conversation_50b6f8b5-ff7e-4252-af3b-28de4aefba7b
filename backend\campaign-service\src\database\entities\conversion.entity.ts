import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Campaign } from './campaign.entity';

export enum ConversionType {
  PURCHASE = 'purchase',
  SIGNUP = 'signup',
  LEAD = 'lead',
  DOWNLOAD = 'download',
  SUBSCRIPTION = 'subscription',
  FORM_SUBMIT = 'form_submit',
  PHONE_CALL = 'phone_call',
  STORE_VISIT = 'store_visit',
  APP_INSTALL = 'app_install',
  VIDEO_COMPLETE = 'video_complete',
  CUSTOM = 'custom',
}

export enum ConversionStatus {
  COMPLETED = 'completed',
  PENDING = 'pending',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

@Entity('conversions')
@Index(['userId', 'timestamp'])
@Index(['campaignId', 'timestamp'])
@Index(['type', 'timestamp'])
@Index(['status', 'timestamp'])
@Index(['timestamp'])
@Index(['sessionId'])
@Index(['orderId'])
export class Conversion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  userId: string;

  @Column({ type: 'uuid', nullable: true })
  @Index()
  campaignId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  sessionId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  orderId: string;

  @Column({ type: 'enum', enum: ConversionType })
  type: ConversionType;

  @Column({ type: 'enum', enum: ConversionStatus, default: ConversionStatus.COMPLETED })
  status: ConversionStatus;

  @Column({ type: 'timestamp' })
  @Index()
  timestamp: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  value: number;

  @Column({ type: 'varchar', length: 3, default: 'BRL' })
  currency: string;

  @Column({ type: 'int', default: 1 })
  quantity: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  productId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  productName: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  productCategory: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  productBrand: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  productSku: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  conversionUrl: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  referrer: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  source: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  medium: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  campaign: string;

  @Column({ type: 'varchar', length: 45, nullable: true })
  ipAddress: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  userAgent: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  countryCode: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  city: string;

  @Column({ type: 'json', nullable: true })
  customAttributes: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  productDetails: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => Campaign, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'campaignId' })
  campaignEntity: Campaign;

  // Helper methods
  getFormattedValue(): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: this.currency,
    }).format(this.value);
  }

  getTotalValue(): number {
    return this.value * this.quantity;
  }

  getFormattedTotalValue(): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: this.currency,
    }).format(this.getTotalValue());
  }

  isValidForAttribution(): boolean {
    return this.status === ConversionStatus.COMPLETED && this.value > 0;
  }

  getConversionTypeLabel(): string {
    const labels = {
      [ConversionType.PURCHASE]: 'Compra',
      [ConversionType.SIGNUP]: 'Cadastro',
      [ConversionType.LEAD]: 'Lead',
      [ConversionType.DOWNLOAD]: 'Download',
      [ConversionType.SUBSCRIPTION]: 'Assinatura',
      [ConversionType.FORM_SUBMIT]: 'Envio de Formulário',
      [ConversionType.PHONE_CALL]: 'Ligação',
      [ConversionType.STORE_VISIT]: 'Visita à Loja',
      [ConversionType.APP_INSTALL]: 'Instalação de App',
      [ConversionType.VIDEO_COMPLETE]: 'Vídeo Completo',
      [ConversionType.CUSTOM]: 'Personalizado',
    };

    return labels[this.type] || this.type;
  }

  getStatusLabel(): string {
    const labels = {
      [ConversionStatus.COMPLETED]: 'Concluído',
      [ConversionStatus.PENDING]: 'Pendente',
      [ConversionStatus.CANCELLED]: 'Cancelado',
      [ConversionStatus.REFUNDED]: 'Reembolsado',
    };

    return labels[this.status] || this.status;
  }

  static createFromEvent(eventData: {
    userId: string;
    campaignId?: string;
    sessionId?: string;
    orderId?: string;
    type: ConversionType;
    value: number;
    currency?: string;
    quantity?: number;
    productId?: string;
    productName?: string;
    productCategory?: string;
    productBrand?: string;
    productSku?: string;
    conversionUrl?: string;
    referrer?: string;
    source?: string;
    medium?: string;
    campaign?: string;
    ipAddress?: string;
    userAgent?: string;
    customAttributes?: Record<string, any>;
    productDetails?: Record<string, any>;
    notes?: string;
  }): Partial<Conversion> {
    return {
      userId: eventData.userId,
      campaignId: eventData.campaignId,
      sessionId: eventData.sessionId,
      orderId: eventData.orderId,
      type: eventData.type,
      status: ConversionStatus.COMPLETED,
      timestamp: new Date(),
      value: eventData.value,
      currency: eventData.currency || 'BRL',
      quantity: eventData.quantity || 1,
      productId: eventData.productId,
      productName: eventData.productName,
      productCategory: eventData.productCategory,
      productBrand: eventData.productBrand,
      productSku: eventData.productSku,
      conversionUrl: eventData.conversionUrl,
      referrer: eventData.referrer,
      source: eventData.source,
      medium: eventData.medium,
      campaign: eventData.campaign,
      ipAddress: eventData.ipAddress,
      userAgent: eventData.userAgent,
      customAttributes: eventData.customAttributes,
      productDetails: eventData.productDetails,
      notes: eventData.notes,
    };
  }
}
