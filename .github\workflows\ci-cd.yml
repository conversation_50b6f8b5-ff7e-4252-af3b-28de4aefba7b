name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Test Backend Services
  test-backend:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth-service, campaign-service]

    env:
      NODE_ENV: test
      JWT_SECRET: test-secret-key-for-ci
      JWT_EXPIRES_IN: 8h
      JWT_REFRESH_SECRET: test-refresh-secret-for-ci
      JWT_REFRESH_EXPIRES_IN: 7d
      DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
      REDIS_HOST: localhost
      REDIS_PORT: 6379
      RABBITMQ_URL: amqp://guest:guest@localhost:5672

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: backend/${{ matrix.service }}/package-lock.json

    - name: Wait for services
      run: |
        echo "Waiting for PostgreSQL..."
        until pg_isready -h localhost -p 5432 -U postgres; do
          echo "PostgreSQL is unavailable - sleeping"
          sleep 1
        done
        echo "PostgreSQL is up!"

        echo "Waiting for Redis..."
        until redis-cli -h localhost -p 6379 ping; do
          echo "Redis is unavailable - sleeping"
          sleep 1
        done
        echo "Redis is up!"

    - name: Install dependencies
      working-directory: backend/${{ matrix.service }}
      run: npm ci

    - name: Run linting
      working-directory: backend/${{ matrix.service }}
      run: npm run lint

    - name: Run unit tests
      working-directory: backend/${{ matrix.service }}
      run: npm run test:cov

    - name: Run e2e tests
      working-directory: backend/${{ matrix.service }}
      run: npm run test:e2e
      continue-on-error: true

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: backend/${{ matrix.service }}/coverage/lcov.info
        flags: ${{ matrix.service }}
        name: ${{ matrix.service }}-coverage

  # Test Frontend
  test-frontend:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: frontend-nextjs/package-lock.json

    - name: Install dependencies
      working-directory: frontend-nextjs
      run: npm ci --legacy-peer-deps

    - name: Run linting
      working-directory: frontend-nextjs
      run: npm run lint
      continue-on-error: true

    - name: Run tests
      working-directory: frontend-nextjs
      run: npm run test -- --passWithNoTests --watchAll=false
      env:
        CI: true

    - name: Build application
      working-directory: frontend-nextjs
      run: npm run build
      env:
        CI: true

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: frontend-nextjs/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    # Snyk scan commented out - requires SNYK_TOKEN secret
    # - name: Run Snyk security scan
    #   uses: snyk/actions/node@master
    #   env:
    #     SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
    #   with:
    #     args: --severity-threshold=high

  # Build and Push Docker Images - COMMENTED OUT FOR NOW (no server yet)
  # Uncomment when you have a container registry configured
  # build-and-push:
  #   runs-on: ubuntu-latest
  #   needs: [test-backend, test-frontend, security-scan]
  #   if: github.ref == 'refs/heads/main'
  #
  #   strategy:
  #     matrix:
  #       service: [auth-service, campaign-service, frontend]
  #
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4
  #
  #   - name: Set up Docker Buildx
  #     uses: docker/setup-buildx-action@v3
  #
  #   - name: Log in to Container Registry
  #     uses: docker/login-action@v3
  #     with:
  #       registry: ${{ env.REGISTRY }}
  #       username: ${{ github.actor }}
  #       password: ${{ secrets.GITHUB_TOKEN }}
  #
  #   - name: Extract metadata
  #     id: meta
  #     uses: docker/metadata-action@v5
  #     with:
  #       images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
  #       tags: |
  #         type=ref,event=branch
  #         type=ref,event=pr
  #         type=sha,prefix={{branch}}-
  #         type=raw,value=latest,enable={{is_default_branch}}
  #
  #   - name: Build and push Docker image
  #     uses: docker/build-push-action@v5
  #     with:
  #       context: ${{ matrix.service == 'frontend' && './frontend' || format('./backend/{0}', matrix.service) }}
  #       file: ${{ matrix.service == 'frontend' && './frontend/Dockerfile.prod' || format('./backend/{0}/Dockerfile.prod', matrix.service) }}
  #       push: true
  #       tags: ${{ steps.meta.outputs.tags }}
  #       labels: ${{ steps.meta.outputs.labels }}
  #       cache-from: type=gha
  #       cache-to: type=gha,mode=max

  # Deploy to Staging - COMMENTED OUT FOR NOW (no server yet)
  # deploy-staging:
  #   runs-on: ubuntu-latest
  #   needs: build-and-push
  #   if: github.ref == 'refs/heads/main'
  #   environment: staging
  #
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4
  #
  #   - name: Setup kubectl
  #     uses: azure/setup-kubectl@v3
  #     with:
  #       version: 'v1.28.0'
  #
  #   - name: Configure kubectl
  #     run: |
  #       echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > kubeconfig
  #       export KUBECONFIG=kubeconfig
  #
  #   - name: Deploy to staging
  #     run: |
  #       export KUBECONFIG=kubeconfig
  #
  #       # Update image tags in deployment files
  #       sed -i "s|IMAGE_TAG|${{ github.sha }}|g" infrastructure/k8s/staging/*.yaml
  #
  #       # Apply Kubernetes manifests
  #       kubectl apply -f infrastructure/k8s/staging/
  #
  #       # Wait for rollout to complete
  #       kubectl rollout status deployment/auth-service -n retail-media-staging
  #       kubectl rollout status deployment/campaign-service -n retail-media-staging
  #       kubectl rollout status deployment/frontend -n retail-media-staging
  #
  #   - name: Run smoke tests
  #     run: |
  #       export KUBECONFIG=kubeconfig
  #
  #       # Get service URLs
  #       AUTH_URL=$(kubectl get service auth-service -n retail-media-staging -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
  #       FRONTEND_URL=$(kubectl get service frontend -n retail-media-staging -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
  #
  #       # Run basic health checks
  #       curl -f http://$AUTH_URL:3001/health || exit 1
  #       curl -f http://$FRONTEND_URL:3000/health || exit 1

  # Deploy to Production - COMMENTED OUT FOR NOW (no server yet)
  # deploy-production:
  #   runs-on: ubuntu-latest
  #   needs: deploy-staging
  #   if: github.ref == 'refs/heads/main'
  #   environment: production
  #
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4
  #
  #   - name: Setup kubectl
  #     uses: azure/setup-kubectl@v3
  #     with:
  #       version: 'v1.28.0'
  #
  #   - name: Configure kubectl
  #     run: |
  #       echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
  #       export KUBECONFIG=kubeconfig
  #
  #   - name: Deploy to production
  #     run: |
  #       export KUBECONFIG=kubeconfig
  #
  #       # Update image tags in deployment files
  #       sed -i "s|IMAGE_TAG|${{ github.sha }}|g" infrastructure/k8s/production/*.yaml
  #
  #       # Apply Kubernetes manifests with rolling update
  #       kubectl apply -f infrastructure/k8s/production/
  #
  #       # Wait for rollout to complete
  #       kubectl rollout status deployment/auth-service -n retail-media-production --timeout=600s
  #       kubectl rollout status deployment/campaign-service -n retail-media-production --timeout=600s
  #       kubectl rollout status deployment/frontend -n retail-media-production --timeout=600s
  #
  #   - name: Run production smoke tests
  #     run: |
  #       export KUBECONFIG=kubeconfig
  #
  #       # Get service URLs
  #       AUTH_URL=$(kubectl get service auth-service -n retail-media-production -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
  #       FRONTEND_URL=$(kubectl get service frontend -n retail-media-production -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
  #
  #       # Run comprehensive health checks
  #       curl -f http://$AUTH_URL:3001/health || exit 1
  #       curl -f http://$FRONTEND_URL:3000/health || exit 1
  #
  #       # Test critical endpoints
  #       curl -f -X POST http://$AUTH_URL:3001/auth/request-otp \
  #         -H "Content-Type: application/json" \
  #         -d '{"email":"<EMAIL>"}' || echo "OTP endpoint test failed"
  #
  #   - name: Notify deployment success
  #     uses: 8398a7/action-slack@v3
  #     with:
  #       status: success
  #       channel: '#deployments'
  #       text: '🚀 Retail Media deployed successfully to production!'
  #     env:
  #       SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Rollback on failure - COMMENTED OUT FOR NOW (no server yet)
  # rollback-production:
  #   runs-on: ubuntu-latest
  #   needs: deploy-production
  #   if: failure() && github.ref == 'refs/heads/main'
  #   environment: production
  #
  #   steps:
  #   - name: Setup kubectl
  #     uses: azure/setup-kubectl@v3
  #     with:
  #       version: 'v1.28.0'
  #
  #   - name: Configure kubectl
  #     run: |
  #       echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
  #       export KUBECONFIG=kubeconfig
  #
  #   - name: Rollback deployment
  #     run: |
  #       export KUBECONFIG=kubeconfig
  #
  #       # Rollback to previous version
  #       kubectl rollout undo deployment/auth-service -n retail-media-production
  #       kubectl rollout undo deployment/campaign-service -n retail-media-production
  #       kubectl rollout undo deployment/frontend -n retail-media-production
  #
  #       # Wait for rollback to complete
  #       kubectl rollout status deployment/auth-service -n retail-media-production
  #       kubectl rollout status deployment/campaign-service -n retail-media-production
  #       kubectl rollout status deployment/frontend -n retail-media-production
  #
  #   - name: Notify rollback
  #     uses: 8398a7/action-slack@v3
  #     with:
  #       status: failure
  #       channel: '#deployments'
  #       text: '⚠️ Retail Media deployment failed and was rolled back!'
  #     env:
  #       SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
