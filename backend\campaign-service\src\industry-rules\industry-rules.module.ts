import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IndustryRulesService } from './industry-rules.service';
import { IndustryRulesController } from './industry-rules.controller';
import { IndustryRule } from '../database/entities/industry-rules.entity';

@Module({
  imports: [TypeOrmModule.forFeature([IndustryRule])],
  controllers: [IndustryRulesController],
  providers: [IndustryRulesService],
  exports: [IndustryRulesService],
})
export class IndustryRulesModule {}
