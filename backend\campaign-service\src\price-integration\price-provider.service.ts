import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, timeout } from 'rxjs';

import { PriceProvider, ProviderType } from '../database/entities/price-provider.entity';
import { PriceType } from '../database/entities/price-types.entity';

export interface PriceData {
  price: number;
  currency?: string;
  validFrom?: Date;
  validTo?: Date;
  metadata?: Record<string, any>;
}

@Injectable()
export class PriceProviderService {
  private readonly logger = new Logger(PriceProviderService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async fetchPrice(
    provider: PriceProvider,
    productId: string,
    priceType: PriceType
  ): Promise<PriceData | null> {
    switch (provider.type) {
      case ProviderType.API:
        return this.fetchFromApi(provider, productId, priceType);
      case ProviderType.PDV:
        return this.fetchFromPdv(provider, productId, priceType);
      case ProviderType.ERP:
        return this.fetchFromErp(provider, productId, priceType);
      case ProviderType.FALLBACK:
        return this.fetchFromFallback(provider, productId, priceType);
      default:
        throw new Error(`Unsupported provider type: ${provider.type}`);
    }
  }

  private async fetchFromApi(
    provider: PriceProvider,
    productId: string,
    priceType: PriceType
  ): Promise<PriceData | null> {
    if (!provider.apiUrl) {
      throw new Error('API URL not configured for provider');
    }

    try {
      // Build request URL
      const url = this.buildApiUrl(provider.apiUrl, productId, priceType, provider.mapping);
      
      // Prepare headers
      const headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Retail-Media-System/1.0',
        ...provider.apiConfig?.headers,
      };

      // Add authentication if configured
      if (provider.apiConfig?.auth) {
        const auth = provider.apiConfig.auth;
        if (auth.type === 'bearer' && auth.token) {
          headers['Authorization'] = `Bearer ${auth.token}`;
        } else if (auth.type === 'apikey' && auth.key && auth.value) {
          headers[auth.key] = auth.value;
        }
      }

      this.logger.debug(`Fetching price from API: ${url}`);

      // Make HTTP request with timeout
      const response = await firstValueFrom(
        this.httpService.get(url, { headers }).pipe(
          timeout(provider.timeoutMs || 30000)
        )
      );

      // Parse response based on provider mapping
      return this.parseApiResponse((response as any).data, provider.mapping);
    } catch (error) {
      this.logger.error(`API request failed for provider ${provider.name}:`, error.message);
      throw new Error(`API request failed: ${error.message}`);
    }
  }

  private async fetchFromPdv(
    provider: PriceProvider,
    productId: string,
    priceType: PriceType
  ): Promise<PriceData | null> {
    // Simulate PDV integration
    // In a real implementation, this would connect to a PDV system
    this.logger.debug(`Simulating PDV price fetch for product ${productId}`);
    
    // Mock PDV response with realistic price
    const mockPrice = this.generateMockPrice(productId, 'pdv');
    
    return {
      price: mockPrice,
      currency: 'BRL',
      metadata: {
        source: 'pdv',
        timestamp: new Date().toISOString(),
        productId,
      },
    };
  }

  private async fetchFromErp(
    provider: PriceProvider,
    productId: string,
    priceType: PriceType
  ): Promise<PriceData | null> {
    // Simulate ERP integration
    // In a real implementation, this would connect to an ERP system
    this.logger.debug(`Simulating ERP price fetch for product ${productId}`);
    
    // Mock ERP response with realistic price
    const mockPrice = this.generateMockPrice(productId, 'erp');
    
    return {
      price: mockPrice,
      currency: 'BRL',
      validFrom: new Date(),
      validTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Valid for 30 days
      metadata: {
        source: 'erp',
        timestamp: new Date().toISOString(),
        productId,
        priceType,
      },
    };
  }

  private async fetchFromFallback(
    provider: PriceProvider,
    productId: string,
    priceType: PriceType
  ): Promise<PriceData | null> {
    // Fallback provider returns a default price
    // This could be a fixed price, average price, or last known price
    this.logger.debug(`Using fallback price for product ${productId}`);
    
    const fallbackPrice = this.generateMockPrice(productId, 'fallback');
    
    return {
      price: fallbackPrice,
      currency: 'BRL',
      metadata: {
        source: 'fallback',
        timestamp: new Date().toISOString(),
        productId,
        note: 'Fallback price used due to provider unavailability',
      },
    };
  }

  private buildApiUrl(
    baseUrl: string,
    productId: string,
    priceType: PriceType,
    mapping?: Record<string, any>
  ): string {
    let url = baseUrl;

    // Replace placeholders in URL
    url = url.replace('{productId}', encodeURIComponent(productId));
    url = url.replace('{priceType}', encodeURIComponent(priceType));

    // Add query parameters if configured
    if (mapping?.queryParams) {
      const params = new URLSearchParams();
      Object.entries(mapping.queryParams).forEach(([key, value]) => {
        let processedValue: any = value;
        if (typeof processedValue === 'string') {
          processedValue = processedValue.replace('{productId}', productId);
          processedValue = processedValue.replace('{priceType}', priceType);
        }
        params.append(key, String(processedValue));
      });
      
      const separator = url.includes('?') ? '&' : '?';
      url += separator + params.toString();
    }

    return url;
  }

  private parseApiResponse(
    data: any,
    mapping?: Record<string, any>
  ): PriceData | null {
    if (!data) {
      return null;
    }

    try {
      // Default mapping
      let priceField = 'price';
      let currencyField = 'currency';
      let validFromField = 'validFrom';
      let validToField = 'validTo';

      // Use custom mapping if provided
      if (mapping?.fields) {
        priceField = mapping.fields.price || priceField;
        currencyField = mapping.fields.currency || currencyField;
        validFromField = mapping.fields.validFrom || validFromField;
        validToField = mapping.fields.validTo || validToField;
      }

      // Extract price (required)
      const price = this.getNestedValue(data, priceField);
      if (price === null || price === undefined) {
        throw new Error(`Price field '${priceField}' not found in response`);
      }

      const numericPrice = parseFloat(price);
      if (isNaN(numericPrice) || numericPrice < 0) {
        throw new Error(`Invalid price value: ${price}`);
      }

      // Extract optional fields
      const currency = this.getNestedValue(data, currencyField) || 'BRL';
      const validFrom = this.parseDate(this.getNestedValue(data, validFromField));
      const validTo = this.parseDate(this.getNestedValue(data, validToField));

      return {
        price: numericPrice,
        currency,
        validFrom,
        validTo,
        metadata: {
          rawResponse: data,
          parsedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error('Failed to parse API response:', error.message);
      throw new Error(`Response parsing failed: ${error.message}`);
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  private parseDate(value: any): Date | undefined {
    if (!value) return undefined;
    
    const date = new Date(value);
    return isNaN(date.getTime()) ? undefined : date;
  }

  private generateMockPrice(productId: string, source: string): number {
    // Generate a consistent mock price based on productId
    // This ensures the same product always gets the same price from the same source
    const hash = this.simpleHash(productId + source);
    const basePrice = 10 + (hash % 1000); // Price between 10 and 1010
    
    // Add some variation based on source
    const sourceMultiplier = {
      pdv: 1.0,
      erp: 0.95, // ERP prices slightly lower
      fallback: 1.1, // Fallback prices slightly higher
    };

    return Math.round((basePrice * (sourceMultiplier[source] || 1.0)) * 100) / 100;
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  async testProvider(provider: PriceProvider): Promise<{
    success: boolean;
    responseTime: number;
    error?: string;
    sampleData?: any;
  }> {
    const startTime = Date.now();
    
    try {
      // Use a test product ID
      const testProductId = 'test-product-123';
      const result = await this.fetchPrice(provider, testProductId, PriceType.REGULAR);
      
      const responseTime = Date.now() - startTime;
      
      return {
        success: true,
        responseTime,
        sampleData: result,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        success: false,
        responseTime,
        error: error.message,
      };
    }
  }
}
