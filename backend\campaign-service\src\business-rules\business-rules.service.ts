import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BusinessRule, BusinessRuleType, BusinessRuleStatus } from '../database/entities/business-rule.entity';
import { validateCampaignAgainstRules, ValidationResult } from '../utils/business-rules.utils';

export interface CreateBusinessRuleDto {
  type: BusinessRuleType;
  name: string;
  description?: string;
  enabled?: boolean;
  industryId?: string;
  minPercentage?: number;
  maxPercentage?: number;
  minValue?: number;
  maxValue?: number;
  minCount?: number;
  maxCount?: number;
  durationDays?: number;
  configuration?: Record<string, any>;
  errorMessage?: string;
  warningMessage?: string;
  priority?: number;
  effectiveFrom?: Date;
  effectiveTo?: Date;
}

export interface UpdateBusinessRuleDto extends Partial<CreateBusinessRuleDto> {
  status?: BusinessRuleStatus;
}

@Injectable()
export class BusinessRulesService {
  private readonly logger = new Logger(BusinessRulesService.name);

  constructor(
    @InjectRepository(BusinessRule)
    private readonly businessRuleRepository: Repository<BusinessRule>,
  ) {}

  /**
   * Create a new business rule
   */
  async createRule(
    createDto: CreateBusinessRuleDto,
    createdBy: string,
  ): Promise<BusinessRule> {
    this.logger.log(`Creating business rule: ${createDto.name}`);

    // Validate rule configuration
    this.validateRuleConfiguration(createDto);

    // Check for existing rule of same type for same industry
    const existingRule = await this.businessRuleRepository.findOne({
      where: {
        type: createDto.type,
        industryId: createDto.industryId || null,
      },
    });

    if (existingRule) {
      throw new BadRequestException(
        `Business rule of type ${createDto.type} already exists for this industry`
      );
    }

    const rule = this.businessRuleRepository.create({
      ...createDto,
      createdBy,
      status: BusinessRuleStatus.ACTIVE,
    });

    const savedRule = await this.businessRuleRepository.save(rule);
    this.logger.log(`Business rule created with ID: ${savedRule.id}`);

    return savedRule;
  }

  /**
   * Get all business rules with optional filtering
   */
  async getRules(filters?: {
    industryId?: string;
    type?: BusinessRuleType;
    enabled?: boolean;
    status?: BusinessRuleStatus;
  }): Promise<BusinessRule[]> {
    const queryBuilder = this.businessRuleRepository.createQueryBuilder('rule');

    if (filters?.industryId) {
      queryBuilder.andWhere('rule.industryId = :industryId', {
        industryId: filters.industryId,
      });
    }

    if (filters?.type) {
      queryBuilder.andWhere('rule.type = :type', { type: filters.type });
    }

    if (filters?.enabled !== undefined) {
      queryBuilder.andWhere('rule.enabled = :enabled', {
        enabled: filters.enabled,
      });
    }

    if (filters?.status) {
      queryBuilder.andWhere('rule.status = :status', { status: filters.status });
    }

    queryBuilder.orderBy('rule.priority', 'ASC').addOrderBy('rule.createdAt', 'DESC');

    return queryBuilder.getMany();
  }

  /**
   * Get a specific business rule by ID
   */
  async getRuleById(id: string): Promise<BusinessRule> {
    const rule = await this.businessRuleRepository.findOne({
      where: { id },
    });

    if (!rule) {
      throw new NotFoundException(`Business rule with ID ${id} not found`);
    }

    return rule;
  }

  /**
   * Update a business rule
   */
  async updateRule(
    id: string,
    updateDto: UpdateBusinessRuleDto,
    updatedBy: string,
  ): Promise<BusinessRule> {
    this.logger.log(`Updating business rule: ${id}`);

    const rule = await this.getRuleById(id);

    // Validate updated configuration
    if (updateDto.type || updateDto.minPercentage !== undefined || 
        updateDto.maxPercentage !== undefined || updateDto.minValue !== undefined ||
        updateDto.maxValue !== undefined || updateDto.minCount !== undefined ||
        updateDto.maxCount !== undefined) {
      this.validateRuleConfiguration({ ...rule, ...updateDto });
    }

    Object.assign(rule, updateDto, { updatedBy });

    const savedRule = await this.businessRuleRepository.save(rule);
    this.logger.log(`Business rule updated: ${savedRule.id}`);

    return savedRule;
  }

  /**
   * Delete a business rule
   */
  async deleteRule(id: string): Promise<void> {
    this.logger.log(`Deleting business rule: ${id}`);

    const rule = await this.getRuleById(id);
    await this.businessRuleRepository.remove(rule);

    this.logger.log(`Business rule deleted: ${id}`);
  }

  /**
   * Enable or disable a business rule
   */
  async toggleRule(id: string, enabled: boolean, updatedBy: string): Promise<BusinessRule> {
    this.logger.log(`${enabled ? 'Enabling' : 'Disabling'} business rule: ${id}`);

    const rule = await this.getRuleById(id);
    rule.enabled = enabled;
    rule.updatedBy = updatedBy;

    const savedRule = await this.businessRuleRepository.save(rule);
    this.logger.log(`Business rule ${enabled ? 'enabled' : 'disabled'}: ${savedRule.id}`);

    return savedRule;
  }

  /**
   * Validate campaign data against applicable business rules
   */
  async validateCampaign(
    campaignData: any,
    industryId?: string,
  ): Promise<ValidationResult> {
    this.logger.log(`Validating campaign against business rules for industry: ${industryId}`);

    // Get applicable rules (global + industry-specific)
    const rules = await this.getApplicableRules(industryId);

    // Validate campaign data
    const result = validateCampaignAgainstRules(campaignData, rules);

    this.logger.log(
      `Campaign validation result: ${result.isValid ? 'VALID' : 'INVALID'} ` +
      `(${result.errors.length} errors, ${result.warnings.length} warnings)`
    );

    return result;
  }

  /**
   * Get rules applicable to a specific industry (global + industry-specific)
   */
  async getApplicableRules(industryId?: string): Promise<BusinessRule[]> {
    const queryBuilder = this.businessRuleRepository.createQueryBuilder('rule');

    // Get global rules (industryId is null) and industry-specific rules
    if (industryId) {
      queryBuilder.where('rule.industryId IS NULL OR rule.industryId = :industryId', {
        industryId,
      });
    } else {
      queryBuilder.where('rule.industryId IS NULL');
    }

    queryBuilder
      .andWhere('rule.enabled = :enabled', { enabled: true })
      .andWhere('rule.status = :status', { status: BusinessRuleStatus.ACTIVE })
      .orderBy('rule.priority', 'ASC')
      .addOrderBy('rule.createdAt', 'DESC');

    return queryBuilder.getMany();
  }

  /**
   * Initialize default business rules for a new industry
   */
  async initializeDefaultRules(industryId: string, createdBy: string): Promise<BusinessRule[]> {
    this.logger.log(`Initializing default business rules for industry: ${industryId}`);

    const defaultRules: CreateBusinessRuleDto[] = [
      {
        type: BusinessRuleType.INCENTIVE_PERCENTAGE,
        name: 'Incentivo Mínimo (%)',
        description: 'Percentual mínimo de incentivo permitido para campanhas',
        minPercentage: 0.1,
        maxPercentage: 100,
        errorMessage: 'Percentual deve ser entre 0,1% e 100%',
        priority: 1,
        industryId,
      },
      {
        type: BusinessRuleType.INCENTIVE_VALUE,
        name: 'Incentivo Mínimo (R$)',
        description: 'Valor mínimo em reais de incentivo permitido',
        minValue: 0.01,
        errorMessage: 'Valor mínimo de incentivo é R$ 0,01',
        priority: 2,
        industryId,
      },
      {
        type: BusinessRuleType.PRODUCTS_PER_CYCLE,
        name: 'Limite de Produtos por Ciclo',
        description: 'Número máximo de produtos permitidos por ciclo de campanha',
        maxCount: 1000,
        errorMessage: 'Limite de produtos por ciclo excedido',
        priority: 3,
        industryId,
      },
    ];

    const createdRules: BusinessRule[] = [];

    for (const ruleDto of defaultRules) {
      try {
        const rule = await this.createRule(ruleDto, createdBy);
        createdRules.push(rule);
      } catch (error) {
        this.logger.warn(`Failed to create default rule ${ruleDto.type}: ${error.message}`);
      }
    }

    this.logger.log(`Created ${createdRules.length} default rules for industry: ${industryId}`);
    return createdRules;
  }

  /**
   * Validate rule configuration
   */
  private validateRuleConfiguration(config: any): void {
    // Validate percentage ranges
    if (config.minPercentage !== undefined) {
      if (config.minPercentage < 0 || config.minPercentage > 100) {
        throw new BadRequestException('Minimum percentage must be between 0 and 100');
      }
    }

    if (config.maxPercentage !== undefined) {
      if (config.maxPercentage < 0 || config.maxPercentage > 100) {
        throw new BadRequestException('Maximum percentage must be between 0 and 100');
      }
    }

    if (config.minPercentage !== undefined && config.maxPercentage !== undefined) {
      if (config.minPercentage > config.maxPercentage) {
        throw new BadRequestException('Minimum percentage cannot be greater than maximum percentage');
      }
    }

    // Validate value ranges
    if (config.minValue !== undefined && config.minValue < 0) {
      throw new BadRequestException('Minimum value cannot be negative');
    }

    if (config.maxValue !== undefined && config.maxValue < 0) {
      throw new BadRequestException('Maximum value cannot be negative');
    }

    if (config.minValue !== undefined && config.maxValue !== undefined) {
      if (config.minValue > config.maxValue) {
        throw new BadRequestException('Minimum value cannot be greater than maximum value');
      }
    }

    // Validate count ranges
    if (config.minCount !== undefined && config.minCount < 0) {
      throw new BadRequestException('Minimum count cannot be negative');
    }

    if (config.maxCount !== undefined && config.maxCount < 0) {
      throw new BadRequestException('Maximum count cannot be negative');
    }

    if (config.minCount !== undefined && config.maxCount !== undefined) {
      if (config.minCount > config.maxCount) {
        throw new BadRequestException('Minimum count cannot be greater than maximum count');
      }
    }

    // Validate duration
    if (config.durationDays !== undefined && config.durationDays < 1) {
      throw new BadRequestException('Duration must be at least 1 day');
    }
  }
}
