import { AppDataSource } from '../../config/database.config';
import { CampaignSeed } from './campaign-seed';

async function runSeeds() {
  try {
    console.log('Initializing database connection...');
    await AppDataSource.initialize();
    console.log('Database connection established.');

    console.log('Running campaign seeds...');
    await CampaignSeed.run(AppDataSource);

    console.log('All seeds completed successfully!');
  } catch (error) {
    console.error('Error running seeds:', error);
    process.exit(1);
  } finally {
    await AppDataSource.destroy();
    console.log('Database connection closed.');
  }
}

runSeeds();
