import {
  Injectable,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';

import { Campaign, CampaignStatus } from '../database/entities/campaign.entity';
import { CampaignTransition, TransitionAction, TransitionStatus, TransitionType } from '../database/entities/campaign-transition.entity';

@Injectable()
export class TransitionService {
  private readonly logger = new Logger(TransitionService.name);

  constructor(
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
    @InjectRepository(CampaignTransition)
    private readonly transitionRepository: Repository<CampaignTransition>,
  ) {}

  async executeTransition(transition: CampaignTransition): Promise<void> {
    this.logger.log(`Executing transition ${transition.id}: ${transition.action} for campaign ${transition.campaignId}`);

    try {
      // Mark as processing
      transition.markAsProcessing();
      await this.transitionRepository.save(transition);

      // Get campaign
      const campaign = await this.campaignRepository.findOne({
        where: { id: transition.campaignId },
      });

      if (!campaign) {
        throw new Error(`Campaign ${transition.campaignId} not found`);
      }

      // Check idempotency
      if (transition.isIdempotent && transition.idempotencyKey) {
        const existingTransition = await this.transitionRepository.findOne({
          where: {
            campaignId: transition.campaignId,
            action: transition.action,
            status: TransitionStatus.COMPLETED,
            idempotencyKey: transition.idempotencyKey,
          },
        });

        if (existingTransition) {
          this.logger.log(`Idempotent transition already completed: ${transition.idempotencyKey}`);
          transition.markAsCompleted({ idempotentSkip: true });
          await this.transitionRepository.save(transition);
          return;
        }
      }

      // Execute the specific action
      const result = await this.executeAction(campaign, transition);

      // Mark as completed
      transition.markAsCompleted(result);
      await this.transitionRepository.save(transition);

      this.logger.log(`Transition completed successfully: ${transition.id}`);

    } catch (error) {
      this.logger.error(`Transition failed: ${transition.id}`, error.stack);
      
      const shouldRetry = this.shouldRetryTransition(error);
      transition.markAsFailed(error.message, shouldRetry);
      await this.transitionRepository.save(transition);

      if (!shouldRetry) {
        // Send alert or notification about permanent failure
        await this.handlePermanentFailure(transition, error);
      }

      throw error;
    }
  }

  private async executeAction(campaign: Campaign, transition: CampaignTransition): Promise<any> {
    const oldStatus = campaign.status;

    switch (transition.action) {
      case TransitionAction.ACTIVATE:
        return this.activateCampaign(campaign, transition);

      case TransitionAction.PAUSE:
        return this.pauseCampaign(campaign, transition);

      case TransitionAction.RESUME:
        return this.resumeCampaign(campaign, transition);

      case TransitionAction.END:
        return this.endCampaign(campaign, transition);

      case TransitionAction.REACTIVATE:
        return this.reactivateCampaign(campaign, transition);

      default:
        throw new Error(`Unknown transition action: ${transition.action}`);
    }
  }

  private async activateCampaign(campaign: Campaign, transition: CampaignTransition): Promise<any> {
    if (!campaign.canBeActivated()) {
      throw new BadRequestException('Campaign cannot be activated in current status');
    }

    const oldStatus = campaign.status;
    campaign.status = CampaignStatus.ACTIVE;
    campaign.publishedAt = new Date();
    campaign.clearScheduledTransition();

    await this.campaignRepository.save(campaign);

    // Schedule automatic end if end date is set
    if (campaign.endDate) {
      await this.scheduleAutomaticEnd(campaign);
    }

    return {
      oldStatus,
      newStatus: campaign.status,
      publishedAt: campaign.publishedAt,
    };
  }

  private async pauseCampaign(campaign: Campaign, transition: CampaignTransition): Promise<any> {
    if (!campaign.canBePaused()) {
      throw new BadRequestException('Campaign cannot be paused in current status');
    }

    const oldStatus = campaign.status;
    campaign.status = CampaignStatus.PAUSED;
    campaign.pausedAt = new Date();
    campaign.pausedBy = transition.triggeredBy;
    campaign.clearScheduledTransition();

    await this.campaignRepository.save(campaign);

    return {
      oldStatus,
      newStatus: campaign.status,
      pausedAt: campaign.pausedAt,
      pausedBy: campaign.pausedBy,
    };
  }

  private async resumeCampaign(campaign: Campaign, transition: CampaignTransition): Promise<any> {
    if (!campaign.canBeResumed()) {
      throw new BadRequestException('Campaign cannot be resumed in current status');
    }

    const oldStatus = campaign.status;
    campaign.status = CampaignStatus.ACTIVE;
    campaign.pausedAt = null;
    campaign.pausedBy = null;

    await this.campaignRepository.save(campaign);

    // Reschedule automatic end if needed
    if (campaign.endDate && campaign.endDate > new Date()) {
      await this.scheduleAutomaticEnd(campaign);
    }

    return {
      oldStatus,
      newStatus: campaign.status,
      resumedAt: new Date(),
    };
  }

  private async endCampaign(campaign: Campaign, transition: CampaignTransition): Promise<any> {
    if (!campaign.canBeEnded()) {
      throw new BadRequestException('Campaign cannot be ended in current status');
    }

    const oldStatus = campaign.status;
    campaign.status = CampaignStatus.ENDED;
    campaign.endedAt = new Date();
    campaign.endedBy = transition.triggeredBy;
    campaign.clearScheduledTransition();

    await this.campaignRepository.save(campaign);

    // Generate final report
    await this.generateFinalReport(campaign);

    return {
      oldStatus,
      newStatus: campaign.status,
      endedAt: campaign.endedAt,
      endedBy: campaign.endedBy,
    };
  }

  private async reactivateCampaign(campaign: Campaign, transition: CampaignTransition): Promise<any> {
    if (!campaign.canBeReactivated()) {
      throw new BadRequestException('Campaign cannot be reactivated in current status');
    }

    const oldStatus = campaign.status;
    campaign.status = CampaignStatus.REACTIVATED;
    campaign.endedAt = null;
    campaign.endedBy = null;

    // Reset some metrics for the new cycle
    campaign.quantityUsed = 0;
    campaign.impactedConsumers = 0;
    campaign.convertedConsumers = 0;

    await this.campaignRepository.save(campaign);

    return {
      oldStatus,
      newStatus: campaign.status,
      reactivatedAt: new Date(),
    };
  }

  private async scheduleAutomaticEnd(campaign: Campaign): Promise<void> {
    const transition = CampaignTransition.createTransition({
      campaignId: campaign.id,
      action: TransitionAction.END,
      type: TransitionType.AUTOMATIC,
      executeAt: campaign.endDate,
      reason: 'Automatic end based on campaign end date',
    });

    await this.transitionRepository.save(transition);

    campaign.scheduleTransition(TransitionAction.END, campaign.endDate);
    await this.campaignRepository.save(campaign);
  }

  private shouldRetryTransition(error: any): boolean {
    // Don't retry business logic errors
    if (error instanceof BadRequestException) {
      return false;
    }

    // Don't retry if campaign not found
    if (error.message?.includes('not found')) {
      return false;
    }

    // Retry for database connection issues, timeouts, etc.
    return true;
  }

  private async handlePermanentFailure(transition: CampaignTransition, error: any): Promise<void> {
    this.logger.error(`Permanent failure for transition ${transition.id}:`, error);
    
    // TODO: Send notification to administrators
    // TODO: Create alert in monitoring system
  }

  private async generateFinalReport(campaign: Campaign): Promise<void> {
    // TODO: Generate and store final campaign report
    // This would integrate with analytics service
    this.logger.log(`Generating final report for campaign ${campaign.id}`);
  }

  // Cron job to process pending transitions every 30 seconds
  @Cron('*/30 * * * * *')
  async processPendingTransitions(): Promise<void> {
    try {
      const pendingTransitions = await this.transitionRepository.find({
        where: {
          status: TransitionStatus.PENDING,
        },
        order: {
          executeAt: 'ASC',
        },
        take: 10, // Process max 10 at a time
      });

      const now = new Date();
      const readyTransitions = pendingTransitions.filter(
        t => !t.executeAt || t.executeAt <= now
      );

      for (const transition of readyTransitions) {
        try {
          await this.executeTransition(transition);
        } catch (error) {
          // Error is already logged in executeTransition
          continue;
        }
      }

      if (readyTransitions.length > 0) {
        this.logger.log(`Processed ${readyTransitions.length} pending transitions`);
      }

    } catch (error) {
      this.logger.error('Error processing pending transitions:', error);
    }
  }

  // Cron job to retry failed transitions
  @Cron(CronExpression.EVERY_MINUTE)
  async retryFailedTransitions(): Promise<void> {
    try {
      const failedTransitions = await this.transitionRepository.find({
        where: {
          status: TransitionStatus.FAILED,
        },
        order: {
          nextRetryAt: 'ASC',
        },
        take: 5, // Process max 5 retries at a time
      });

      const now = new Date();
      const retryTransitions = failedTransitions.filter(
        t => t.shouldRetry && (!t.nextRetryAt || t.nextRetryAt <= now)
      );

      for (const transition of retryTransitions) {
        try {
          this.logger.log(`Retrying failed transition ${transition.id} (attempt ${transition.retryCount + 1})`);
          
          // Reset status to pending for retry
          transition.status = TransitionStatus.PENDING;
          transition.errorMessage = null;
          await this.transitionRepository.save(transition);

          await this.executeTransition(transition);
        } catch (error) {
          // Error is already logged in executeTransition
          continue;
        }
      }

      if (retryTransitions.length > 0) {
        this.logger.log(`Retried ${retryTransitions.length} failed transitions`);
      }

    } catch (error) {
      this.logger.error('Error retrying failed transitions:', error);
    }
  }

  // Manual method to process specific transition
  async processTransition(transitionId: string): Promise<void> {
    const transition = await this.transitionRepository.findOne({
      where: { id: transitionId },
    });

    if (!transition) {
      throw new Error(`Transition ${transitionId} not found`);
    }

    if (transition.isCompleted) {
      throw new Error(`Transition ${transitionId} is already completed`);
    }

    await this.executeTransition(transition);
  }

  // Get transition history for a campaign
  async getTransitionHistory(campaignId: string): Promise<CampaignTransition[]> {
    return this.transitionRepository.find({
      where: { campaignId },
      order: { createdAt: 'DESC' },
    });
  }

  // Cancel a pending transition
  async cancelTransition(transitionId: string, reason?: string): Promise<void> {
    const transition = await this.transitionRepository.findOne({
      where: { id: transitionId },
    });

    if (!transition) {
      throw new Error(`Transition ${transitionId} not found`);
    }

    if (transition.isCompleted) {
      throw new Error('Cannot cancel completed transition');
    }

    transition.cancel(reason);
    await this.transitionRepository.save(transition);

    // Clear scheduled transition from campaign if this was the next one
    const campaign = await this.campaignRepository.findOne({
      where: { id: transition.campaignId },
    });

    if (campaign && campaign.nextTransitionAt && 
        campaign.nextTransitionAction === transition.action) {
      campaign.clearScheduledTransition();
      await this.campaignRepository.save(campaign);
    }

    this.logger.log(`Transition cancelled: ${transitionId}`);
  }

  // Cron job to activate scheduled campaigns every 5 minutes
  @Cron('0 */5 * * * *')
  async activateScheduledCampaigns(): Promise<void> {
    try {
      const now = new Date();

      // Find campaigns scheduled to start
      const campaignsToActivate = await this.campaignRepository.find({
        where: {
          status: CampaignStatus.SCHEDULED,
        },
      });

      // Filter by start date (TypeORM syntax varies by version)
      const filteredCampaigns = campaignsToActivate.filter(
        campaign => campaign.startDate && campaign.startDate <= now
      );

      this.logger.log(`Found ${filteredCampaigns.length} campaigns to activate`);

      for (const campaign of filteredCampaigns) {
        try {
          // Create activation transition
          const transition = this.transitionRepository.create({
            campaignId: campaign.id,
            action: TransitionAction.ACTIVATE,
            type: TransitionType.AUTOMATIC,
            fromStatus: campaign.status,
            toStatus: CampaignStatus.ACTIVE,
            executeAt: now,
            triggeredBy: 'system',
            reason: 'Ativação automática agendada',
            isIdempotent: true,
            idempotencyKey: `auto-activate-${campaign.id}-${now.getTime()}`,
          });

          await this.transitionRepository.save(transition);
          await this.executeTransition(transition);

          this.logger.log(`Campaign ${campaign.id} activated automatically`);
        } catch (error) {
          this.logger.error(`Error activating campaign ${campaign.id}:`, error);
        }
      }
    } catch (error) {
      this.logger.error('Error in activateScheduledCampaigns job:', error);
    }
  }

  // Cron job to expire campaigns every minute
  @Cron(CronExpression.EVERY_MINUTE)
  async expireActiveCampaigns(): Promise<void> {
    try {
      const now = new Date();

      // Find active campaigns that should expire
      const activeCampaigns = await this.campaignRepository.find({
        where: {
          status: CampaignStatus.ACTIVE,
        },
      });

      // Filter by end date
      const campaignsToExpire = activeCampaigns.filter(
        campaign => campaign.endDate && campaign.endDate <= now
      );

      this.logger.log(`Found ${campaignsToExpire.length} campaigns to expire`);

      for (const campaign of campaignsToExpire) {
        try {
          // Create expiration transition
          const transition = this.transitionRepository.create({
            campaignId: campaign.id,
            action: TransitionAction.END,
            type: TransitionType.AUTOMATIC,
            fromStatus: campaign.status,
            toStatus: CampaignStatus.ENDED,
            executeAt: now,
            triggeredBy: 'system',
            reason: 'Expiração automática por data de término',
            isIdempotent: true,
            idempotencyKey: `auto-expire-${campaign.id}-${now.getTime()}`,
          });

          await this.transitionRepository.save(transition);
          await this.executeTransition(transition);

          this.logger.log(`Campaign ${campaign.id} expired automatically`);
        } catch (error) {
          this.logger.error(`Error expiring campaign ${campaign.id}:`, error);
        }
      }
    } catch (error) {
      this.logger.error('Error in expireActiveCampaigns job:', error);
    }
  }

  // Cron job to send expiration warnings (runs every hour)
  @Cron('0 0 * * * *')
  async sendExpirationWarnings(): Promise<void> {
    try {
      const now = new Date();
      const warningTime = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours from now

      // Find campaigns expiring in the next 24 hours
      const activeCampaigns = await this.campaignRepository.find({
        where: {
          status: CampaignStatus.ACTIVE,
        },
      });

      const campaignsExpiringSoon = activeCampaigns.filter(
        campaign => campaign.endDate &&
                   campaign.endDate >= now &&
                   campaign.endDate <= warningTime
      );

      this.logger.log(`Found ${campaignsExpiringSoon.length} campaigns expiring in 24 hours`);

      for (const campaign of campaignsExpiringSoon) {
        // This would integrate with notification service
        this.logger.log(`Campaign ${campaign.id} (${campaign.name}) expires in less than 24 hours`);

        // Here you would send notifications to campaign owners
        // await this.notificationService.sendExpirationWarning(campaign);
      }
    } catch (error) {
      this.logger.error('Error in sendExpirationWarnings job:', error);
    }
  }
}
