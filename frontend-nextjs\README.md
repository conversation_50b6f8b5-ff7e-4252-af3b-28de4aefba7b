# Frontend - Retail Media Platform

Frontend da plataforma Retail Media desenvolvido com Next.js 15, React 19 e TypeScript.

## Stack Tecnológica

- **Next.js 15** com App Router
- **React 19**
- **TypeScript**
- **Tailwind CSS 4**
- **shadcn/ui** para componentes
- **React Hook Form** + **Zod** para formulários
- **React Hot Toast** para notificações

## Desenvolvimento

```bash
# Instalar dependências
npm install

# Executar em modo desenvolvimento
npm run dev

# Build para produção
npm run build

# Executar build de produção
npm start

# Linting
npm run lint
```

## Estrutura

```
src/
├── app/                 # App Router (Next.js 15)
├── components/          # Componentes reutilizáveis
├── contexts/           # Contextos React
├── hooks/              # Custom hooks
├── lib/                # Utilitários
└── types/              # Definições TypeScript
```

## Funcionalidades Implementadas

- ✅ Autenticação por OTP
- ✅ Dashboard de campanhas
- ✅ Gestão de usuários (RBAC)
- ✅ Relatórios e analytics
- ✅ Interface responsiva

## Variáveis de Ambiente

```env
NEXT_PUBLIC_API_URL=http://localhost:8080
```
