import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany,
} from 'typeorm';

export enum CycleStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
}

export enum CycleType {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  CUSTOM = 'custom',
}

export enum CyclePeriod {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
}

@Entity('campaign_cycles')
@Index(['industryId', 'status'])
@Index(['startDate', 'endDate'])
@Index(['status', 'isActive'])
export class CampaignCycle {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 255 })
  industryId: string;

  @Column({
    type: 'enum',
    enum: CycleType,
    default: CycleType.MONTHLY,
  })
  type: CycleType;

  @Column({
    type: 'enum',
    enum: CyclePeriod,
    default: CyclePeriod.MONTHLY,
  })
  period: CyclePeriod;

  @Column({
    type: 'enum',
    enum: CycleStatus,
    default: CycleStatus.DRAFT,
  })
  status: CycleStatus;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isRecurring: boolean;

  @Column({ type: 'int', nullable: true })
  recurringCount: number; // Number of times to repeat

  @Column({ type: 'int', nullable: true })
  recurringInterval: number; // Interval between recurrences

  // Limits
  @Column({ type: 'int', nullable: true })
  maxCampaigns: number;

  @Column({ type: 'int', nullable: true })
  maxProducts: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  maxBudget: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  maxIncentiveValue: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  maxIncentivePercentage: number;

  @Column({ type: 'int', nullable: true })
  maxParticipants: number;

  // Current usage tracking
  @Column({ type: 'int', default: 0 })
  currentCampaigns: number;

  @Column({ type: 'int', default: 0 })
  currentProducts: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  currentBudgetUsed: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  currentIncentiveValue: number;

  @Column({ type: 'int', default: 0 })
  currentParticipants: number;

  // Configuration
  @Column({ type: 'json', nullable: true })
  allowedDaysOfWeek: number[]; // 0-6 (Sunday-Saturday)

  @Column({ type: 'json', nullable: true })
  excludedDates: string[]; // Array of dates in YYYY-MM-DD format

  @Column({ type: 'json', nullable: true })
  businessHours: {
    start: string; // HH:MM format
    end: string; // HH:MM format
  };

  @Column({ type: 'varchar', length: 100, default: 'America/Sao_Paulo' })
  timezone: string;

  @Column({ type: 'boolean', default: true })
  allowWeekends: boolean;

  @Column({ type: 'boolean', default: true })
  allowHolidays: boolean;

  // Approval and workflow
  @Column({ type: 'boolean', default: false })
  requiresApproval: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'text', nullable: true })
  approvalNotes: string;

  // Notifications
  @Column({ type: 'boolean', default: true })
  notifyOnLimitReached: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 80 })
  warningThresholdPercentage: number; // Warn when X% of limit is reached

  @Column({ type: 'json', nullable: true })
  notificationEmails: string[];

  // Metadata
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'varchar', length: 255, nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  updatedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  isCurrentlyActive(): boolean {
    const now = new Date();
    return this.status === CycleStatus.ACTIVE &&
           this.isActive &&
           now >= this.startDate &&
           now <= this.endDate;
  }

  getDaysRemaining(): number {
    const now = new Date();
    const diffTime = this.endDate.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getUsagePercentage(type: 'campaigns' | 'products' | 'budget' | 'incentive' | 'participants'): number {
    switch (type) {
      case 'campaigns':
        return this.maxCampaigns ? (this.currentCampaigns / this.maxCampaigns) * 100 : 0;
      case 'products':
        return this.maxProducts ? (this.currentProducts / this.maxProducts) * 100 : 0;
      case 'budget':
        return this.maxBudget ? (this.currentBudgetUsed / this.maxBudget) * 100 : 0;
      case 'incentive':
        return this.maxIncentiveValue ? (this.currentIncentiveValue / this.maxIncentiveValue) * 100 : 0;
      case 'participants':
        return this.maxParticipants ? (this.currentParticipants / this.maxParticipants) * 100 : 0;
      default:
        return 0;
    }
  }

  isLimitReached(type: 'campaigns' | 'products' | 'budget' | 'incentive' | 'participants'): boolean {
    return this.getUsagePercentage(type) >= 100;
  }

  isWarningThresholdReached(type: 'campaigns' | 'products' | 'budget' | 'incentive' | 'participants'): boolean {
    return this.getUsagePercentage(type) >= this.warningThresholdPercentage;
  }

  canAddCampaign(): boolean {
    return !this.maxCampaigns || this.currentCampaigns < this.maxCampaigns;
  }

  canAddProducts(count: number): boolean {
    return !this.maxProducts || (this.currentProducts + count) <= this.maxProducts;
  }

  canAddBudget(amount: number): boolean {
    return !this.maxBudget || (this.currentBudgetUsed + amount) <= this.maxBudget;
  }

  canAddIncentive(amount: number): boolean {
    return !this.maxIncentiveValue || (this.currentIncentiveValue + amount) <= this.maxIncentiveValue;
  }

  canAddParticipants(count: number): boolean {
    return !this.maxParticipants || (this.currentParticipants + count) <= this.maxParticipants;
  }

  getRemainingCapacity(type: 'campaigns' | 'products' | 'budget' | 'incentive' | 'participants'): number {
    switch (type) {
      case 'campaigns':
        return this.maxCampaigns ? Math.max(0, this.maxCampaigns - this.currentCampaigns) : Infinity;
      case 'products':
        return this.maxProducts ? Math.max(0, this.maxProducts - this.currentProducts) : Infinity;
      case 'budget':
        return this.maxBudget ? Math.max(0, this.maxBudget - this.currentBudgetUsed) : Infinity;
      case 'incentive':
        return this.maxIncentiveValue ? Math.max(0, this.maxIncentiveValue - this.currentIncentiveValue) : Infinity;
      case 'participants':
        return this.maxParticipants ? Math.max(0, this.maxParticipants - this.currentParticipants) : Infinity;
      default:
        return 0;
    }
  }

  isDateAllowed(date: Date): boolean {
    const dayOfWeek = date.getDay();
    
    // Check allowed days of week
    if (this.allowedDaysOfWeek && !this.allowedDaysOfWeek.includes(dayOfWeek)) {
      return false;
    }

    // Check weekends
    if (!this.allowWeekends && (dayOfWeek === 0 || dayOfWeek === 6)) {
      return false;
    }

    // Check excluded dates
    if (this.excludedDates) {
      const dateStr = date.toISOString().split('T')[0];
      if (this.excludedDates.includes(dateStr)) {
        return false;
      }
    }

    return true;
  }

  getNextRecurrenceDate(): Date | null {
    if (!this.isRecurring || !this.recurringInterval) {
      return null;
    }

    const nextDate = new Date(this.endDate);
    
    switch (this.period) {
      case CyclePeriod.DAILY:
        nextDate.setDate(nextDate.getDate() + this.recurringInterval);
        break;
      case CyclePeriod.WEEKLY:
        nextDate.setDate(nextDate.getDate() + (this.recurringInterval * 7));
        break;
      case CyclePeriod.MONTHLY:
        nextDate.setMonth(nextDate.getMonth() + this.recurringInterval);
        break;
      case CyclePeriod.QUARTERLY:
        nextDate.setMonth(nextDate.getMonth() + (this.recurringInterval * 3));
        break;
      case CyclePeriod.YEARLY:
        nextDate.setFullYear(nextDate.getFullYear() + this.recurringInterval);
        break;
    }

    return nextDate;
  }

  getStatusColor(): string {
    switch (this.status) {
      case CycleStatus.DRAFT:
        return '#6B7280'; // Gray
      case CycleStatus.ACTIVE:
        return '#10B981'; // Green
      case CycleStatus.PAUSED:
        return '#F59E0B'; // Yellow
      case CycleStatus.COMPLETED:
        return '#3B82F6'; // Blue
      case CycleStatus.CANCELLED:
        return '#EF4444'; // Red
      case CycleStatus.EXPIRED:
        return '#9CA3AF'; // Light Gray
      default:
        return '#6B7280';
    }
  }
}
