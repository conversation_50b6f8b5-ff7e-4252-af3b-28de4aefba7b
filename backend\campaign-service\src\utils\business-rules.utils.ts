/**
 * Business rules utilities for campaign management
 * Following Clean Code principles and SOLID design
 */

/**
 * Custom error class for business rule violations
 */
export class BusinessRuleError extends Error {
  public readonly code: string;

  constructor(message: string, code: string = 'BUSINESS_RULE_VIOLATION') {
    super(message);
    this.name = 'BusinessRuleError';
    this.code = code;
  }
}

/**
 * Incentive types supported by the system
 */
export type IncentiveType = 'percentage' | 'fixed';

/**
 * Business rules configuration interface
 */
export interface IncentiveRules {
  minIncentiveValue: number;
  maxIncentiveValue: number;
  allowedTypes: readonly IncentiveType[];
  maxCampaignDuration: number;
}

/**
 * Validates campaign start and end dates according to business rules
 * @param startDate - Campaign start date
 * @param endDate - Campaign end date
 * @throws BusinessRuleError if dates are invalid
 */
export function validateCampaignDates(startDate: Date, endDate: Date): void {
  if (!startDate || !endDate) {
    throw new BusinessRuleError(
      'Datas de início e fim são obrigatórias',
      'MISSING_DATES'
    );
  }

  if (!(startDate instanceof Date) || !(endDate instanceof Date)) {
    throw new BusinessRuleError(
      'Datas devem ser objetos Date válidos',
      'INVALID_DATE_TYPE'
    );
  }

  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    throw new BusinessRuleError(
      'Datas devem ser válidas',
      'INVALID_DATE_VALUE'
    );
  }

  // Start date cannot be in the past (with 1 hour tolerance)
  const now = new Date();
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
  
  if (startDate < oneHourAgo) {
    throw new BusinessRuleError(
      'Data de início não pode ser no passado',
      'START_DATE_IN_PAST'
    );
  }

  // End date must be after start date
  if (endDate <= startDate) {
    throw new BusinessRuleError(
      'Data de fim deve ser posterior à data de início',
      'END_DATE_BEFORE_START'
    );
  }

  // Campaign duration cannot exceed 1 year
  const maxDuration = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
  const duration = endDate.getTime() - startDate.getTime();
  
  if (duration > maxDuration) {
    throw new BusinessRuleError(
      'Duração da campanha não pode exceder 1 ano',
      'CAMPAIGN_TOO_LONG'
    );
  }
}

/**
 * Calculates the incentive value based on product price and incentive configuration
 * @param productPrice - Base product price
 * @param incentiveType - Type of incentive (percentage or fixed)
 * @param incentiveValue - Incentive value
 * @returns Calculated incentive amount
 */
export function calculateIncentiveValue(
  productPrice: number,
  incentiveType: IncentiveType,
  incentiveValue: number
): number {
  if (typeof productPrice !== 'number' || isNaN(productPrice) || productPrice < 0) {
    throw new BusinessRuleError(
      'Preço do produto deve ser um número válido e não negativo',
      'INVALID_PRODUCT_PRICE'
    );
  }

  if (typeof incentiveValue !== 'number' || isNaN(incentiveValue) || incentiveValue < 0) {
    throw new BusinessRuleError(
      'Valor do incentivo deve ser um número válido e não negativo',
      'INVALID_INCENTIVE_VALUE'
    );
  }

  if (incentiveValue > 1000000) {
    throw new BusinessRuleError(
      'Valor do incentivo é muito alto',
      'INCENTIVE_VALUE_TOO_HIGH'
    );
  }

  switch (incentiveType) {
    case 'percentage':
      if (incentiveValue > 100) {
        throw new BusinessRuleError(
          'Percentual de incentivo não pode exceder 100%',
          'PERCENTAGE_TOO_HIGH'
        );
      }
      return Math.round((productPrice * incentiveValue / 100) * 100) / 100;

    case 'fixed':
      return incentiveValue;

    default:
      throw new BusinessRuleError(
        `Tipo de incentivo inválido: ${incentiveType}`,
        'INVALID_INCENTIVE_TYPE'
      );
  }
}

/**
 * Validates incentive rules configuration
 * @param rules - Incentive rules to validate
 * @throws BusinessRuleError if rules are invalid
 */
export function validateIncentiveRules(rules: IncentiveRules): void {
  if (!rules) {
    throw new BusinessRuleError(
      'Regras de incentivo são obrigatórias',
      'MISSING_RULES'
    );
  }

  if (typeof rules.minIncentiveValue !== 'number' || rules.minIncentiveValue < 0) {
    throw new BusinessRuleError(
      'Valor mínimo de incentivo deve ser um número não negativo',
      'INVALID_MIN_INCENTIVE'
    );
  }

  if (typeof rules.maxIncentiveValue !== 'number' || rules.maxIncentiveValue < 0) {
    throw new BusinessRuleError(
      'Valor máximo de incentivo deve ser um número não negativo',
      'INVALID_MAX_INCENTIVE'
    );
  }

  if (rules.maxIncentiveValue < rules.minIncentiveValue) {
    throw new BusinessRuleError(
      'Valor máximo deve ser maior ou igual ao valor mínimo',
      'MAX_LESS_THAN_MIN'
    );
  }

  if (!Array.isArray(rules.allowedTypes) || rules.allowedTypes.length === 0) {
    throw new BusinessRuleError(
      'Tipos de incentivo permitidos devem ser especificados',
      'MISSING_ALLOWED_TYPES'
    );
  }

  if (typeof rules.maxCampaignDuration !== 'number' || rules.maxCampaignDuration <= 0) {
    throw new BusinessRuleError(
      'Duração máxima da campanha deve ser um número positivo',
      'INVALID_MAX_DURATION'
    );
  }
}

/**
 * Formats a number as Brazilian currency
 * @param value - Numeric value to format
 * @returns Formatted currency string
 */
export function formatCurrency(value: number): string {
  if (typeof value !== 'number' || isNaN(value)) {
    return 'R$ 0,00';
  }

  const formatter = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  return formatter.format(value);
}

/**
 * Validates if a timezone string is valid
 * @param timezone - Timezone string to validate
 * @returns true if timezone is valid
 */
export function isValidTimeZone(timezone: string): boolean {
  if (!timezone || typeof timezone !== 'string') {
    return false;
  }

  try {
    // Try to create a date with the timezone
    new Intl.DateTimeFormat('en', { timeZone: timezone });
    return true;
  } catch {
    return false;
  }
}

/**
 * Calculates the ROI (Return on Investment) for a campaign
 * @param totalInvestment - Total amount invested in the campaign
 * @param totalRevenue - Total revenue generated by the campaign
 * @returns ROI percentage
 */
export function calculateROI(totalInvestment: number, totalRevenue: number): number {
  if (typeof totalInvestment !== 'number' || totalInvestment <= 0) {
    throw new BusinessRuleError(
      'Investimento total deve ser um número positivo',
      'INVALID_INVESTMENT'
    );
  }

  if (typeof totalRevenue !== 'number' || totalRevenue < 0) {
    throw new BusinessRuleError(
      'Receita total deve ser um número não negativo',
      'INVALID_REVENUE'
    );
  }

  const roi = ((totalRevenue - totalInvestment) / totalInvestment) * 100;
  return Math.round(roi * 100) / 100; // Round to 2 decimal places
}

/**
 * Validates if a campaign budget is within acceptable limits
 * @param budget - Campaign budget to validate
 * @param minBudget - Minimum allowed budget
 * @param maxBudget - Maximum allowed budget
 * @throws BusinessRuleError if budget is invalid
 */
export function validateCampaignBudget(
  budget: number,
  minBudget: number = 100,
  maxBudget: number = 1000000
): void {
  if (typeof budget !== 'number' || isNaN(budget) || budget <= 0) {
    throw new BusinessRuleError(
      'Orçamento deve ser um número positivo',
      'INVALID_BUDGET'
    );
  }

  if (budget < minBudget) {
    throw new BusinessRuleError(
      `Orçamento mínimo é ${formatCurrency(minBudget)}`,
      'BUDGET_TOO_LOW'
    );
  }

  if (budget > maxBudget) {
    throw new BusinessRuleError(
      `Orçamento máximo é ${formatCurrency(maxBudget)}`,
      'BUDGET_TOO_HIGH'
    );
  }
}

/**
 * Checks if a campaign is currently active based on dates
 * @param startDate - Campaign start date
 * @param endDate - Campaign end date
 * @param currentDate - Current date (defaults to now)
 * @returns true if campaign is active
 */
export function isCampaignActive(
  startDate: Date,
  endDate: Date,
  currentDate: Date = new Date()
): boolean {
  return currentDate >= startDate && currentDate <= endDate;
}

/**
 * Calculates the remaining days in a campaign
 * @param endDate - Campaign end date
 * @param currentDate - Current date (defaults to now)
 * @returns Number of days remaining (0 if campaign ended)
 */
export function getRemainingDays(
  endDate: Date,
  currentDate: Date = new Date()
): number {
  const timeDiff = endDate.getTime() - currentDate.getTime();
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
  return Math.max(0, daysDiff);
}

/**
 * Business rule validation result
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validates campaign data against business rules
 * @param campaignData - Campaign data to validate
 * @param rules - Business rules to apply
 * @returns Validation result
 */
export function validateCampaignAgainstRules(
  campaignData: any,
  rules: any[]
): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  for (const rule of rules) {
    if (!rule.enabled || !rule.isActive()) {
      continue;
    }

    try {
      switch (rule.type) {
        case 'incentive_percentage':
          if (campaignData.incentivePercentage !== undefined) {
            if (!rule.validateValue(campaignData.incentivePercentage, 'percentage')) {
              errors.push(rule.getViolationMessage(campaignData.incentivePercentage, 'percentage'));
            }
          }
          break;

        case 'incentive_value':
          if (campaignData.incentiveValue !== undefined) {
            if (!rule.validateValue(campaignData.incentiveValue, 'value')) {
              errors.push(rule.getViolationMessage(campaignData.incentiveValue, 'value'));
            }
          }
          break;

        case 'products_per_cycle':
          if (campaignData.productCount !== undefined) {
            if (!rule.validateValue(campaignData.productCount, 'count')) {
              errors.push(rule.getViolationMessage(campaignData.productCount, 'count'));
            }
          }
          break;

        case 'campaign_duration':
          if (campaignData.startDate && campaignData.endDate) {
            const startDate = new Date(campaignData.startDate);
            const endDate = new Date(campaignData.endDate);
            const duration = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24));
            if (rule.durationDays && duration > rule.durationDays) {
              errors.push(`Duração da campanha (${duration} dias) excede o limite de ${rule.durationDays} dias`);
            }
          }
          break;

        case 'budget_limit':
          if (campaignData.budget !== undefined) {
            if (!rule.validateValue(campaignData.budget, 'value')) {
              errors.push(rule.getViolationMessage(campaignData.budget, 'value'));
            }
          }
          break;
      }
    } catch (error) {
      console.error(`Error validating rule ${rule.id}:`, error);
      warnings.push(`Erro ao validar regra: ${rule.name}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
