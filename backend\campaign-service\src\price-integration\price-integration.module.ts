import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

import { PriceIntegrationController } from './price-integration.controller';
import { PriceIntegrationService } from './price-integration.service';
import { PriceProviderService } from './price-provider.service';
import { PriceCacheService } from './price-cache.service';
import { PriceValidationService } from './price-validation.service';
import { ProductPrice } from '../database/entities/product-price.entity';
import { PriceHistory } from '../database/entities/price-history.entity';
import { PriceProvider } from '../database/entities/price-provider.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductPrice, PriceHistory, PriceProvider]),
    HttpModule.register({
      timeout: 30000, // 30 seconds timeout for external API calls
      maxRedirects: 3,
    }),
    ConfigModule,
  ],
  controllers: [PriceIntegrationController],
  providers: [
    PriceIntegrationService,
    PriceProviderService,
    PriceCacheService,
    PriceValidationService,
  ],
  exports: [PriceIntegrationService, PriceCacheService],
})
export class PriceIntegrationModule {}
