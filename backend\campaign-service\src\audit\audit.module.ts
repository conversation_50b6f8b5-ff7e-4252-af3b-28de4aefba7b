import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';

import { AuditController } from './audit.controller';
import { AuditService } from './audit.service';
import { AuditLoggerService } from './audit-logger.service';
import { SecurityAuditService } from './security-audit.service';
import { ComplianceAuditService } from './compliance-audit.service';

import { AuditLog } from '../database/entities/audit-log.entity';
import { SecurityEvent } from '../database/entities/security-event.entity';
import { DataAccessLog } from '../database/entities/data-access-log.entity';
import { ComplianceEvent } from '../database/entities/compliance-event.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AuditLog,
      SecurityEvent,
      DataAccessLog,
      ComplianceEvent,
    ]),
    ConfigModule,
  ],
  controllers: [AuditController],
  providers: [
    AuditService,
    AuditLoggerService,
    SecurityAuditService,
    ComplianceAuditService,
  ],
  exports: [
    AuditService,
    AuditLoggerService,
    SecurityAuditService,
    ComplianceAuditService,
  ],
})
export class AuditModule {}
