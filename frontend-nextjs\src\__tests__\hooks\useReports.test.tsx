import { renderHook, act, waitFor } from '@testing-library/react'
import { useReports } from '@/hooks/useReports'
import toast from 'react-hot-toast'

// Mock the API
jest.mock('@/lib/api', () => ({
  reportsAPI: {
    getCampaignPerformance: jest.fn(),
    exportReport: jest.fn(),
  },
}))

// Mock toast
jest.mock('react-hot-toast', () => ({
  success: jest.fn(),
  error: jest.fn(),
}))

// Store original functions
const originalCreateElement = document.createElement
const originalAppendChild = document.body.appendChild
const originalRemoveChild = document.body.removeChild

describe('useReports', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    // Restore original DOM functions after each test
    document.createElement = originalCreateElement
    document.body.appendChild = originalAppendChild
    document.body.removeChild = originalRemoveChild
  })

  it('loads initial data on mount', async () => {
    const { reportsAPI } = require('@/lib/api')
    reportsAPI.getCampaignPerformance.mockResolvedValue({
      data: {
        summary: {
          totalCampaigns: 5,
          activeCampaigns: 3,
          completedCampaigns: 2,
          totalReach: 10000,
          totalEngagement: 5000,
          totalConversions: 500,
          totalRevenue: 50000,
          averageROI: 2.5,
          averageIncentive: 10
        },
        campaigns: [],
        trends: { daily: [], weekly: [], monthly: [] },
        topPerformers: { campaigns: [], products: [], categories: [] }
      }
    })

    const { result } = renderHook(() => useReports())

    await act(async () => {
      await result.current.loadReportData(
        { campaignIds: [], status: [], search: '' },
        { type: 'preset', preset: '7d' }
      )
    })

    expect(result.current.reportData).toBeTruthy()
    expect(result.current.isLoading).toBe(false)
    expect(result.current.reportData?.summary.totalCampaigns).toBe(5)
  })

  it('filters data correctly by status', async () => {
    const { reportsAPI } = require('@/lib/api')
    reportsAPI.getCampaignPerformance.mockResolvedValue({
      data: {
        summary: {
          totalCampaigns: 2,
          activeCampaigns: 2,
          completedCampaigns: 0,
          totalReach: 5000,
          totalEngagement: 2500,
          totalConversions: 250,
          totalRevenue: 25000,
          averageROI: 2.0,
          averageIncentive: 10
        },
        campaigns: [
          { id: '1', name: 'Campaign 1', status: 'active', startDate: '2024-01-01', endDate: '2024-12-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 },
          { id: '2', name: 'Campaign 2', status: 'active', startDate: '2024-01-01', endDate: '2024-12-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 }
        ],
        trends: { daily: [], weekly: [], monthly: [] },
        topPerformers: { campaigns: [], products: [], categories: [] }
      }
    })

    const { result } = renderHook(() => useReports())

    await act(async () => {
      await result.current.loadReportData(
        { campaignIds: [], status: ['active'], search: '' },
        { type: 'preset', preset: 'all' }
      )
    })

    expect(result.current.reportData).toBeTruthy()
    expect(result.current.reportData?.campaigns.every(c => c.status === 'active')).toBe(true)
  })

  it('filters data correctly by search term', async () => {
    const { reportsAPI } = require('@/lib/api')
    reportsAPI.getCampaignPerformance.mockResolvedValue({
      data: {
        summary: {
          totalCampaigns: 1,
          activeCampaigns: 1,
          completedCampaigns: 0,
          totalReach: 2500,
          totalEngagement: 1250,
          totalConversions: 125,
          totalRevenue: 12500,
          averageROI: 2.0,
          averageIncentive: 10
        },
        campaigns: [
          { id: '1', name: 'Black Friday Campaign', status: 'active', startDate: '2024-01-01', endDate: '2024-12-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 }
        ],
        trends: { daily: [], weekly: [], monthly: [] },
        topPerformers: { campaigns: [], products: [], categories: [] }
      }
    })

    const { result } = renderHook(() => useReports())

    await act(async () => {
      await result.current.loadReportData(
        { campaignIds: [], status: [], search: 'Black Friday' },
        { type: 'preset', preset: 'all' }
      )
    })

    expect(result.current.reportData).toBeTruthy()
    expect(result.current.reportData?.campaigns.every(c => c.name.toLowerCase().includes('black friday'))).toBe(true)
  })

  it('filters data correctly by campaign IDs', async () => {
    const { reportsAPI } = require('@/lib/api')
    reportsAPI.getCampaignPerformance.mockResolvedValue({
      data: {
        summary: {
          totalCampaigns: 2,
          activeCampaigns: 2,
          completedCampaigns: 0,
          totalReach: 5000,
          totalEngagement: 2500,
          totalConversions: 250,
          totalRevenue: 25000,
          averageROI: 2.0,
          averageIncentive: 10
        },
        campaigns: [
          { id: '1', name: 'Campaign 1', status: 'active', startDate: '2024-01-01', endDate: '2024-12-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 },
          { id: '2', name: 'Campaign 2', status: 'active', startDate: '2024-01-01', endDate: '2024-12-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 }
        ],
        trends: { daily: [], weekly: [], monthly: [] },
        topPerformers: { campaigns: [], products: [], categories: [] }
      }
    })

    const { result } = renderHook(() => useReports())

    await act(async () => {
      await result.current.loadReportData(
        { campaignIds: ['1', '2'], status: [], search: '' },
        { type: 'preset', preset: 'all' }
      )
    })

    expect(result.current.reportData).toBeTruthy()
    expect(result.current.reportData?.campaigns.length).toBe(2)
    expect(result.current.reportData?.campaigns.every(c => ['1', '2'].includes(c.id))).toBe(true)
  })

  it('calculates summary metrics correctly based on filtered data', async () => {
    const { reportsAPI } = require('@/lib/api')
    reportsAPI.getCampaignPerformance.mockResolvedValue({
      data: {
        summary: {
          totalCampaigns: 3,
          activeCampaigns: 3,
          completedCampaigns: 0,
          totalReach: 7500,
          totalEngagement: 3750,
          totalConversions: 375,
          totalRevenue: 37500,
          averageROI: 2.0,
          averageIncentive: 10
        },
        campaigns: [
          { id: '1', name: 'Campaign 1', status: 'active', startDate: '2024-01-01', endDate: '2024-12-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 },
          { id: '2', name: 'Campaign 2', status: 'active', startDate: '2024-01-01', endDate: '2024-12-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 },
          { id: '3', name: 'Campaign 3', status: 'active', startDate: '2024-01-01', endDate: '2024-12-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 }
        ],
        trends: { daily: [], weekly: [], monthly: [] },
        topPerformers: { campaigns: [], products: [], categories: [] }
      }
    })

    const { result } = renderHook(() => useReports())

    await act(async () => {
      await result.current.loadReportData(
        { campaignIds: [], status: ['active'], search: '' },
        { type: 'preset', preset: 'all' }
      )
    })

    expect(result.current.reportData).toBeTruthy()

    const { summary, campaigns } = result.current.reportData!

    // Summary should match filtered campaigns
    expect(summary.totalCampaigns).toBe(3)
    expect(summary.activeCampaigns).toBe(3)
    expect(summary.totalReach).toBe(7500)
  })

  it('handles export functionality', async () => {
    const { reportsAPI } = require('@/lib/api')
    reportsAPI.exportReport.mockResolvedValue({
      data: {
        downloadUrl: '/api/v1/reports/download/test.pdf',
        filename: 'test-report.pdf'
      }
    })

    const { result } = renderHook(() => useReports())

    // Mock URL.createObjectURL and related functions AFTER renderHook
    const originalCreateElement = document.createElement
    const originalAppendChild = document.body.appendChild
    const originalRemoveChild = document.body.removeChild

    // Mock document methods
    const mockLink = {
      href: '',
      download: '',
      click: jest.fn(),
      target: '',
      style: {},
    } as any
    document.createElement = jest.fn((tag: string) => {
      if (tag === 'a') return mockLink
      return originalCreateElement.call(document, tag)
    }) as any
    document.body.appendChild = jest.fn()
    document.body.removeChild = jest.fn()

    await act(async () => {
      await result.current.exportReport(
        { campaignIds: [], status: [], search: '' },
        { type: 'preset', preset: 'all' },
        { format: 'pdf', reportType: 'campaign', includeCharts: true, includeRawData: true }
      )
    })

    expect(reportsAPI.exportReport).toHaveBeenCalled()
    expect(toast.success).toHaveBeenCalledWith('Relatório gerado: test-report.pdf')
    expect(toast.success).toHaveBeenCalledWith('Download iniciado!')
    expect(mockLink.click).toHaveBeenCalled()

    // Restore original functions
    document.createElement = originalCreateElement
    document.body.appendChild = originalAppendChild
    document.body.removeChild = originalRemoveChild
  })

  it('handles export error', async () => {
    const { reportsAPI } = require('@/lib/api')
    reportsAPI.exportReport.mockRejectedValue(new Error('Export failed'))

    const { result } = renderHook(() => useReports())

    await act(async () => {
      try {
        await result.current.exportReport(
          { campaignIds: [], status: [], search: '' },
          { type: 'preset', preset: 'all' },
          { format: 'pdf', reportType: 'campaign', includeCharts: true, includeRawData: true }
        )
      } catch (error) {
        // Expected to throw
      }
    })

    expect(toast.error).toHaveBeenCalledWith('Erro ao exportar relatório. Tente novamente.')
  })

  it('refreshes data with last filters', async () => {
    const { reportsAPI } = require('@/lib/api')
    const mockData = {
      data: {
        summary: {
          totalCampaigns: 1,
          activeCampaigns: 1,
          completedCampaigns: 0,
          totalReach: 2500,
          totalEngagement: 1250,
          totalConversions: 125,
          totalRevenue: 12500,
          averageROI: 2.0,
          averageIncentive: 10
        },
        campaigns: [
          { id: '1', name: 'Test Campaign', status: 'active', startDate: '2024-01-01', endDate: '2024-12-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 }
        ],
        trends: { daily: [], weekly: [], monthly: [] },
        topPerformers: { campaigns: [], products: [], categories: [] }
      }
    }
    reportsAPI.getCampaignPerformance.mockResolvedValue(mockData)

    const { result } = renderHook(() => useReports())

    // Load data with specific filters
    await act(async () => {
      await result.current.loadReportData(
        { campaignIds: ['1'], status: ['active'], search: 'test' },
        { type: 'preset', preset: '30d' }
      )
    })

    // Refresh data
    await act(async () => {
      result.current.refreshData()
    })

    expect(result.current.reportData).toBeTruthy()
    expect(result.current.reportData?.campaigns.length).toBe(1)
  })

  it('handles period filters correctly', async () => {
    const { reportsAPI } = require('@/lib/api')
    reportsAPI.getCampaignPerformance.mockResolvedValue({
      data: {
        summary: {
          totalCampaigns: 1,
          activeCampaigns: 1,
          completedCampaigns: 0,
          totalReach: 2500,
          totalEngagement: 1250,
          totalConversions: 125,
          totalRevenue: 12500,
          averageROI: 2.0,
          averageIncentive: 10
        },
        campaigns: [
          { id: '1', name: 'Campaign 1', status: 'active', startDate: '2024-01-01', endDate: '2024-01-31', reach: 2500, engagement: 1250, conversions: 125, revenue: 12500, roi: 2.0, incentivePercentage: 10, productsCount: 5 }
        ],
        trends: { daily: [], weekly: [], monthly: [] },
        topPerformers: { campaigns: [], products: [], categories: [] }
      }
    })

    const { result } = renderHook(() => useReports())

    await act(async () => {
      await result.current.loadReportData(
        { campaignIds: [], status: [], search: '' },
        { type: 'custom', startDate: '2024-01-01', endDate: '2024-01-31' }
      )
    })

    expect(result.current.reportData).toBeTruthy()
    expect(result.current.reportData?.summary).toBeTruthy()
  })
})
