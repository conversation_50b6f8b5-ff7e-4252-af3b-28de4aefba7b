import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

export enum SecurityEventType {
  FAILED_LOGIN = 'failed_login',
  SUCCESSFUL_LOGIN = 'successful_login',
  ACCOUNT_LOCKED = 'account_locked',
  PASSWORD_CHANGED = 'password_changed',
  PERMISSION_DENIED = 'permission_denied',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  DATA_BREACH_ATTEMPT = 'data_breach_attempt',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  MALICIOUS_REQUEST = 'malicious_request',
  SESSION_HIJACK_ATTEMPT = 'session_hijack_attempt',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  SQL_INJECTION_ATTEMPT = 'sql_injection_attempt',
  XSS_ATTEMPT = 'xss_attempt',
  CSRF_ATTEMPT = 'csrf_attempt',
  BRUTE_FORCE_ATTACK = 'brute_force_attack',
  DDoS_ATTEMPT = 'ddos_attempt',
  UNUSUAL_LOCATION = 'unusual_location',
  UNUSUAL_TIME = 'unusual_time',
  MULTIPLE_SESSIONS = 'multiple_sessions',
}

export enum SecuritySeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum SecurityStatus {
  DETECTED = 'detected',
  INVESTIGATING = 'investigating',
  CONFIRMED = 'confirmed',
  FALSE_POSITIVE = 'false_positive',
  RESOLVED = 'resolved',
  IGNORED = 'ignored',
}

@Entity('security_events')
@Index(['userId', 'timestamp'])
@Index(['type', 'timestamp'])
@Index(['severity', 'timestamp'])
@Index(['status', 'timestamp'])
@Index(['ipAddress', 'timestamp'])
@Index(['timestamp'])
@Index(['isBlocked'])
export class SecurityEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  userId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  sessionId: string;

  @Column({ type: 'enum', enum: SecurityEventType })
  type: SecurityEventType;

  @Column({ type: 'enum', enum: SecuritySeverity, default: SecuritySeverity.MEDIUM })
  severity: SecuritySeverity;

  @Column({ type: 'enum', enum: SecurityStatus, default: SecurityStatus.DETECTED })
  status: SecurityStatus;

  @Column({ type: 'varchar', length: 500 })
  description: string;

  @Column({ type: 'varchar', length: 45, nullable: true })
  @Index()
  ipAddress: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  userAgent: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  endpoint: string; // API endpoint or URL

  @Column({ type: 'varchar', length: 10, nullable: true })
  httpMethod: string;

  @Column({ type: 'int', nullable: true })
  httpStatusCode: number;

  @Column({ type: 'varchar', length: 500, nullable: true })
  referer: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  countryCode: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  city: string;

  @Column({ type: 'json', nullable: true })
  requestHeaders: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  requestBody: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  responseData: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  riskFactors: Record<string, any>; // Risk assessment data

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  riskScore: number; // 0.00 to 1.00

  @Column({ type: 'boolean', default: false })
  @Index()
  isBlocked: boolean; // Whether the request was blocked

  @Column({ type: 'boolean', default: false })
  isAutomated: boolean; // Whether this was detected by automated systems

  @Column({ type: 'varchar', length: 255, nullable: true })
  detectionRule: string; // Rule or algorithm that detected this

  @Column({ type: 'varchar', length: 255, nullable: true })
  correlationId: string; // For grouping related events

  @Column({ type: 'json', nullable: true })
  mitigationActions: string[]; // Actions taken to mitigate

  @Column({ type: 'text', nullable: true })
  investigationNotes: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  investigatedBy: string; // User who investigated

  @Column({ type: 'timestamp', nullable: true })
  investigatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  resolvedAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  @Index()
  timestamp: Date;

  // Helper methods
  static createSecurityEvent(data: {
    userId?: string;
    sessionId?: string;
    type: SecurityEventType;
    severity?: SecuritySeverity;
    description: string;
    ipAddress?: string;
    userAgent?: string;
    endpoint?: string;
    httpMethod?: string;
    httpStatusCode?: number;
    referer?: string;
    countryCode?: string;
    city?: string;
    requestHeaders?: Record<string, any>;
    requestBody?: Record<string, any>;
    responseData?: Record<string, any>;
    riskFactors?: Record<string, any>;
    riskScore?: number;
    isBlocked?: boolean;
    isAutomated?: boolean;
    detectionRule?: string;
    correlationId?: string;
    mitigationActions?: string[];
    metadata?: Record<string, any>;
  }): Partial<SecurityEvent> {
    return {
      userId: data.userId,
      sessionId: data.sessionId,
      type: data.type,
      severity: data.severity || SecuritySeverity.MEDIUM,
      status: SecurityStatus.DETECTED,
      description: data.description,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      endpoint: data.endpoint,
      httpMethod: data.httpMethod,
      httpStatusCode: data.httpStatusCode,
      referer: data.referer,
      countryCode: data.countryCode,
      city: data.city,
      requestHeaders: data.requestHeaders,
      requestBody: data.requestBody,
      responseData: data.responseData,
      riskFactors: data.riskFactors,
      riskScore: data.riskScore || 0,
      isBlocked: data.isBlocked || false,
      isAutomated: data.isAutomated !== false, // Default to true
      detectionRule: data.detectionRule,
      correlationId: data.correlationId,
      mitigationActions: data.mitigationActions,
      metadata: data.metadata,
    };
  }

  getTypeLabel(): string {
    const labels = {
      [SecurityEventType.FAILED_LOGIN]: 'Falha no Login',
      [SecurityEventType.SUCCESSFUL_LOGIN]: 'Login Bem-sucedido',
      [SecurityEventType.ACCOUNT_LOCKED]: 'Conta Bloqueada',
      [SecurityEventType.PASSWORD_CHANGED]: 'Senha Alterada',
      [SecurityEventType.PERMISSION_DENIED]: 'Permissão Negada',
      [SecurityEventType.SUSPICIOUS_ACTIVITY]: 'Atividade Suspeita',
      [SecurityEventType.DATA_BREACH_ATTEMPT]: 'Tentativa de Violação de Dados',
      [SecurityEventType.UNAUTHORIZED_ACCESS]: 'Acesso Não Autorizado',
      [SecurityEventType.RATE_LIMIT_EXCEEDED]: 'Limite de Taxa Excedido',
      [SecurityEventType.MALICIOUS_REQUEST]: 'Requisição Maliciosa',
      [SecurityEventType.SESSION_HIJACK_ATTEMPT]: 'Tentativa de Sequestro de Sessão',
      [SecurityEventType.PRIVILEGE_ESCALATION]: 'Escalação de Privilégios',
      [SecurityEventType.SQL_INJECTION_ATTEMPT]: 'Tentativa de SQL Injection',
      [SecurityEventType.XSS_ATTEMPT]: 'Tentativa de XSS',
      [SecurityEventType.CSRF_ATTEMPT]: 'Tentativa de CSRF',
      [SecurityEventType.BRUTE_FORCE_ATTACK]: 'Ataque de Força Bruta',
      [SecurityEventType.DDoS_ATTEMPT]: 'Tentativa de DDoS',
      [SecurityEventType.UNUSUAL_LOCATION]: 'Localização Incomum',
      [SecurityEventType.UNUSUAL_TIME]: 'Horário Incomum',
      [SecurityEventType.MULTIPLE_SESSIONS]: 'Múltiplas Sessões',
    };

    return labels[this.type] || this.type;
  }

  getSeverityLabel(): string {
    const labels = {
      [SecuritySeverity.LOW]: 'Baixa',
      [SecuritySeverity.MEDIUM]: 'Média',
      [SecuritySeverity.HIGH]: 'Alta',
      [SecuritySeverity.CRITICAL]: 'Crítica',
    };

    return labels[this.severity] || this.severity;
  }

  getStatusLabel(): string {
    const labels = {
      [SecurityStatus.DETECTED]: 'Detectado',
      [SecurityStatus.INVESTIGATING]: 'Investigando',
      [SecurityStatus.CONFIRMED]: 'Confirmado',
      [SecurityStatus.FALSE_POSITIVE]: 'Falso Positivo',
      [SecurityStatus.RESOLVED]: 'Resolvido',
      [SecurityStatus.IGNORED]: 'Ignorado',
    };

    return labels[this.status] || this.status;
  }

  getRiskScorePercentage(): string {
    return (this.riskScore * 100).toFixed(1) + '%';
  }

  isHighRisk(): boolean {
    return (
      this.severity === SecuritySeverity.HIGH ||
      this.severity === SecuritySeverity.CRITICAL ||
      this.riskScore >= 0.7
    );
  }

  isCritical(): boolean {
    return this.severity === SecuritySeverity.CRITICAL || this.riskScore >= 0.9;
  }

  requiresImmedateAttention(): boolean {
    const criticalTypes = [
      SecurityEventType.DATA_BREACH_ATTEMPT,
      SecurityEventType.SQL_INJECTION_ATTEMPT,
      SecurityEventType.PRIVILEGE_ESCALATION,
      SecurityEventType.SESSION_HIJACK_ATTEMPT,
    ];

    return (
      this.isCritical() ||
      criticalTypes.includes(this.type) ||
      (this.status === SecurityStatus.DETECTED && this.isHighRisk())
    );
  }

  getTimeSinceDetection(): string {
    const now = new Date();
    const diffMs = now.getTime() - this.timestamp.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} dia${diffDays > 1 ? 's' : ''} atrás`;
    } else if (diffHours > 0) {
      return `${diffHours} hora${diffHours > 1 ? 's' : ''} atrás`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minuto${diffMinutes > 1 ? 's' : ''} atrás`;
    } else {
      return 'Agora mesmo';
    }
  }
}
