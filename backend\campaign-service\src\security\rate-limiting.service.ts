import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan } from 'typeorm';

export interface RateLimitRule {
  id: string;
  name: string;
  endpoint: string;
  method: string;
  maxRequests: number;
  windowMs: number; // Time window in milliseconds
  userType?: 'admin' | 'editor' | 'reader' | 'all';
  industryId?: string;
  isActive: boolean;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: string; // Custom key generation logic
  message?: string;
  headers?: boolean; // Include rate limit headers in response
  createdAt: Date;
  updatedAt: Date;
}

export interface RateLimitAttempt {
  id: string;
  ruleId: string;
  key: string; // Unique identifier (IP, user ID, etc.)
  endpoint: string;
  method: string;
  userId?: string;
  userType?: string;
  industryId?: string;
  ip: string;
  userAgent?: string;
  timestamp: Date;
  success: boolean;
  statusCode: number;
  responseTime: number;
  blocked: boolean;
  reason?: string;
  metadata?: Record<string, any>;
}

export interface SecurityEvent {
  id: string;
  type: 'rate_limit_exceeded' | 'suspicious_activity' | 'brute_force' | 'ddos_attempt' | 'unauthorized_access';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string; // IP address or user ID
  endpoint: string;
  method: string;
  userId?: string;
  userType?: string;
  industryId?: string;
  description: string;
  metadata: Record<string, any>;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
  actions: string[]; // Actions taken (block, alert, etc.)
}

@Injectable()
export class RateLimitingService {
  private readonly logger = new Logger(RateLimitingService.name);
  private readonly cache = new Map<string, { count: number; resetTime: number }>();
  private readonly suspiciousIPs = new Set<string>();
  private readonly blockedIPs = new Set<string>();

  // Default rate limiting rules
  private readonly defaultRules: Omit<RateLimitRule, 'id' | 'createdAt' | 'updatedAt'>[] = [
    {
      name: 'General API Rate Limit',
      endpoint: '/api/v1/*',
      method: '*',
      maxRequests: 1000, // Increased for development
      windowMs: 60000, // 1 minute
      userType: 'all',
      isActive: true,
      headers: true,
      message: 'Muitas requisições. Tente novamente em alguns minutos.',
    },
    {
      name: 'Authentication Rate Limit',
      endpoint: '/api/v1/auth/*',
      method: 'POST',
      maxRequests: 5,
      windowMs: 300000, // 5 minutes
      userType: 'all',
      isActive: true,
      headers: true,
      message: 'Muitas tentativas de autenticação. Aguarde 5 minutos.',
    },
    {
      name: 'Campaign Creation Rate Limit',
      endpoint: '/api/v1/campaigns',
      method: 'POST',
      maxRequests: 10,
      windowMs: 3600000, // 1 hour
      userType: 'editor',
      isActive: true,
      headers: true,
      message: 'Limite de criação de campanhas excedido. Aguarde 1 hora.',
    },
    {
      name: 'AI Estimator Rate Limit',
      endpoint: '/api/v1/ai-estimator/*',
      method: '*',
      maxRequests: 20,
      windowMs: 60000, // 1 minute
      userType: 'all',
      isActive: true,
      headers: true,
      message: 'Limite de estimativas de IA excedido. Aguarde 1 minuto.',
    },
    {
      name: 'Bulk Operations Rate Limit',
      endpoint: '/api/v1/*/bulk',
      method: 'POST',
      maxRequests: 3,
      windowMs: 300000, // 5 minutes
      userType: 'all',
      isActive: true,
      headers: true,
      message: 'Limite de operações em lote excedido. Aguarde 5 minutos.',
    },
  ];

  constructor() {
    this.initializeDefaultRules();
    this.startCleanupInterval();
  }

  async checkRateLimit(
    endpoint: string,
    method: string,
    key: string,
    userType?: string,
    industryId?: string,
    ip?: string,
  ): Promise<{
    allowed: boolean;
    rule?: RateLimitRule;
    remaining?: number;
    resetTime?: number;
    retryAfter?: number;
  }> {
    // DISABLED FOR DEVELOPMENT - Always allow
    return { allowed: true };

    // Skip rate limiting for localhost in development
    if (ip && (ip === '127.0.0.1' || ip === '::1' || ip === 'localhost' || ip.startsWith('172.'))) {
      return { allowed: true };
    }

    // Check if IP is blocked
    if (ip && this.blockedIPs.has(ip)) {
      this.logger.warn(`Blocked IP attempted access: ${ip}`);
      return { allowed: false };
    }

    // Find matching rule
    const rule = this.findMatchingRule(endpoint, method, userType);
    if (!rule || !rule.isActive) {
      return { allowed: true };
    }

    const cacheKey = this.generateCacheKey(rule, key, userType, industryId);
    const now = Date.now();
    const cached = this.cache.get(cacheKey);

    if (!cached || now > cached.resetTime) {
      // Reset or initialize counter
      this.cache.set(cacheKey, {
        count: 1,
        resetTime: now + rule.windowMs,
      });
      return {
        allowed: true,
        rule,
        remaining: rule.maxRequests - 1,
        resetTime: now + rule.windowMs,
      };
    }

    if (cached.count >= rule.maxRequests) {
      // Rate limit exceeded
      const retryAfter = Math.ceil((cached.resetTime - now) / 1000);
      
      // Log security event
      await this.logSecurityEvent({
        type: 'rate_limit_exceeded',
        severity: 'medium',
        source: ip || key,
        endpoint,
        method,
        userType,
        industryId,
        description: `Rate limit exceeded for rule: ${rule.name}`,
        metadata: {
          rule: rule.name,
          maxRequests: rule.maxRequests,
          windowMs: rule.windowMs,
          currentCount: cached.count,
        },
      });

      // Check for suspicious activity
      await this.checkSuspiciousActivity(ip || key, endpoint, method);

      return {
        allowed: false,
        rule,
        remaining: 0,
        resetTime: cached.resetTime,
        retryAfter,
      };
    }

    // Increment counter
    cached.count++;
    this.cache.set(cacheKey, cached);

    return {
      allowed: true,
      rule,
      remaining: rule.maxRequests - cached.count,
      resetTime: cached.resetTime,
    };
  }

  async logAttempt(attempt: Omit<RateLimitAttempt, 'id' | 'timestamp'>): Promise<void> {
    // In a real implementation, this would save to database
    this.logger.log(`Rate limit attempt: ${attempt.method} ${attempt.endpoint} - ${attempt.success ? 'SUCCESS' : 'FAILED'}`);
  }

  async logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp' | 'resolved' | 'actions'>): Promise<void> {
    const securityEvent: SecurityEvent = {
      ...event,
      id: this.generateId(),
      timestamp: new Date(),
      resolved: false,
      actions: [],
    };

    // In a real implementation, this would save to database and trigger alerts
    this.logger.warn(`Security Event: ${event.type} - ${event.description}`);

    // Auto-resolve low severity events
    if (event.severity === 'low') {
      securityEvent.resolved = true;
      securityEvent.resolvedAt = new Date();
      securityEvent.actions.push('auto_resolved');
    }

    // Take automatic actions for high/critical events
    if (event.severity === 'high' || event.severity === 'critical') {
      await this.takeSecurityAction(securityEvent);
    }
  }

  async blockIP(ip: string, reason: string, duration?: number): Promise<void> {
    this.blockedIPs.add(ip);
    
    this.logger.warn(`IP blocked: ${ip} - Reason: ${reason}`);

    await this.logSecurityEvent({
      type: 'unauthorized_access',
      severity: 'high',
      source: ip,
      endpoint: '*',
      method: '*',
      description: `IP blocked: ${reason}`,
      metadata: { duration, reason },
    });

    // Auto-unblock after duration
    if (duration) {
      setTimeout(() => {
        this.blockedIPs.delete(ip);
        this.logger.log(`IP unblocked: ${ip}`);
      }, duration);
    }
  }

  async unblockIP(ip: string): Promise<void> {
    this.blockedIPs.delete(ip);
    this.suspiciousIPs.delete(ip);
    this.logger.log(`IP unblocked manually: ${ip}`);
  }

  getSecurityStatus(): {
    blockedIPs: string[];
    suspiciousIPs: string[];
    activeRules: number;
    totalAttempts: number;
    recentEvents: number;
  } {
    return {
      blockedIPs: Array.from(this.blockedIPs),
      suspiciousIPs: Array.from(this.suspiciousIPs),
      activeRules: this.defaultRules.filter(r => r.isActive).length,
      totalAttempts: this.cache.size,
      recentEvents: 0, // Would be calculated from database
    };
  }

  private findMatchingRule(endpoint: string, method: string, userType?: string): RateLimitRule | null {
    for (const rule of this.defaultRules) {
      if (!rule.isActive) continue;

      // Check method match
      if (rule.method !== '*' && rule.method !== method) continue;

      // Check user type match
      if (rule.userType && rule.userType !== 'all' && rule.userType !== userType) continue;

      // Check endpoint match (simple pattern matching)
      if (this.matchesPattern(endpoint, rule.endpoint)) {
        return {
          ...rule,
          id: this.generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      }
    }

    return null;
  }

  private matchesPattern(endpoint: string, pattern: string): boolean {
    if (pattern === '*') return true;
    if (pattern.endsWith('/*')) {
      const prefix = pattern.slice(0, -2);
      return endpoint.startsWith(prefix);
    }
    return endpoint === pattern;
  }

  private generateCacheKey(rule: RateLimitRule, key: string, userType?: string, industryId?: string): string {
    const parts = [rule.id, key];
    if (userType) parts.push(userType);
    if (industryId) parts.push(industryId);
    return parts.join(':');
  }

  private async checkSuspiciousActivity(source: string, endpoint: string, method: string): Promise<void> {
    // Simple suspicious activity detection
    const recentAttempts = Array.from(this.cache.entries())
      .filter(([key]) => key.includes(source))
      .reduce((sum, [, value]) => sum + value.count, 0);

    if (recentAttempts > 50) {
      this.suspiciousIPs.add(source);
      
      await this.logSecurityEvent({
        type: 'suspicious_activity',
        severity: 'medium',
        source,
        endpoint,
        method,
        description: `Suspicious activity detected: ${recentAttempts} requests`,
        metadata: { recentAttempts },
      });

      // Auto-block if very suspicious
      if (recentAttempts > 100) {
        await this.blockIP(source, 'Excessive requests detected', 3600000); // 1 hour
      }
    }
  }

  private async takeSecurityAction(event: SecurityEvent): Promise<void> {
    const actions: string[] = [];

    switch (event.type) {
      case 'rate_limit_exceeded':
        if (event.severity === 'high') {
          await this.blockIP(event.source, 'Repeated rate limit violations', 1800000); // 30 minutes
          actions.push('ip_blocked_30min');
        }
        break;

      case 'brute_force':
        await this.blockIP(event.source, 'Brute force attack detected', 3600000); // 1 hour
        actions.push('ip_blocked_1hour');
        break;

      case 'ddos_attempt':
        await this.blockIP(event.source, 'DDoS attempt detected', 7200000); // 2 hours
        actions.push('ip_blocked_2hours');
        break;
    }

    event.actions = actions;
  }

  private initializeDefaultRules(): void {
    this.logger.log(`Initialized ${this.defaultRules.length} default rate limiting rules`);
  }

  private startCleanupInterval(): void {
    // Clean up expired cache entries every 5 minutes
    setInterval(() => {
      const now = Date.now();
      for (const [key, value] of this.cache.entries()) {
        if (now > value.resetTime) {
          this.cache.delete(key);
        }
      }
    }, 300000); // 5 minutes
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
