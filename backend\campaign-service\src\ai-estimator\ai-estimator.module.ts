import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AIEstimatorService } from './ai-estimator.service';
import { AIEstimatorController } from './ai-estimator.controller';
import { RateLimiterService } from './services/rate-limiter.service';
import { AnomalyDetectorService } from './services/anomaly-detector.service';
import { EstimationCalculatorService } from './services/estimation-calculator.service';
import { Product } from '../database/entities/product.entity';
import { ProductCategory } from '../database/entities/product-category.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Product, ProductCategory])],
  controllers: [AIEstimatorController],
  providers: [
    AIEstimatorService,
    RateLimiterService,
    AnomalyDetectorService,
    EstimationCalculatorService,
  ],
  exports: [
    AIEstimatorService,
    RateLimiterService,
    AnomalyDetectorService,
    EstimationCalculatorService,
  ],
})
export class AIEstimatorModule {}
