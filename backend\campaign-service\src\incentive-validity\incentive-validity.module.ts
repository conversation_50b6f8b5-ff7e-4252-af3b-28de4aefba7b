import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IncentiveValidityService } from './incentive-validity.service';
import { IncentiveValidityController } from './incentive-validity.controller';
import { IncentiveValidityConfig } from '../database/entities/incentive-validity.entity';

@Module({
  imports: [TypeOrmModule.forFeature([IncentiveValidityConfig])],
  controllers: [IncentiveValidityController],
  providers: [IncentiveValidityService],
  exports: [IncentiveValidityService],
})
export class IncentiveValidityModule {}
